﻿using InnoBook.DTO;
using InnoBook.DTO.Client;
using InnoBook.DTO.CoreModel;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Extension;
using InnoBook.Request.Client;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using System;

namespace InnoBook.Services.Classes
{
    public class ClientService(InnoLogicielContext context, IEncryptionService encryptionService) : IClientService
    {
        public async Task<bool> DeleteClient(List<Guid?> listClient)
        {
            try
            {
                var listItemDetail = context.Clients.Where(x => listClient.Contains(x.Id)).ToList();
                if (listItemDetail.Count() > 0)
                {
                    foreach (var item in listItemDetail)
                    {
                        var dt = context.Clients.FirstOrDefault(x => x.Id == item.Id);
                        dt.isActive = false;
                    }
                    await context.SaveChangesAsync();
                    return true;

                }
                return false;
            }
            catch
            {
                return false;
            }


        }

        public async Task<PaginatedResponse<RequestAllClient>> GetAllClientAsync(GetClientRequestParam query,string companyId)
        {
            var data = context.Clients
                .Include(c => c.Company)
                .Include(c=> c.Projects)
                    .ThenInclude(c => c.Members)
                .Where(c => c.CompanyId.ToString() == companyId && c.isActive);
           
            if (!string.IsNullOrEmpty(query.Search))
            {
                data = data.Where(c => c.ClientName.ToLower().Contains(query.Search.ToLower()));
            }

            if (query.Role != UserBusinessRole.Admin)
            {
                data = data.Where(c => c.CreatedBy == query.UserId ||
                                       c.Projects.Any(p => p.isActive && !p.isArchive && p.Members.Select(m => m.UserId).Contains(Guid.Parse(query.UserId))));
            }

            var totalRecords = await data.CountAsync();

            var panigationQuery = data.Select(c => new RequestAllClient
                             {
                                 id = c.Id,
                                 emailAddress=c.EmailAddress,
                                 clientName = c.ClientName,
                                 companyName = c.Company.BusinessName,
                                 totalOutstanding = c.Invoices.Where(i => i.DueDate > DateTime.UtcNow).Sum(i => i.TotalAmount)
                             })
                             .OrderBy(c => c.clientName)
                             .AsQueryable();

            if (query.Direction != null && query.ColumnName != null)
            {
                panigationQuery = SortExtensions.DynamicSort(panigationQuery, query.ColumnName, query.Direction != "Ascending");
            }

            if (query.Page > 0 && query.PageSize > 0)
            {
                panigationQuery = panigationQuery.Skip((query.Page - 1) * query.PageSize)
                               .Take(query.PageSize);
            }

            var result = await panigationQuery.ToListAsync();

            return new PaginatedResponse<RequestAllClient>
            {
                Data = result,
                Page = query.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)query.PageSize),
                PageSize = query.PageSize,
                TotalRecords = totalRecords
            };
        }

        public async Task<bool> Insert(RequestClient client, string UserId,string CompanyId)
        {
            var check_ = await context.Clients.FirstOrDefaultAsync(x => x.ClientName.Equals(client.ClientName));
            if(check_==null)
            {
                client.isActive = true;
                var newClient = new Client
                {
                    Id = Guid.NewGuid(),
                    ClientName = client.ClientName,
                    CompanyId = Guid.Parse(CompanyId),
                    FirstName = client.FirstName,
                    LastName = client.LastName,
                    EmailAddress = client.EmailAddress,
                    PhoneNumber = client.PhoneNumber,
                    BusinessPhoneNumber = client.BusinessPhoneNumber,
                    MobilePhoneNumber = client.MobilePhoneNumber,
                    PostePhoneNumber = client.PostePhoneNumber,
                    PosteBusinessPhoneNumber = client.PosteBusinessPhoneNumber,
                    PosteMobilePhoneNumber = client.PosteMobilePhoneNumber,
                    Country = client.Country,
                    AddressLine1 = client.AddressLine1,
                    AddressLine2 = client.AddressLine2,
                    TownCity = client.TownCity,
                    StateProvince = client.StateProvince,
                    PostalCode = client.PostalCode,
                    CreatedBy = UserId,
                    CreatedAt = DateTime.UtcNow
                };

                // Encrypt sensitive client data before saving
                newClient = await encryptionService.EncryptClientAsync(newClient);

                context.Clients.Add(newClient);
                await context.SaveChangesAsync();
                return true;
            }
            return false;
        }

        public async Task<bool> Update(RequestClient model, string UserId)
        {
            try
            {
                var client = await context.Clients.FirstOrDefaultAsync(x => x.Id == model.Id);

                client.UpdatedBy = UserId;
                client.UpdatedAt = DateTime.UtcNow;
                client.ClientName = model.ClientName;
                client.FirstName = model.FirstName;
                client.LastName = model.LastName;
                client.EmailAddress = model.EmailAddress;
                client.PhoneNumber = model.PhoneNumber;
                client.BusinessPhoneNumber = model.BusinessPhoneNumber;
                client.MobilePhoneNumber = model.MobilePhoneNumber;
                client.PostePhoneNumber = model.PostePhoneNumber;
                client.PosteBusinessPhoneNumber = model.PosteBusinessPhoneNumber;
                client.PosteMobilePhoneNumber = model.PosteMobilePhoneNumber;
                client.Country = model.Country;
                client.AddressLine1 = model.AddressLine1;
                client.AddressLine2 = model.AddressLine2;
                client.TownCity = model.TownCity;
                client.StateProvince = model.StateProvince;
                client.PostalCode = model.PostalCode;

                // Encrypt sensitive client data before saving
                client = await encryptionService.EncryptClientAsync(client);

                context.Clients.Update(client);
                await context.SaveChangesAsync();
                return true;
            }
            catch
            {
                throw;
            }
        }

        public List<GetClientTimeTracking> ClientTimeTracking(Guid companyId)
        {
            var query = from client in context.Clients
                        where client.CompanyId == companyId && client.isActive == true
                        join time in context.TimeTrackings
                        on client.Id equals time.ClientId into timeGroup
                        from time in timeGroup.DefaultIfEmpty() // Left join
                        group time by new { client.Id, client.ClientName } into grouped
                        select new GetClientTimeTracking
                        {
                            ClientId= grouped.Key.Id,
                            ClientName= grouped.Key.ClientName,
                            TimeTrackings = grouped
                            .Where(tt => tt != null)
                            .Select(tt => new TimeTracking
                            {
                                Id = tt.Id, 
                                StartTime = tt.StartTime,
                                Date= tt.Date,
                                EndTime = tt.EndTime,
                                Description = tt.Description
                            }).ToList()

                        };

            var result = query.ToList();
            return result;
          
        }

        public async Task<CalculationClient> CalculationClient( string companyId)
        {
            decimal TotalOverdue = 0;
            decimal TotalDraft = 0;
            decimal TotalAmount = 0;
            var client = context.Clients.Where(x => x.CompanyId.ToString() == companyId&&x.isActive==true).ToList();
            var invoices = await context.Invoices
            .Where(t => client.Select(p => p.Id).ToList().Contains((Guid)t.ClientId))
            .ToListAsync();


            foreach (var item in invoices)
            {
                if (item.Status == (int)InvoiceStatus.DRAFT)
                {
                    TotalDraft += item.TotalAmount;
                }
                if (item.DueDate< DateTime.UtcNow)
                {
                    TotalOverdue += item.TotalAmount;
                } 
                else
                {
                    TotalAmount += item.TotalAmount;
                }    
            }



            return new CalculationClient
            {
                TotalOverdue = TotalOverdue,
                TotalAmount = TotalAmount,
                TotalDraft = TotalDraft,

            };

        }

        public async Task<GetClientDTO> GetClientById(string Id)
        {
            var client = await context.Clients
                .AsNoTracking()
                .Where(c => c.Id.ToString() == Id && c.isActive == true)
                .FirstOrDefaultAsync();

            if (client == null)
                return null;

            // Decrypt sensitive client data before returning
            client = await encryptionService.DecryptClientAsync(client);

            var data = new GetClientDTO
            {
                CompanyId = client.CompanyId,
                ClientName = client.ClientName,
                Id = client.Id,
                FirstName = client.FirstName,
                LastName = client.LastName,
                EmailAddress = client.EmailAddress,
                PhoneNumber = client.PhoneNumber,
                BusinessPhoneNumber = client.BusinessPhoneNumber,
                MobilePhoneNumber = client.MobilePhoneNumber,
                PostePhoneNumber = client.PostePhoneNumber,
                PosteBusinessPhoneNumber = client.PosteBusinessPhoneNumber,
                PosteMobilePhoneNumber = client.PosteMobilePhoneNumber,
                Country = client.Country,
                IsInternal = client.IsInternal,
                AddressLine1 = client.AddressLine1,
                AddressLine2 = client.AddressLine2,
                TownCity = client.TownCity,
                isActive = client.isActive,
                StateProvince = client.StateProvince,
                PostalCode = client.PostalCode
            };

            return data;
        }
    }
}
