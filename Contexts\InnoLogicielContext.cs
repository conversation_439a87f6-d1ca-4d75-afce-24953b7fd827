﻿using Amazon.Runtime.Internal.Transform;
using InnoBook.Entities;
using InnoBook.Funtion;
using InnoBook.Services.Classes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace InnoLogiciel.Server.Contexts
{
    public class InnoLogicielContext(DbContextOptions<InnoLogicielContext> options) : DbContext(options)
    {
        public DbSet<User> Users { get; set; }
        public DbSet<Company> Companies { get; set; }
        public DbSet<UserBusiness> UserBusinesses { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<Client> Clients { get; set; }
        public DbSet<Project> Projects { get; set; }
        public DbSet<Service> Services { get; set; }
        public DbSet<Member> Members { get; set; }
        public DbSet<TimeTracking> TimeTrackings { get; set; }
        public DbSet<Invoice> Invoices { get; set; }
        public DbSet<Tax> Taxs { get; set; }
        public DbSet<Payment> Payment { get; set; }
        public DbSet<Category> Categorys { get; set; }
        public DbSet<Item> Items { get; set; }
        public DbSet<CompanyTax> CompanyTax { get; set; }
        public DbSet<CategoryItem> CategoryItem { get; set; }
        public DbSet<Expenses> Expenses { get; set; }
        public DbSet<Merchant> Merchant { get; set; }
        public DbSet<ItemInvoice> ItemInvoice { get; set; }
        public DbSet<ItemExpense> ItemExpense { get; set; }
        public DbSet<InvoiceSend> InvoiceSend { get; set; }

        public DbSet<Attachment> Attachment { get; set; }
        public DbSet<Plan> Plans { get; set; }
        public DbSet<CompanyPlan> CompanyPlans { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDbFunction(typeof(PgCryptoFunctions).GetMethod(nameof(PgCryptoFunctions.PgpSymEncrypt)));
            modelBuilder.HasDbFunction(typeof(PgCryptoFunctions).GetMethod(nameof(PgCryptoFunctions.PgpSymDecrypt)));
            modelBuilder.HasDbFunction(typeof(PgCryptoFunctions).GetMethod(nameof(PgCryptoFunctions.Decode)));


            base.OnModelCreating(modelBuilder);
            // Configure entities
            modelBuilder.Entity<User>(entity =>
            {
                // GUID primary key
                entity.HasKey(e => e.Id);

                // Generate new GUID on add
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                // Unique constraints
                entity.HasIndex(e => e.Email).IsUnique();
                entity.HasIndex(e => e.Username).IsUnique();

                entity.HasMany(c => c.UserBusinesses)
                  .WithOne(c => c.User)
                  .HasForeignKey(ub => ub.UserId)
                  .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<UserBusiness>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(c => c.Company)
                .WithMany(c => c.UserBusinesses)
                .HasForeignKey(c => c.CompanyId)
                .OnDelete(DeleteBehavior.Cascade);
                ;
            });


            modelBuilder.Entity<Company>(entity =>
            {
                entity.Property(e => e.Id)
                .HasDefaultValueSql("gen_random_uuid()")
                .ValueGeneratedOnAdd();
            });



            modelBuilder.Entity<Role>().Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

            modelBuilder.Entity<Permission>().Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

            modelBuilder.Entity<RolePermission>().HasKey(e => new { e.RoleId, e.PermissionId });

            modelBuilder.Entity<Client>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(c => c.Company)
                      .WithMany(c => c.Clients)
                      .HasForeignKey(c => c.CompanyId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(c => c.Projects)
                      .WithOne(p => p.Client)
                      .HasForeignKey(p => p.ClientId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(c => c.TimeTrackings)
                      .WithOne(t => t.Client)
                      .HasForeignKey(t => t.ClientId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<Project>(entity =>
            {

                entity.Property(e => e.Id)
                        .HasDefaultValueSql("gen_random_uuid()")
                        .ValueGeneratedOnAdd();

                entity.HasOne(p => p.Client)
                      .WithMany(c => c.Projects)
                      .HasForeignKey(p => p.ClientId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Company)
                      .WithMany(p => p.Projects)
                      .HasForeignKey(p => p.CompanyId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(p => p.Members)
                      .WithOne(m => m.Project)
                      .HasForeignKey(m => m.ProjectId)
                      .OnDelete(DeleteBehavior.Cascade);


                entity.HasMany(p => p.Invoices)
                      .WithOne(i => i.Project)
                      .HasForeignKey(i => i.ProjectId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(p => p.TimeTrackings)
                      .WithOne(t => t.Project)
                      .HasForeignKey(t => t.ProjectId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(p => p.Services)
                       .WithMany(s => s.Projects)
                       .UsingEntity(j => j.ToTable("projectService"));
            });

            modelBuilder.Entity<Service>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasMany(s => s.TimeTrackings)
                      .WithOne(t => t.Service)
                      .HasForeignKey(t => t.ServiceId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<Member>(entity =>
            {

                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(m => m.User)
                      .WithMany(m => m.Members)
                      .HasForeignKey(m => m.UserId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(m => m.Project)
                      .WithMany(p => p.Members)
                      .HasForeignKey(m => m.ProjectId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(m => m.Company)
                      .WithMany(m => m.Members)
                      .HasForeignKey(m => m.CompanyId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(m => m.TimeTrackings)
                      .WithOne(t => t.Member)
                      .HasForeignKey(t => t.MemberId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<TimeTracking>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(t => t.Member)
                      .WithMany(m => m.TimeTrackings)
                      .HasForeignKey(t => t.MemberId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(t => t.Company)
                      .WithMany()
                      .HasForeignKey(t => t.CompanyId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(t => t.Project)
                      .WithMany(p => p.TimeTrackings)
                      .HasForeignKey(t => t.ProjectId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(t => t.Service)
                      .WithMany(s => s.TimeTrackings)
                      .HasForeignKey(t => t.ServiceId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(t => t.Client)
                      .WithMany(c => c.TimeTrackings)
                      .HasForeignKey(t => t.ClientId)
                      .OnDelete(DeleteBehavior.Restrict);

            });

            modelBuilder.Entity<Invoice>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(i => i.Company)
                      .WithMany(i => i.Invoices)
                      .HasForeignKey(i => i.CompanyId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(i => i.Client)
                      .WithMany(c => c.Invoices)
                      .HasForeignKey(i => i.ClientId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(i => i.Project)
                      .WithMany(p => p.Invoices)
                      .HasForeignKey(i => i.ProjectId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(i => i.Payments)
                      .WithOne(p => p.Invoice)
                      .HasForeignKey(p => p.InvoiceId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(i => i.ItemInvoices)
                      .WithOne(pi => pi.Invoice)
                      .HasForeignKey(pi => pi.InvoiceId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<Category>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasMany(c => c.CategoryItems)
                      .WithOne(ci => ci.Category)
                      .HasForeignKey(ci => ci.CategoryId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(c => c.Company)
                    .WithMany(ci => ci.Categorys)
                    .HasForeignKey(ci => ci.CompanyId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.Number).IsUnique();
            });

            modelBuilder.Entity<CategoryItem>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(ci => ci.Category)
                      .WithMany(c => c.CategoryItems)
                      .HasForeignKey(ci => ci.CategoryId)
                      .OnDelete(DeleteBehavior.Restrict);


                entity.HasOne(c => c.Company)
                    .WithMany(ci => ci.CategoryItems)
                    .HasForeignKey(ci => ci.CompanyId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasIndex(e => e.Number).IsUnique();

            });

            modelBuilder.Entity<CompanyTax>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(t => t.Company)
                       .WithMany(i => i.CompanyTaxs)
                       .HasForeignKey(t => t.CompanyId)
                       .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(t => t.Taxes)
                      .WithOne(pi => pi.CompanyTax)
                      .HasForeignKey(t => t.CompanyTaxId)
                      .OnDelete(DeleteBehavior.Restrict);
            });
            

            modelBuilder.Entity<Tax>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(t => t.CompanyTax)
                       .WithMany(i => i.Taxes)
                       .HasForeignKey(t => t.CompanyTaxId)
                       .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(t => t.ItemInvoice)
                      .WithMany(pi => pi.Taxes)
                      .HasForeignKey(t => t.ItemInvoiceId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(t => t.ItemExpense)
                       .WithMany(pi => pi.Taxes)
                       .HasForeignKey(t => t.ItemExpenseId)
                       .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<Payment>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(p => p.Invoice)
                      .WithMany(i => i.Payments)
                      .HasForeignKey(p => p.InvoiceId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(p => p.Company)
                      .WithMany(p => p.Payments)
                      .HasForeignKey(p => p.CompanyId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<Expenses>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(e => e.Company)
                      .WithMany(e => e.Expenses)
                      .HasForeignKey(e => e.CompanyId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Client)
                      .WithMany(e => e.Expenses)
                      .HasForeignKey(e => e.ClientId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Project)
                      .WithMany(e => e.Expenses)
                      .HasForeignKey(e => e.ProjectId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Category)
                      .WithMany(e => e.Expenses)
                      .HasForeignKey(e => e.CategoryId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.CategoryItem)
                      .WithMany(e => e.Expenses)
                      .HasForeignKey(e => e.CategoryItemId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.Merchant)
                      .WithMany(e => e.Expenses)
                      .HasForeignKey(e => e.MerchantId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(e => e.Attachments)
                      .WithOne(a => a.Expenses)
                      .HasForeignKey(a => a.ExpensesId)
                      .OnDelete(DeleteBehavior.Cascade);

                entity.HasMany(e => e.ItemExpense)
                      .WithOne(pe => pe.Expenses)
                      .HasForeignKey(pe => pe.ExpensesId)
                      .OnDelete(DeleteBehavior.Cascade);

            });

            modelBuilder.Entity<Merchant>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(m => m.Company)
                      .WithMany(m => m.Merchants)
                      .HasForeignKey(m => m.CompanyId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(m => m.Expenses)
                      .WithOne(e => e.Merchant)
                      .HasForeignKey(e => e.MerchantId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<ItemInvoice>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(pi => pi.Invoice)
                      .WithMany(i => i.ItemInvoices)
                      .HasForeignKey(pi => pi.InvoiceId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(pi => pi.Taxes)
                      .WithOne(t => t.ItemInvoice)
                      .HasForeignKey(t => t.ItemInvoiceId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<ItemExpense>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(pi => pi.Expenses)
                      .WithMany(i => i.ItemExpense)
                      .HasForeignKey(pi => pi.ExpensesId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasMany(pi => pi.Taxes)
                      .WithOne(t => t.ItemExpense)
                      .HasForeignKey(t => t.ItemExpenseId)
                      .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<Attachment>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(pi => pi.Expenses)
                      .WithMany(i => i.Attachments)
                      .HasForeignKey(pi => pi.ExpensesId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<Plan>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasMany(p => p.CompanyPlans)
                      .WithOne(cp => cp.Plan)
                      .HasForeignKey(cp => cp.PlanId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            modelBuilder.Entity<CompanyPlan>(entity =>
            {
                entity.Property(e => e.Id)
                    .HasDefaultValueSql("gen_random_uuid()")
                    .ValueGeneratedOnAdd();

                entity.HasOne(cp => cp.Company)
                      .WithMany()
                      .HasForeignKey(cp => cp.CompanyId)
                      .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(cp => cp.Plan)
                      .WithMany(p => p.CompanyPlans)
                      .HasForeignKey(cp => cp.PlanId)
                      .OnDelete(DeleteBehavior.Restrict);
            });

            var category1Id = new Guid("10000000-0000-0000-0000-000000000001");
            var category2Id = new Guid("10000000-0000-0000-0000-000000000002");
            var category3Id = new Guid("10000000-0000-0000-0000-000000000003");
            var category4Id = new Guid("10000000-0000-0000-0000-000000000004");
            var category5Id = new Guid("10000000-0000-0000-0000-000000000005");
            var category6Id = new Guid("10000000-0000-0000-0000-000000000006");
            var category7Id = new Guid("10000000-0000-0000-0000-000000000007");
            var category8Id = new Guid("10000000-0000-0000-0000-000000000008");
            var category9Id = new Guid("10000000-0000-0000-0000-000000000009");
            var category10Id = new Guid("10000000-0000-0000-0000-000000000010");
            var category11Id = new Guid("10000000-0000-0000-0000-000000000011");
            var category12Id = new Guid("10000000-0000-0000-0000-000000000012");
            var category13Id = new Guid("10000000-0000-0000-0000-000000000013");
            var category14Id = new Guid("10000000-0000-0000-0000-000000000014");
            var category15Id = new Guid("10000000-0000-0000-0000-000000000015");
            var category16Id = new Guid("10000000-0000-0000-0000-000000000016");

            var createdAt = DateTime.SpecifyKind(new DateTime(2025, 2, 1), DateTimeKind.Utc);
            var updatedAt = DateTime.SpecifyKind(new DateTime(2025, 2, 1), DateTimeKind.Utc);


            var categories = new List<Category>
    {
        new Category { Id = category1Id, CategoryName = "Advertising", Number = 1, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null },
        new Category { Id = category2Id, CategoryName = "Car and Truck Expenses", Number = 2, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category3Id, CategoryName = "Contractors", Number = 3, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category4Id, CategoryName = "Education and Training", Number = 4, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category5Id, CategoryName = "Employee Benefits", Number = 5, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category6Id, CategoryName = "Meals and Entertainment", Number = 6, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category7Id, CategoryName = "Office Expenses and Postage", Number = 7, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category8Id, CategoryName = "Other Expenses", Number = 8, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category9Id, CategoryName = "Personal", Number = 9, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category10Id, CategoryName = "Professional Services", Number = 10, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category11Id, CategoryName = "Rent or Lease", Number = 11, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category12Id, CategoryName = "Supplies", Number = 12, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category13Id, CategoryName = "Travel", Number = 13, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category14Id, CategoryName = "Uncategorized Expenses", Number = 14, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category15Id, CategoryName = "Utilities", Number = 15, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new Category { Id = category16Id, CategoryName = "Cost of Goods Sold", Number = 16, CanEdit = false, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  }
    };

            var categoryItems = new List<CategoryItem>
    {
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000001"), ItemName = "None", Number = 1, CanEdit = false, CategoryId = category1Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000002"), ItemName = "Gas", Number = 2, CanEdit = false, CategoryId = category2Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000003"), ItemName = "Mileage", Number = 3, CanEdit = false, CategoryId = category2Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000004"), ItemName = "Repairs", Number = 4, CanEdit = false, CategoryId = category2Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000005"), ItemName = "Vehicle Insurance", Number = 5, CanEdit = false, CategoryId = category2Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000006"), ItemName = "Vehicle Licensing", Number = 6, CanEdit = false, CategoryId = category2Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000007"), ItemName = "None", Number = 7, CanEdit = false, CategoryId = category3Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000008"), ItemName = "None", Number = 8, CanEdit = false, CategoryId = category4Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000009"), ItemName = "Accident Insurance", Number = 9, CanEdit = false, CategoryId = category5Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000010"), ItemName = "Health Insurance", Number = 10, CanEdit = false, CategoryId = category5Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000011"), ItemName = "Life Insurance", Number = 11, CanEdit = false, CategoryId = category5Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000012"), ItemName = "Entertainment", Number = 12, CanEdit = false, CategoryId = category6Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000013"), ItemName = "Restaurants/Dining", Number = 13, CanEdit = false, CategoryId = category6Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000014"), ItemName = "Hardware", Number = 14, CanEdit = false, CategoryId = category7Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000015"), ItemName = "Office Supplies", Number = 15, CanEdit = false, CategoryId = category7Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000016"), ItemName = "Packaging", Number = 16, CanEdit = false, CategoryId = category7Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000017"), ItemName = "Postage", Number = 17, CanEdit = false, CategoryId = category7Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000018"), ItemName = "Printing", Number = 18, CanEdit = false, CategoryId = category7Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000019"), ItemName = "Shipping and Couriers", Number = 19, CanEdit = false, CategoryId = category7Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Software", Number = 20, CanEdit = false, CategoryId = category7Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Stationery", Number = 21, CanEdit = false, CategoryId = category7Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Bank Fees", Number = 22, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Business Insurance", Number = 23, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt , CompanyId = null },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Commissions", Number = 24, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Depreciation", Number = 25, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Interest - Mortgage", Number = 26, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000027"), ItemName = "Interest - Other", Number = 27, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000028"), ItemName = "Online Services", Number = 28, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000029"), ItemName = "Reference Materials", Number = 29, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000030"), ItemName = "Repairs and Maintenance", Number = 30, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000031"), ItemName = "Subscriptions/Dues/Memberships", Number = 31, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000032"), ItemName = "Taxes and Licenses", Number = 32, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Wages", Number = 33, CanEdit = false, CategoryId = category8Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "None", Number = 34, CanEdit = false, CategoryId = category9Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Accounting", Number = 35, CanEdit = false, CategoryId = category10Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Legal Fees", Number = 36, CanEdit = false, CategoryId = category10Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Equipment", Number = 37, CanEdit = false, CategoryId = category11Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Machinery", Number = 38, CanEdit = false, CategoryId = category11Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-************"), ItemName = "Office Space", Number = 39, CanEdit = false, CategoryId = category11Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000040"), ItemName = "Vehicles", Number = 40, CanEdit = false, CategoryId = category11Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000041"), ItemName = "None", Number = 41, CanEdit = false, CategoryId = category12Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000042"), ItemName = "Airfare", Number = 42, CanEdit = false, CategoryId = category13Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000043"), ItemName = "Hotel/Lodging/Accommodation", Number = 43, CanEdit = false, CategoryId = category13Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000044"), ItemName = "Taxi and Parking", Number = 44, CanEdit = false, CategoryId = category13Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000045"), ItemName = "None", Number = 45, CanEdit = false, CategoryId = category14Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000046"), ItemName = "Gas and Electrical", Number = 46, CanEdit = false, CategoryId = category15Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000047"), ItemName = "Phone", Number = 47, CanEdit = false, CategoryId = category15Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000048"), ItemName = "Cost of Billed Expenses", Number = 48, CanEdit = false, CategoryId = category16Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  },
        new CategoryItem { Id = new Guid("********-0000-0000-0000-000000000049"), ItemName = "Cost of Shipping and Handling", Number = 49, CanEdit = false, CategoryId = category16Id, CreatedAt = createdAt, UpdatedAt = updatedAt, CompanyId = null  }
    };

            // Seed plans
            var planFreeId = new Guid("*************-0000-0000-000000000001");
            var planProId = new Guid("*************-0000-0000-000000000002");
            var planEliteId = new Guid("*************-0000-0000-000000000003");

            var plans = new List<Plan>
            {
                new Plan {
                    Id = planFreeId,
                    Name = "Departure Package",
                    Description = "Start your transactions journey with essential features and flexibility.",
                    MonthlyPrice = 0,
                    YearlyPrice = 0,
                    IsRecommended = false,
                    MaxClients = 3,
                    MaxTeamMembers = 1,
                    AdditionalUserPrice = 0,
                    HasUnlimitedInvoices = true,
                    HasTimeTracking = true,
                    HasCalendar = true,
                    HasDashboard = true,
                    HasExpenses = false,
                    HasEstimates = false,
                    HasIntegrations = false,
                    HasReports = false,
                    HasAccountingUser = false,
                    HasContractingUser = false,
                    HasUnlimitedUsers = true,
                    HasUnlimitedClients = false,
                    PlanType = InnoBook.Enum.PlanEnum.Free,
                    CreatedAt = createdAt,
                    UpdatedAt = updatedAt
                },
                new Plan {
                    Id = planProId,
                    Name = "Professional Access",
                    Description = "Unlock advanced tools and enhanced security for seamless transactions.",
                    MonthlyPrice = 10,
                    YearlyPrice = 108,
                    IsRecommended = true,
                    MaxClients = 10,
                    MaxTeamMembers = 3,
                    AdditionalUserPrice = 3,
                    HasUnlimitedInvoices = true,
                    HasTimeTracking = true,
                    HasCalendar = true,
                    HasDashboard = true,
                    HasExpenses = true,
                    HasEstimates = true,
                    HasIntegrations = true,
                    HasReports = true,
                    HasAccountingUser = true,
                    HasContractingUser = true,
                    HasUnlimitedUsers = false,
                    HasUnlimitedClients = false,
                    PlanType = InnoBook.Enum.PlanEnum.Professional,
                    CreatedAt = createdAt,
                    UpdatedAt = updatedAt
                },
                new Plan {
                    Id = planEliteId,
                    Name = "Elite Plan",
                    Description = "Access premium features for optimal transaction control and performance.",
                    MonthlyPrice = 20,
                    YearlyPrice = 216,
                    IsRecommended = false,
                    MaxClients = 0,
                    MaxTeamMembers = 5,
                    AdditionalUserPrice = 6,
                    HasUnlimitedInvoices = true,
                    HasTimeTracking = true,
                    HasCalendar = true,
                    HasDashboard = true,
                    HasExpenses = true,
                    HasEstimates = true,
                    HasIntegrations = true,
                    HasReports = true,
                    HasAccountingUser = true,
                    HasContractingUser = true,
                    HasUnlimitedUsers = false,
                    HasUnlimitedClients = true,
                    PlanType = InnoBook.Enum.PlanEnum.Elite,
                    CreatedAt = createdAt,
                    UpdatedAt = updatedAt
                }
            };

            modelBuilder.Entity<Category>().HasData(categories);
            modelBuilder.Entity<CategoryItem>().HasData(categoryItems);
            modelBuilder.Entity<Plan>().HasData(plans);
        }
    }
}