using InnoBook.Services.Classes;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace InnoBook
{
    /// <summary>
    /// Simple test class to verify encryption functionality
    /// This is for testing purposes only and should be removed in production
    /// </summary>
    public class EncryptionTest
    {
        public static async Task TestEncryption()
        {
            // This is a simple test method to verify encryption works
            // You can call this from a controller or create a test endpoint
            
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.Development.json")
                .Build();

            var connectionString = configuration.GetConnectionString("InnoLogicielContext");
            
            var optionsBuilder = new DbContextOptionsBuilder<InnoLogicielContext>();
            optionsBuilder.UseNpgsql(connectionString).UseSnakeCaseNamingConvention();

            using var context = new InnoLogicielContext(optionsBuilder.Options);
            var encryptionService = new EncryptionService(context, configuration);

            try
            {
                // Test basic encryption/decryption
                var originalText = "John Doe";
                Console.WriteLine($"Original text: {originalText}");

                var encrypted = await encryptionService.EncryptAsync(originalText);
                Console.WriteLine($"Encrypted text: {encrypted}");

                var decrypted = await encryptionService.DecryptAsync(encrypted);
                Console.WriteLine($"Decrypted text: {decrypted}");

                if (originalText == decrypted)
                {
                    Console.WriteLine("✅ Encryption test PASSED!");
                }
                else
                {
                    Console.WriteLine("❌ Encryption test FAILED!");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Encryption test ERROR: {ex.Message}");
            }
        }
    }
}
