<app-inno-modal-wrapper
  [title]="!data ? ('PROJECT.ADD_PROJECT_FORM.Title' | translate): ('PROJECT.ADD_PROJECT_FORM.TitleEdit' | translate) "
  (onClose)="handleClose()">
  <form [formGroup]="newprojectForm">

    <div class="w-full p-[16px] flex flex-col gap-[16px]">
      <app-inno-form-project-type
        #selectProjectTypeElement
        [label]="'PROJECT.ADD_PROJECT_FORM.ProjectType' | translate"
        [options]="projectType"
        [formControl]="f['option']"
        [value]="f['option'].value"
        [placeholder]="'PROJECT.ADD_PROJECT_FORM.ProjectTypePlaceholder' | translate"
        [customOptionTemplate]="projectTypeOptionTemplate">
        <ng-template #projectTypeOptionTemplate let-item>
          <div (click)="handleSelectProjectType(item)"
            class="w-full flex p-[8px] items-center gap-[10px] hover:bg-bg-secondary rounded-md cursor-pointer">
            <div class="w-full">
              <p class="line-clamp-1 text-text-primary text-text-sm-regular">
                {{ item.name }}
              </p>
            </div>
          </div>
        </ng-template>
      </app-inno-form-project-type>

      <app-inno-form-input
        [label]="'PROJECT.ADD_PROJECT_FORM.ProjectName' | translate"
        [placeholder]="'PROJECT.ADD_PROJECT_FORM.ProjectNamePlaceholder' | translate"
        [formControl]="f['projectname']"
        [value]="f['projectname'].value"
        [errorMessages]="{
          required: 'PROJECT.ADD_PROJECT_FORM.ProjectName' | translate
        }" />

      <app-inno-form-textarea
        [label]="'PROJECT.ADD_PROJECT_FORM.Description' | translate"
        [isAbleResize]="true"
        [formControl]="f['description']"
        [value]="f['description'].value"
        [placeholder]="'PROJECT.ADD_PROJECT_FORM.DescriptionPlaceholder' | translate" />

      <app-inno-form-select-search
        #selectSearchClientElement
        [label]="'PROJECT.ADD_PROJECT_FORM.AssignClient' | translate"
        [options]="clientOptions"
        [formControl]="f['clientId']"
        [value]="f['clientId'].value"
        [placeholder]="'PROJECT.ADD_PROJECT_FORM.AssignClientPlaceholder' | translate"
        [errorMessages]="{ required: 'PROJECT.ADD_PROJECT_FORM.ClientRequired' | translate}"
        [customOptionTemplate]="projectOptionTemplate">
        <ng-template #projectOptionTemplate let-item>
          <div (click)="handleSelectClient(item)"
            class="w-full flex p-[8px] items-center gap-[10px] hover:bg-bg-secondary rounded-md cursor-pointer">
            <ngx-avatars
              [size]="32"
              [name]="item.label" />
            <div class="w-full flex flex-col">
              <p class="line-clamp-1 text-text-primary text-text-sm-regular">
                {{ item.label }}
              </p>
              @if(item?.metadata?.client?.isInternal)
              {
              <p
                class="line-clamp-1 text-text-sm-regular  text-text-tertiary">
                Internal ( {{ item.label }})
              </p>
              }
            </div>
          </div>
        </ng-template>
      </app-inno-form-select-search>

      <app-inno-form-datepicker
        [label]="'PROJECT.ADD_PROJECT_FORM.EndDate' | translate"
        [placeholder]="'PROJECT.ADD_PROJECT_FORM.EndDatePlaceholder' | translate"
        [value]="f['endDate'].value"
        [formControl]="f['endDate']" />
      @if(f['option'].value==hourly)
      {
      <app-inno-form-input
        [label]="'PROJECT.ADD_PROJECT_FORM.HourlyRate' | translate"
        type="number"
        [formControl]="f['hourly']"
        [value]="f['hourly'].value"
        [placeholder]="'PROJECT.ADD_PROJECT_FORM.HourlyRatePlaceholder' | translate" />
      }
      <app-inno-form-input
        [label]="'PROJECT.ADD_PROJECT_FORM.FlatRate' | translate"
        type="number"
        [formControl]="f['flat']"
        [value]="f['flat'].value"
        [placeholder]="'PROJECT.ADD_PROJECT_FORM.FlatRatePlaceholder' | translate" />

      <app-inno-form-input
        [label]="'PROJECT.ADD_PROJECT_FORM.TotalHours' | translate"
        type="number"
        [formControl]="f['total']"
        [value]="f['total'].value"
        [placeholder]="'PROJECT.ADD_PROJECT_FORM.TotalHoursPlaceholder' | translate" />

      <div class="w-full flex flex-col relative">
        <label class="text-text-secondary text-text-sm-semibold mb-[2px]">
          {{ 'PROJECT.ADD_PROJECT_FORM.Member' | translate }}
        </label>
        <div class="flex flex-wrap gap-2 w-full">
          @for(item of listChooseUser ; track item; let i=$index) {
          @if(item.status==0)
          {
          <div class="w-[40px] h-[40px] rounded-full relative">

            <!-- i clearly doesnt understand that part. F.C. -->
            <!--Answer: You cannot remove yourself from a project you created..but you can remove other members. -->
            @if(auth_services.getIdUser()!=item.userId)
            {
            <span (click)="Remove(i)"
              class="material-icons icon_clear cursor-pointer click view-hover-inline-block text-dark bg-white">
              close
            </span>
            }

            @if(item.firstName)
            {

            <ngx-avatars
              matTooltip="{{item.firstName}} {{item.lastName}} "
              [size]="40"
              bgColor="{{_storeService.getBgColor(item.firstName.slice(0,1))}}"
              [name]="item.firstName.charAt(0) +' '+ (item.lastName ? item.lastName.charAt(0) : '')" />
            }
            @else{
            <ngx-avatars
              matTooltip="{{item.email}}"
              [size]="40"
              bgColor="{{_storeService.getBgColor(item.email.slice(0,1))}}"
              [name]="item.email.slice(0,1)" />
            }

          </div>
          }

          }
          <ng-template #templateTriggerSelectUser>
            <button
              class="cursor-pointer w-[40px] h-[40px] rounded-full border border-dashed flex justify-center items-center border-border-in-tertiary">
              <img class="w-[20px]"
                src="../../../../assets/img/icon/ic_add.svg"
                alt="Icon">
            </button>
          </ng-template>
          <app-inno-select-search-user
            [isAll]="false"
            [templateTrigger]="templateTriggerSelectUser"
            (onSelect)="handleSelectUser($event)" />
        </div>
      </div>
      <app-inno-form-checkbox
        [checked]="previewBillable"
        (onChange)="handleChangeBillable($event)">
        {{ 'PROJECT.ADD_PROJECT_FORM.Billable' | translate }}
      </app-inno-form-checkbox>
      <div class="w-full flex flex-col relative">
        <div class="w-full">
          @if(listService.length>0)
          {

          <div class="overflow-auto w-full">
            <div class="invoiceTableLayout">
              <p class="text-text-tertiary text-text-sm-semibold">
                Service Name
              </p>
              <p class="text-text-tertiary text-text-sm-semibold">
                Rate
              </p>
              <p class="text-text-tertiary text-text-sm-semibold">
                Quantity
              </p>
              <p class="text-text-tertiary text-text-sm-semibold">
                Tax
              </p>
              <p class="text-text-tertiary text-text-sm-semibold">
                Line Total
              </p>
            </div>
            @for(serviceItem of listService; track serviceItem;
            let i = $index) {
            <div class="invoiceTableLayout">
              <p class="text-text-primary text-text-md-semibold">
                {{ serviceItem?.serviceName ?? '' }}
              </p>
              <p class="text-text-primary text-text-md-regular">
                ${{ serviceItem?.rate ?? 0 | formatNumber }}
              </p>
              <p class="text-text-primary text-text-md-regular">
                {{ serviceItem?.qty ?? 0 | formatNumber }}
              </p>
              @if(serviceItem?.taxes&&serviceItem?.taxes.length>0&&getNameSelectedTaxes(serviceItem?.taxes)!='')
              {
              <p
                class="text-text-primary text-text-md-regular cursor-pointer">
                {{ getNameSelectedTaxes(serviceItem?.taxes) }}
              </p>
              }
              @else{
              <p
                class="text-text-primary text-text-md-regular cursor-pointer">
                -
              </p>
              }

              <p class="text-text-primary text-text-md-bold">
                ${{
                calculateTotalInvoiceItem(serviceItem?.rate, serviceItem?.qty) | formatNumber
                }}

              </p>
              <app-inno-table-action
                (onEdit)="handleEditService(serviceItem)"
                (onDelete)="handleDeleteInvoiceItem(i)" />
            </div>
            }
          </div>
          }

        </div>
        <div>
          <button type="button" (click)="handleAddService()"
            class="btn btn-outline-secondary btn-dashed">
            <span class="material-icons">add</span>
            {{ 'PROJECT.ADD_PROJECT_FORM.AddService' | translate }}
          </button>
        </div>
      </div>

    </div>

  </form>
  <div footer>
    <app-inno-modal-footer
      (onSubmit)="handleSubmitForm()"
      (onCancel)="handleCancel()" />
  </div>
</app-inno-modal-wrapper>
