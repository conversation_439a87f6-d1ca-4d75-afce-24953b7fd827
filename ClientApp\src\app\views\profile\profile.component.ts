import { Component, DestroyRef, inject, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { Router, RouterModule } from '@angular/router';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { User } from 'app/dto/interface/user.interface';
import { SharedModule } from 'app/module/shared.module';
import { SpinnerService } from 'app/service/spinner.service';
import { ToastService } from 'app/service/toast.service';
import { Query } from '@syncfusion/ej2-data';
import { FilteringEventArgs } from '@syncfusion/ej2-dropdowns';
import { DropDownListComponent, DropDownListModule } from '@syncfusion/ej2-angular-dropdowns';
import { EmitType } from '@syncfusion/ej2-base';
import { TIMEZONE } from '../../utils/timezone';
import { TranslateService } from '@ngx-translate/core';
@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [RouterModule, SharedModule, MatFormFieldModule, DropDownListModule],
  templateUrl: './profile.component.html',
  styleUrl: './profile.component.scss'
})
export class ProfileComponent implements OnInit {
  public tzNames!: any[];
  businessObject: any;
  companyId!: string;
  public selectedTz!: string;
  public profileForm!: UntypedFormGroup;
  private spiner_services = inject(SpinnerService)
  private auth_services = inject(AuthenticationService)
  router = inject(Router);
  private _toastService = inject(ToastService)
  destroyRef = inject(DestroyRef);
  private formBuilder = inject(UntypedFormBuilder)
  private translate = inject(TranslateService)
  constructor() {
    this.profileForm = this.formBuilder.group({
      firstname: ["", Validators.compose([Validators.required])],
      lastname: ["", Validators.compose([Validators.required])],
      email: ["", Validators.compose([Validators.required, Validators.email])],
      username: [""],
      timezone: [""],
    },

    );
    this.tzNames = TIMEZONE;
  }
  get f() {
    return this.profileForm.controls;
  }
  ngOnInit(): void {
    this.GetUser();
  }
  _handleData(_data: any) {
    this.f["firstname"].setValue(_data.firstName)
    this.f["lastname"].setValue(_data.lastName)
    this.f["email"].setValue(_data.email)
    this.f["username"].setValue(_data.username)
    this.f["timezone"].setValue(_data.timeZoneId)
    this.f["email"].disable();
  }

  GetUser() {
    this.auth_services.GetUser().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {

      if (res) {
        this._handleData(res);

      }
    }
    )
  }
  onSubmit() {
    this.spiner_services.show();
    let payload: User = {
      firstName: this.profileForm.controls["firstname"].value,
      lastName: this.profileForm.controls["lastname"].value,
      email: this.profileForm.controls["email"].value,
      username: this.profileForm.controls["username"].value,
      timeZoneId: this.profileForm.controls["timezone"].value,
    }
    this.auth_services.UpdateUserProfile(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.spiner_services.hide();
        this._toastService.showSuccess(this.translate.instant("TOAST.Save"), this.translate.instant("TOAST.Success"))
        this._handleData(res);

      }
    }
    )
  }
  // maps the appropriate column to fields property
  public fields: Object = { text: 'Name', value: 'Code' };
  // set the height of the popup element
  public height: string = '220px';
  // set the placeholder to DropDownList input element
  public watermark: string = 'Select time zone';
  // set the placeholder to filter search box input element
  public filterPlaceholder: string = 'Search';
  // filtering event handler to filter a Country
  public onFiltering: EmitType<FilteringEventArgs> = (e: FilteringEventArgs) => {
    let query: Query = new Query();
    //frame the query based on search string with filter type.
    query = (e.text !== '') ? query.where('Name', 'startswith', e.text, true) : query;
    //pass the filter data source, filter query to updateData method.
    e.updateData(this.tzNames, query);
  }

  @ViewChild('ddlelement')
  public dropDownListObject?: DropDownListComponent;
  onChange(args: any): void {
    this.profileForm.controls['timezone'].setValue(args.value);
  }
}
