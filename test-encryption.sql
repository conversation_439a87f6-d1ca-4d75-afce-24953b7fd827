-- Test script to verify pgcrypto and encryption functions work
-- Run this in your PostgreSQL database to test encryption

-- 1. Check if pgcrypto extension is enabled
SELECT 
    extname as "Extension Name",
    extversion as "Version"
FROM pg_extension 
WHERE extname = 'pgcrypto';

-- 2. Test basic encryption/decryption
DO $$
DECLARE
    test_text TEXT := 'Hello World';
    test_key TEXT := 'test-key-32-characters-long-here';
    encrypted_data BYTEA;
    decrypted_text TEXT;
BEGIN
    -- Test encryption
    SELECT pgp_sym_encrypt(test_text, test_key) INTO encrypted_data;
    RAISE NOTICE 'Encrypted data length: %', length(encrypted_data);
    
    -- Test decryption
    SELECT pgp_sym_decrypt(encrypted_data, test_key) INTO decrypted_text;
    
    -- Verify the round-trip works
    IF decrypted_text = test_text THEN
        RAISE NOTICE '✅ Encryption test PASSED: "%" -> encrypted -> "%"', test_text, decrypted_text;
    ELSE
        RAISE EXCEPTION '❌ Encryption test FAILED: Expected "%" but got "%"', test_text, decrypted_text;
    END IF;
END $$;

-- 3. Test the exact SQL queries used by EncryptionService
SELECT pgp_sym_encrypt('Test Data', 'my-encryption-key-32-chars-long') as encrypted_result;

-- 4. Test decryption with the same pattern
WITH encrypted_test AS (
    SELECT pgp_sym_encrypt('Test Data', 'my-encryption-key-32-chars-long') as encrypted_data
)
SELECT pgp_sym_decrypt(encrypted_data, 'my-encryption-key-32-chars-long') as decrypted_result
FROM encrypted_test;

-- 5. Show available pgcrypto functions
SELECT 
    proname as "Function Name",
    pg_get_function_identity_arguments(oid) as "Arguments"
FROM pg_proc 
WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
  AND proname LIKE 'pgp_%'
ORDER BY proname;
