import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { AuthenticationService } from './../../auth/service/authentication.service';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { StoreService } from 'app/service/store.service';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { ExpensesService } from './../../service/expenses.service';
import { ToastService } from 'app/service/toast.service';
import { LayoutUtilsService } from './../../core/services/layout-utils.service';
import { SharedModule } from 'app/module/shared.module';
import { Component, DestroyRef, OnInit, ViewChild } from '@angular/core';
import { ActionEventArgs, GridAllModule, GridComponent, GridModule, PagerModule, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { GetExpenseQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { InnoDatepickerComponent } from 'app/component/inno-datepicker/inno-datepicker.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoStatusComponent } from 'app/component/inno-status/inno-status.component';
import { NewExpenseFormDialog } from '../../service/dialog/new-expenses.dialog';
import { AvatarModule } from 'ngx-avatars';
import { MatTooltipModule } from '@angular/material/tooltip';
import { DecimalPipe } from 'app/pipes/decimal.pipe';
import { formatDateFilter } from 'app/helpers/common.helper';
import { Role } from 'app/enum/role.enum';

@Component({
  selector: 'app-expenses',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [
    GridAllModule,
    PagerModule,
    SharedModule,
    GridModule,
    MatTooltipModule,
    AvatarModule,
    DecimalPipe,
    FormatNumberPipe,
    InnoInputSearchComponent,
    InnoDatepickerComponent,
    InnoTableActionComponent,
    InnoStatusComponent,
    InnoSpinomponent,
    InnoPopoverComponent
  ],
  templateUrl: './expenses.component.html',
  styleUrl: './expenses.component.scss'
})
export class ExpensesComponent implements OnInit {
  public sort: SortGird;
  public sortOptions: SortSettingsModel = { columns: [] };
  public search: string = '';
  public dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1;
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10;
  private searchSubject = new Subject<string>();
  private _subscriptions: Subscription[] = [];
  @ViewChild('grid') grid?: GridComponent;
  public idTime: any;
  public columnName: string;
  public direction: any;
  public date: any;
  public isLoading = false;
  public isAdmin = false;

  constructor(
    private expenseService: ExpensesService,
    private toastService: ToastService,
    private translate: TranslateService,
    private activatedRoute: ActivatedRoute,
    private layoutUtilsService: LayoutUtilsService,
    private authenticationService: AuthenticationService,
    private router: Router,
    public _storeService: StoreService,
    private destroyRef: DestroyRef,
    private newExpenseFormDialog: NewExpenseFormDialog
  ) {

  }
  handleSearch(search: string) {
    this.searchSubject.next(search);
  }

  ngOnInit(): void {
    this._subscriptions.push(
      this._storeService.getRoleBusinessAsObservable().subscribe(role => {
        this.isAdmin = role === Role.Admin;
      })
    );
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      this.currentPage = queryParams?.page ?? 1;
      this.GetAllExpenses();
    });
    const sb = this.searchSubject.pipe(debounceTime(550)).subscribe((search) => {
      this.search = search ?? "";
      this.GetAllExpenses();
    });
    this._subscriptions.push(sb)
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize;
      this.GetAllExpenses();
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }

  }

  GetAllExpenses() {
    this.isLoading = true
    let payload: GetExpenseQueryParam = {
      Page: this.currentPage,
      PageSize: this.pageSizesDefault,
      Search: this.search,
      filterDate: this.date,
      ...this.sort
    }
    this.expenseService.GetAllExpenses(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.totalPages = res.totalRecords;
        this.dataSource = res.data;
        this.isLoading = false;
        if (this.columnName) {
          this.sortOptions = {
            columns: [{ field: this.columnName, direction: this.direction }]
          };
        }

      }
    }
    )
  }

  modifyExpenses(item?: any) {
    const dialog = this.newExpenseFormDialog.open(item);

    dialog.then((c) => {
      c.afterClosed().subscribe((result) => {
        if (result) {
          this.toastService.showSuccess(this.translate.instant("TOAST.Save"), this.translate.instant("TOAST.Success"));
          this.GetAllExpenses();
        }
      });
    });
  }
  MarkAsPaid(Id: string) {
    this.expenseService.MarkAsPaid(Id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this.toastService.showSuccess(this.translate.instant("TOAST.Paid"), this.translate.instant("TOAST.Success"));
        this.GetAllExpenses();
      }
    }
    )
  }
  handleChangeDateFilter(dateSelected: any) {
    this.date = formatDateFilter(dateSelected);
    this.GetAllExpenses();
  }
  handleDeleteExpense(item: any) {
    const _title = this.translate.instant('EXPENSES.DeleteExpeneses');
    const _description = this.translate.instant('COMMON.ConfirmDelete');
    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description,
    }).then(isConfirm => {
      if (!isConfirm) return

      this.expenseService.DeleteExpenses([item.id])
        .pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
          if (res) {
            this.toastService.showSuccess(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
            this.GetAllExpenses();
          }
          else {
            this.toastService.showError(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Fail"))
          }
        })
    })
  }
  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        this.GetAllExpenses()
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      this.GetAllExpenses()
    }

  }

  ngOnDestroy(): void {
    if (this.idTime) {
      clearTimeout(this.idTime)
    }

    this._subscriptions.forEach((sb) => sb.unsubscribe());
  }
}
