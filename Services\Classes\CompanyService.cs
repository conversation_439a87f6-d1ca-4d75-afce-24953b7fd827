﻿using InnoBook.Common;
using InnoBook.DTO.Company;
using InnoBook.Entities;
using InnoBook.Interface;
using InnoBook.Request.Company;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using static Org.BouncyCastle.Math.EC.ECCurve;
using System.ComponentModel.Design;

namespace InnoBook.Services.Classes
{
    public class CompanyService(InnoLogicielContext context, IConfiguration config) : ICompanyService
    {
        public async Task<GetCompanyDTO> GetCompanyById(Guid id)
        {
            var data = await context.Companies
      .Where(x => x.Id == id)
      .Select(x => new GetCompanyDTO
      {
          Id = x.Id,
          BusinessName = x.BusinessName,
          Phone = x.Phone,
          CompanyImage=x.CompanyImage,
          Email = x.Email,
          Country = x.Country,
          Adress = x.Adress,
          Adress2 = x.Adress2,
          City = x.City,
          Province = x.Province,
          PostalCode = x.PostalCode,
          TimeZone = x.TimeZone,
          StartWeekOn = x.StartWeekOn,
          DateFormat = x.DateFormat,
          Note = x.Note,
          Rate = x.Rate,
          FiscalMonth = x.FiscalMonth,
          Currency = x.Currency,
          FiscalDay = x.FiscalDay,
          IsPremiumCompany = x.IsPremiumCompany
      })
      .FirstOrDefaultAsync();
            return data;
        }

        public async Task<string> GetCurrencyCompany(Guid id)
        {
          var data = await context.Companies
            .Where(x => x.Id == id)
            .Select(x => new
            {
                x.Currency
            })
            .FirstOrDefaultAsync();
            if(data.Currency == null)
            {
                return string.Empty;
            }    
            return data.Currency.ToString();
        }

        public async Task<List<Company>> GetListCompany()
        {
          return  await context.Companies.ToListAsync();
        }

        public async Task Insert(RequestCompany model,string IdUser)
        {
            var newCompany = new Company
            {
                Id = Guid.NewGuid(),
                CreatedBy= IdUser,
               BusinessName = model.BusinessName,
               Phone = model.Phone,
               Email = model.Email,
               Country = model.Country,
               Adress = model.Adress,
               Adress2 = model.Adress2,
               City = model.City,
                Province = model.Province,
                PostalCode = model.PostalCode,
                TimeZone = model.TimeZone,
                StartWeekOn = model.StartWeekOn,
                DateFormat = model.DateFormat,
                Note = model.Note,
                Rate = model.Rate,
                FiscalMonth = model.FiscalMonth,
                FiscalDay = model.FiscalDay,
                Currency = model.Currency
            };
            context.Companies.Add(newCompany);
            await context.SaveChangesAsync();
        }

        public async Task<bool> RemoveImgCompany(string companyId)
        {
            try
            {
                var data = context.Companies.FirstOrDefault(x => x.Id.ToString() == companyId);
                data.CompanyImage = null;
                await context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
            
        }

        public async Task<bool> Update(RequestCompany model, string companyId)
        {
            try
            {

            var data = context.Companies.FirstOrDefault(x => x.Id == model.Id);
            if (data != null)
            {
                    if (!string.IsNullOrWhiteSpace(model.base64))
                    {
                        var file = DigitalOcean.Base64ToIFormFile(model.base64, model.filename, model.type);
                        var uploaded = await DigitalOcean.UploadFileAsync(file, config, companyId.ToString());

                        if (uploaded)
                        {
                            data.CompanyImage = model.filename;
                        }
                    }

                    data.BusinessName = model.BusinessName;
                data.Phone = model.Phone;
                data.Email = model.Email;
                data.Country = model.Country;
                data.Adress = model.Adress;
                data.Adress2 = model.Adress2;
                data.City = model.City;
                data.Province = model.Province;
                data.PostalCode = model.PostalCode;
                data.TimeZone = model.TimeZone;
                data.StartWeekOn = model.StartWeekOn;
                data.DateFormat = model.DateFormat;
                data.Note = model.Note;
                 await context.SaveChangesAsync();

                }
                return true;
            }
            catch
            {
                return false;
            }

        }

        public async Task UpdateFinancial(RequestCompany model)
        {
            var data=context.Companies.FirstOrDefault(x=>x.Id==model.Id);
            if(data!=null)
            {
                data.Rate = model.Rate;
                data.FiscalDay = model.FiscalDay;
                data.FiscalMonth = model.FiscalMonth;
                data.Currency = model.Currency;
                await context.SaveChangesAsync();
            }    
        
        }
        
    }
}
