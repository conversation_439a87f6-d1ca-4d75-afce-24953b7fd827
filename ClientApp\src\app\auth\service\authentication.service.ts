import { DataService } from 'app/service/data.service';
import { SocialAuthService } from '@abacritt/angularx-social-login';
import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { IEmail } from 'app/dto/interface/email.interface';
import { Itimer, IUserLogin } from 'app/dto/interface/login.interface';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { User } from 'app/dto/interface/user.interface';
import { environment } from 'environments/environment';
import { CookieService } from 'ngx-cookie-service';
import { catchError, map, Observable, tap, throwError } from 'rxjs';
import { JwtHelperService } from '@auth0/angular-jwt';
import { ValidateInvitation } from 'app/dto/interface/userBusiness.interface';

const UrlApi = environment.HOST_API + "/api"
const access_token = "access_token";
const refresh_token = "refresh_token";
@Injectable({ providedIn: 'root' })
export class AuthenticationService {
  private jwt = '';
  private http = inject(HttpClient)
  private router = inject(Router);
  authService = inject(SocialAuthService);
  private cookieService = inject(CookieService)
  constructor(

  ) { }
  saveToken_cookie(accesstoken?: string, refreshtoken?: string) {
    if (accesstoken) {
      this.cookieService.set(access_token, accesstoken!, 365, '/', null!);
    }
    if (refreshtoken) this.cookieService.set(refresh_token, refreshtoken!, 365, '/', null!);
  }
  public getAccessToken() {
    return this.cookieService.get(access_token)
  }
  public getRefreshToken() {
    return this.cookieService.get(refresh_token)
  }
  public ClearCookie() {
    this.cookieService.delete(access_token, '/');
    this.cookieService.delete(refresh_token, '/');
  }
  public ClearLocalStorage() {
    localStorage.removeItem("business")
    localStorage.removeItem("isRunning")
    localStorage.removeItem("ResumeData")
  }

  public getRole() {
    const jwtHelper = new JwtHelperService();
    const jwt = jwtHelper.decodeToken(this.getAccessToken());
    if (jwt)
      return jwt["http://schemas.microsoft.com/ws/2008/06/identity/claims/role"];
    else
      return "";
  }
  public getBusinessRole() {
    const jwtHelper = new JwtHelperService();
    const jwt = jwtHelper.decodeToken(this.getAccessToken());
    if (jwt)
      return jwt["businessRole"];
    else
      return "";
  }
  public getIdUser() {
    const jwtHelper = new JwtHelperService();
    const jwt = jwtHelper.decodeToken(this.getAccessToken());
    if (jwt)
      return jwt["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"];
    else
      return "";
  }



  public isJwtExpired() {
    if (!this.getAccessToken()) {
      // Not exists => expired
      return true;
    }

    const jwtHelper = new JwtHelperService();
    return jwtHelper.isTokenExpired(this.getAccessToken());
  }


  getParamsToken() {

    const url = window.location.href;
    let paramValue = undefined;
    if (url.includes('?')) {
      const httpParams = new HttpParams({ fromString: url.split('?')[1] });
      paramValue = httpParams.get('token');
      if (paramValue) {
        paramValue = decodeURIComponent(paramValue);
      }
    }

    return paramValue.replace(/\s/g, "+");
  }

  UpdateTimer(payload: Itimer) {
    return this.http.post(UrlApi + '/Users/<USER>', payload);
  }
  login(payload: IUserLogin) {
    return this.http.post(UrlApi + '/Users/<USER>', payload);
  }
  CheckTimer() {
    return this.http.get(UrlApi + `/Users/<USER>
  }
  SignIn(email: string, code: string) {
    return this.http.get(UrlApi + `/Users/<USER>
  }
  GetUser(): Observable<User> {
    return this.http.get<User>(UrlApi + `/Users/<USER>
  }
  GetUserByBusinessId(businessId: string): Observable<User> {
    return this.http.get<User>(UrlApi + `/Users/<USER>/` + businessId);
  }

  UpdateUserProfile(payload: User) {
    return this.http.put(UrlApi + '/Users/<USER>', payload);
  }
  UpdateUserProfileByEmail(payload: User) {
    return this.http.put(UrlApi + '/Users/<USER>', payload);
  }

  SignUp(payload: User) {
    return this.http.post(UrlApi + `/Users/<USER>
  }

  ResendCode(email: string) {
    return this.http.get(UrlApi + `/Users/<USER>
  }

  RecoverPassword(email: string) {
    return this.http
      .get(UrlApi + `/Users/<USER>
      })
  }
  DeleteUser(payload: any): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + '/Users/<USER>', payload);
  }

  GetAllUser(payload: Parameter) {
    return this.http.get(UrlApi + `/Users/<USER>
  }
  ValidateInvitation(payload: ValidateInvitation) {
    return this.http.post(UrlApi + '/Users/<USER>', payload);
  }
  Register(payload: User) {
    return this.http.post(UrlApi + '/Users/<USER>', payload);
  }
  ChangePassWithToken(payload: any) {
    return this.http.put(UrlApi + `/Users/<USER>
  }

  creatNewUser(payload: User) {
    return this.http.post(UrlApi + '/Users', payload);
  }
  SendMail(payload: IEmail) {
    return this.http.post(UrlApi + '/Users/<USER>', payload);
  }
  RefreshToken(payload: any) {
    return this.http.post(UrlApi + '/Users/<USER>', payload);
  }
  UpdateAccessToken(payload: any) {
    return this.http.post(UrlApi + '/Users/<USER>', payload);
  }

  refreshAccessToken(payload: any): Observable<any> {
    const refreshToken = this.getRefreshToken();
    if (!refreshToken) {
      this.logout();
      return throwError(() => new Error('Refresh token is missing'));
    }

    return this.http.post<any>(`${UrlApi}/Users/<USER>
      .pipe(
        tap(response => this.saveToken_cookie(response.accessToken)),
        map(response => response.accessToken),
        catchError(err => {
          this.logout();
          return throwError(() => new Error('Token refresh failed'));
        })
      );
  }
  logout() {
    this.authService.signOut();
    this.ClearCookie();
    this.ClearLocalStorage();
    this.router.navigate(['/login'])
  }
}
