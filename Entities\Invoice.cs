﻿using InnoLogiciel.Server.Entities;
using System.ComponentModel.DataAnnotations.Schema;

namespace InnoBook.Entities
{
    public class Invoice : BaseGuidEntity
    {
        public Guid CompanyId { get; set; }
        public Guid ClientId { get; set; }
        public Guid? ProjectId { get; set; }
        public Guid? ItemId { get; set; }
        public Guid? ContractorId { get; set; }
        public DateTime? DatePaid { get; set; }
        public string? InvoiceNumber { get; set; }
        public bool IsEstimate { get; set; } = false;
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public string? Reference { get; set; }
        public string? Notes { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal Rate { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public string? Description { get; set; }
        public bool isArchive { get; set; }
        public bool isActive { get; set; } = true;
        public bool isPaid { get; set; } = false;
        public decimal TimeAmount { get; set; }
        public int Status { get; set; } // Consider an enum for invoice status
        public int Position{ get; set; }
        [NotMapped]
        public string? base64 { get; set; }

        [NotMapped]
        public string? type { get; set; }

        [NotMapped]
        public string? filename { get; set; }
        // Navigation properties
        public List<Payment>? Payments { get; set; }
        public List<ItemInvoice>? ItemInvoices { get; set; }

        public Client? Client { get; set; }
        public Company? Company { get; set; }
        public Item? Item { get; set; }
        public Project? Project { get; set; }
    }
}
