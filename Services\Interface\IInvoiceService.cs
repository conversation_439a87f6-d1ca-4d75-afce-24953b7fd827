﻿using InnoBook.DTO.Chart;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Estimate;
using InnoBook.DTO.Invoice;
using InnoBook.Request.Invoice;
namespace InnoBook.Services.Interface
{
    public interface IInvoiceService
    {
        public Task<RequestInvoice> CreatedInvoice(RequestInvoice invoice, string UserId, Guid CompanyId);
        public Task<RequestInvoiceSend> CreatedInvoiceSend(RequestInvoiceSend invoiceSend, string UserId, Guid CompanyId);
        public Task<bool> UpdateArchive(Guid InvoiceId, bool isArchive);
        public Task<GetResultChartDTO> RevenueChart(string CompanyId,int Year);
        public Task<GraphicsChartDTO> GraphicsChart(string CompanyId, int Year);
        public Task<bool> ConvertToInvoice(Guid InvoiceId);
        public Task<bool> DeleteInvoice(List<Guid?> listInvoice, bool isActive);
        public Task<bool> ChangePosition(RequestChangePosition requestChangePosition);
        public Task<string> GetRoleBussiness(string companyId, string UserId);
        public Task<bool> ResetPosition();
        public Task<bool> ResetPositionItemInvoice();
        public Task<bool> MarkAsPaid(string Id);
        public Task<bool> MarkAsSent(string Id);
        
        public Task<int> CountInvoiceByCompany(Guid CompanyId);
        public Task<int> CountInvoiceByContractor(Guid CompanyId);
        public Task<int> CountEstimate(Guid CompanyId);
        public Task<InvoiceDetailDTO> GetInvoiceById(Guid invoiceId,string CompanyId);

        public Task<MemoryStream> PrintInvoice(Guid invoiceId, Guid CompanyId ,Guid UserId);
        public Task<decimal> CalculateInvoice(int status, Guid CompanyId);
        public Task<CalculationSendToMe> CalculationEstimateSendToMe(int status, Guid UserId, string companyId);
        public Task<CalculationSendToMe> CalculationInvoiceSendToMe(int status, Guid UserId, string companyId);
        public Task<PaginatedResponse<InvoiceAllDTO>> GetAllEstimate(GetEstimateRequestParam filter, string companyId, string UserId);
        public Task<PaginatedResponse<InvoiceAllDTO>> GetAllInvoice(GetInvoiceRequestParam filter, string companyId ,string UserId);
        public Task<PaginatedResponse<EstimateAllSendToMeDTO>> GetAllEstimateSendToMe(PaginatedRequest query, string companyId, string UserId);
        public Task<PaginatedResponse<EstimateAllSendToMeDTO>> GetAllInvoiceSendToMe(GetInvoiceRequestParam filter, string companyId, string UserId);
        Task<RequestInvoice> Update(RequestInvoice model, Guid UserId, Guid CompanyId);
        CalculationInvoice CalculationInvoice(int status, string role,Guid UserId, string companyId);

        Task<CalculationEstimate> CalculationEstimate(int status, Guid UserId, string companyId);
    }
}
