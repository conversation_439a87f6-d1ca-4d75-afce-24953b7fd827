@if(imageUrl)
{
<div
  class="drop-zone"
  (dragover)="onDragOver($event)"
  (dragleave)="onDragLeave($event)"
  (drop)="onDrop($event)"
  (click)="triggerFileInput()">
  <div class="previewImageWrapper">
    @if(canEdit)
    {
    <button class="buttonRemove" (click)="handleRemoveFile($event)">
      <img class="w-[16px]" src="../../../assets/img/icon/ic_remove.svg"
        alt="Icon">
    </button>
    }
    <img class="previewImage" [src]="imageUrl" alt="Thumbnail">

  </div>
</div>
}
@else{

<div
  class="drop-zone"
  (dragover)="onDragOver($event)"
  (dragleave)="onDragLeave($event)"
  (drop)="onDrop($event)"
  (click)="triggerFileInput()">
  @if(!fileDropped) {
  <div
    class="w-full h-full flex justify-center items-center text-center text-text-disabled text-text-sm-semibold textTitle">
    {{ placeholder | translate }}
  </div>
  } @else {
  @if(accept === 'image') {
  <!-- Only single image -->
  <div class="previewImageWrapper">
    <button class="buttonRemove" (click)="handleRemoveFileUpload($event)">
      <img class="w-[16px]" src="../../../assets/img/icon/ic_remove.svg"
        alt="Icon">
    </button>
    <img class="previewImage" [src]="previewFileUpload?.[0]" alt="Thumbnail">

  </div>
  <!-- End for single image -->
  }
  }

</div>
}
<input type="file" (change)="onFileSelect($event)" hidden #fileInput
  [multiple]="isMultiple" accept="{{ acceptInput }}">
