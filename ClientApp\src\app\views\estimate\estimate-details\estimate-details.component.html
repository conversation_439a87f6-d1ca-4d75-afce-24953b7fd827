<div class="w-full py-[24px] border-b border-border-primary bg-bg-primary">
  <div class="container-full flex justify-between items-center flex-wrap gap-2">
    <div class="flex items-center gap-[8px]">
      <button class="button-icon button-size-md" (click)="handleBack()">
        <img src="../../../../assets/img/icon/ic_arrow_left.svg" alt="Icon">
      </button>
      <p class="text-text-primary text-headline-lg-bold">
        {{'ESTIMATE.Title'|translate}} {{ estimateInfo?.invoiceNumber ?? '' }}
      </p>
      <app-inno-status [status]="estimateInfo?.status" />
    </div>

    <div class="flex items-center gap-[12px] flex-wrap">
      <app-inno-popover
        position="bottom-end"
        [content]="templateSearchProject">
        <button target class="button-size-md button-outline">
          <img src="../../../../assets/img/icon/ic_three_dot_horizontal.svg"
            alt="icon">
          {{'ESTIMATE.Summary.More'| translate}}
        </button>
      </app-inno-popover>

      <ng-template #templateSearchProject>
        @for(menu of filteredMenu;track menu; let i = $index)
        {
        <button (click)="handleAction(menu.action)"
          class="button-size-md button-transparent">
          <img src="{{menu.icon}}"
            alt="Icon">
          {{menu.label|translate}}
        </button>
        }
      </ng-template>
      <button (click)="EditEstimate()" class="button-size-md button-outline">
        <img src="../../../../assets/img/icon/ic_edit.svg" alt="icon">
        {{'ESTIMATE.Summary.Edit'| translate}}
      </button>
      <button (click)=ShareLink() class="button-size-md button-outline">
        <img src="../../../../assets/img/icon/ic_link_black.svg" alt="icon">
        {{'ESTIMATE.Summary.ByLink'| translate}}
      </button>
      <button (click)="handleFunctionInDevelopment()"
        class="button-size-md button-primary">
        <img src="../../../../assets/img/icon/ic_email_sending.svg" alt="icon">
        {{'ESTIMATE.Summary.SendMail'| translate}}
      </button>
    </div>
  </div>
</div>

<div class="container-full mt-[24px]">
  <div class="w-full w-fit mx-auto bg-bg-primary p-[32px] relative">
    <div class="flex w-full gap-[18px] md:flex-row flex-col">
      <!-- Thumbnail -->
      <div class="w-[160px] h-[100px] shrink-0 mx-auto md:mx-[unset]">
        <img class="rounded-md w-full h-full object-cover"
          [src]="imageUrl"
          onerror="this.src='../../../../assets/img/image_default.svg'"
          alt="image">

      </div>

      <div class="w-full flex flex-col gap-[16px]">
        <div class="w-full">
          @if(!businessInfo.businessName)
          {
          <p class="text-text-md-semibold text-text-primary mb-[1px]">
            {{'COMMON.Loading'|translate}}
          </p>
          }
          <p
            class="text-text-secondary text-text-sm-semibold mb-[2px] font-bold">{{businessInfo?.businessName}}
          </p>
          @if(businessInfo.businessPhoneNumber) {
          <div class="flex items-center gap-[8px]">

            <p class="text-text-xs-regular text-text-secondary">
              {{ businessInfo.businessPhoneNumber | phoneMask }}
            </p>
          </div>
          }

          @if(businessInfo.businessAddress){
          <p class="w-full text-text-xs-regular text-text-secondary">
            {{ businessInfo.businessAddress }}
          </p>
          }
        </div>

        <div class="w-full grid sm:grid-cols-2 lg:grid-cols-4 gap-[16px]">
          <div class="w-full">
            <p
              class="text-text-brand-primary text-text-sm-semibold mb-[2px]">
              {{'ESTIMATE.Summary.BilledTo'| translate}}
            </p>
            <p class="text-text-secondary text-text-md-regular">
              {{ estimateInfo?.client?.clientName ?? '' }}
            </p>
          </div>
          <div class="w-full">
            <p
              class="text-text-brand-primary text-text-sm-semibold mb-[2px]">{{'ESTIMATE.GIRD.EstimateNumber'|
              translate}}
            </p>
            <p class="text-text-secondary text-text-md-regular">
              {{ estimateInfo?.invoiceNumber ?? '' }}
            </p>
          </div>
          <div class="w-full">
            <p class="text-text-brand-primary text-text-sm-semibold mb-[2px]">
              {{'ESTIMATE.Summary.DatefIssue'| translate}}
            </p>
            <p class="text-text-secondary text-text-md-regular">
              {{ selectedDate }}
            </p>
          </div>

        </div>

        <div class="w-full">
          <p
            class="text-text-secondary text-text-sm-semibold mb-[2px]">{{'ESTIMATE.Summary.Description'|
            translate}}</p>
          <p
            class="text-text-secondary text-text-md-regular whitespace-pre-line">
            {{ estimateInfo?.notes ?? '' }}
          </p>
        </div>
      </div>
    </div>

    <div class="w-full mt-[16px] border-t border-dashed border-border-primary">
      <div class="overflow-auto w-full" cdkDropList
        [cdkDropListData]="estimateInfo?.itemInvoices"
        (cdkDropListDropped)="drop($event)">
        <div class="invoiceTableLayout">
          <p class="text-text-brand-primary  text-text-sm-semibold font-bold">
            {{'ESTIMATE.ESTIMATE_FORM.TableHeaders.EstimateItem'|translate}}
          </p>
          <p class="text-text-brand-primary  text-text-sm-semibold font-bold">
            {{'ESTIMATE.ESTIMATE_FORM.TableHeaders.Rate'|translate}}
          </p>
          <p class="text-text-brand-primary  text-text-sm-semibold font-bold">
            {{'ESTIMATE.ESTIMATE_FORM.TableHeaders.Quantity'|translate}}
          </p>

          <p
            class="text-text-brand-primary  text-text-sm-semibold text-right font-bold">
            {{'ESTIMATE.ESTIMATE_FORM.TableHeaders.LineTotal'|translate}}
          </p>
        </div>
        @for(pay of estimateInfo?.itemInvoices;track pay; let i = $index) {
        <div class="invoiceTableLayout" cdkDrag>
          <div class=" flex flex-col">
            <div class="flex items-center">
              <span class="material-icons cursor-pointer mr-3 !text-[20px]"
                cdkDragHandle>
                drag_indicator
              </span>
              <p
                class="text-text-primary text-text-md-regular  whitespace-pre-line">
                {{ pay?.description ?? '' }}
              </p>
            </div>
          </div>
          <p class="text-text-primary text-text-md-regular">
            ${{pay.rate | formatNumber}}
          </p>
          <div class="text-text-primary text-text-md-regular flex flex-col">
            <p>
              {{ pay.qty | decimal:2 | formatNumber}}
            </p>
            <p> {{ getNameTaxes(pay?.taxes, true) }}</p>
          </div>

          <p class="text-text-primary text-text-md-bold text-right">
            ${{calculateTotal(pay) | decimal:2 | formatNumber}}

          </p>
        </div>
        }
      </div>
    </div>

    <div class="w-full flex flex-col items-end mt-[16px]">
      <div class="flex justify-end items-start gap-[8px]">
        <p class="text-right text-text-primary text-text-md-regular">
          {{'ESTIMATE.ESTIMATE_FORM.Subtotal'|translate}}
        </p>
        <p
          class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
          ${{subtotal | decimal:2 | formatNumber}}
        </p>
      </div>
      <!--  list taxs -->
      @for(tax of taxArray; track tax ;let i=$index)
      {
      <div class="flex justify-end items-start gap-[8px] mb-2">
        <div class=" flex  flex-col pl-2">
          <p class="text-right text-text-primary text-text-sm-regular">
            {{tax.name}} ({{tax.amount}}%)
          </p>
          <p class="text-right text-text-primary text-text-sm-regular">
            #{{tax.numberTax}}
          </p>
        </div>

        <p
          class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
          ${{ tax.total | decimal:2 | formatNumber }}
        </p>
      </div>
      }
      <div class="flex justify-end items-start gap-[8px]">
        <p class="text-right text-text-primary text-text-md-regular">
          {{'ESTIMATE.ESTIMATE_FORM.Tax'|translate}}
        </p>
        <p
          class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
          ${{ CheckIsNaN(sumtax) | decimal:2 | formatNumber }}
        </p>
      </div>
      <div class="flex justify-end items-start gap-[8px]">
        <p class="text-right text-text-primary text-text-md-regular">
          {{'ESTIMATE.ESTIMATE_FORM.Discount'|translate}}
        </p>
        <p
          class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
          $0
        </p>
      </div>
      <div class="flex justify-end items-start gap-[8px]">
        <p class="text-right text-text-primary text-text-md-regular">
          {{'ESTIMATE.ESTIMATE_FORM.EstimateTotal'|translate}}
          ({{_storeService.curencyCompany | async}})
        </p>
        <p
          class="text-text-primary text-headline-md-bold text-right w-[160px] shrink-0">
          ${{ estimateInfo?.totalAmount | decimal:2 | formatNumber }}
        </p>
      </div>
    </div>

    <img class="invoiceDetailsFooter"
      src="../../../../assets/img/bg_footer_invoice_details.png" alt="Footer">
  </div>
</div>
