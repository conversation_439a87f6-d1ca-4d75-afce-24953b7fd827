﻿using InnoBook.Common;
using InnoBook.DTO.CoreModel;
using InnoBook.Entities;
using InnoBook.Extension;
using InnoLogiciel.Common;
using Syncfusion.DocIO;
using Syncfusion.DocIO.DLS;
using Syncfusion.DocIORenderer;
using Syncfusion.Drawing;
using Syncfusion.Pdf;
using System.Text.RegularExpressions;

class CellContentParameters
{
    public WTableCell CurrentCell { get; set; }
    public string Content { get; set; }
    public bool? IsBold { get; set; }
    public bool? IsItalic { get; set; }
    public bool? IsUnderline { get; set; }

    public string? Background { get; set; }

    public string? Color { get; set; }

    public string? Alignment { get; set; }

    // Constructor to initialize properties
    public CellContentParameters(WTableCell currentCell, string content, bool? isBold = null, bool? isItalic = null, bool? isUnderline = null, string? alignment = null, string? background = null, string? color = null)
    {
        CurrentCell = currentCell;
        Content = content;
        IsBold = isBold;
        IsItalic = isItalic;
        IsUnderline = isUnderline;
        Alignment = alignment;
        Background = background;
        Color = color;
    }
}

namespace InnoBook.Services
{
        public class PrintPDFService(IConfiguration configuration)
        {
            private WTableRow AddRowTable(WTable currentTable)
            {
                WTableRow newRow = currentTable.AddRow();
                var listCell = newRow.Cells;

                for (int i = 0; i < listCell.Count; i++)
                {
                    var cellItem = listCell[i];
                    cellItem.CellFormat.Borders.Top.LineWidth = 0;

                    cellItem.CellFormat.Borders.Bottom.LineWidth = 0.5f;
                    cellItem.CellFormat.Borders.Bottom.Color = ColorTranslator.FromHtml("#BFBFBF");

                    cellItem.CellFormat.BackColor = Color.Transparent;
                    cellItem.CellFormat.SamePaddingsAsTable = false;
                    cellItem.CellFormat.Paddings.Top = 10;
                    cellItem.CellFormat.Paddings.Bottom = 6;
                }
                return newRow;
            }

            private WTableCell AddCellToRowTable(CellContentParameters args)
            {
                var cellItem = args.CurrentCell;
                var isBold = args.IsBold ?? false;
                var isItalic = args.IsItalic ?? false;
                var isUnderline = args.IsUnderline ?? false;
                var background = args.Background ?? "";
                var color = args.Color ?? "black";

                var paragraph = cellItem.AddParagraph();
                var cellFormat = paragraph.AppendText(args.Content);
                paragraph.ParagraphFormat.HorizontalAlignment = args.Alignment switch
                {
                    "left" => HorizontalAlignment.Left,
                    "center" => HorizontalAlignment.Center,
                    "right" => HorizontalAlignment.Right,
                    _ => HorizontalAlignment.Left
                };

                cellFormat.CharacterFormat.Bold = isBold;
                cellFormat.CharacterFormat.Italic = isItalic;
                cellFormat.CharacterFormat.UnderlineStyle = isUnderline ? UnderlineStyle.Single : UnderlineStyle.None;
                cellFormat.CharacterFormat.TextColor = ColorTranslator.FromHtml(color);
                cellItem.CellFormat.BackColor = ColorTranslator.FromHtml(background);
                cellFormat.CharacterFormat.FontName = "Roboto";

                return cellItem;
            }


            private MemoryStream ConvertWordToPDF(WordDocument wordDocument) {
                using DocIORenderer render = new DocIORenderer();
                render.Settings.EmbedFonts = true;

                using PdfDocument pdfDocument = render.ConvertToPDF(wordDocument);
                MemoryStream stream = new MemoryStream();
                pdfDocument.Save(stream);
                stream.Position = 0;
                
                return stream;
            }

        static string FormatPhoneNumber(string number)
        {
            if (string.IsNullOrWhiteSpace(number)) return string.Empty;
            // Supprime tous les caractères non numériques
            string digits = Regex.Replace(number, @"\D", "");

            // Vérifie si le numéro contient exactement 10 chiffres
            if (digits.Length == 10)
            {
                return Regex.Replace(digits, @"(\d{3})(\d{3})(\d{4})", "($1) $2-$3");
            }

            return number;
        }

        public   MemoryStream PrintPDFInvoice(Invoice invoice,string userTimeZoneId) 
            {
                Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense("Mgo+DSMBMAY9C3t2XVhhQlJHfV5AQmBIYVp/TGpJfl96cVxMZVVBJAtUQF1hTH5Sdk1jUXpXcnZQRGJV");
                string? filename = "test.pdf";
                string? templatePath = Path.Combine(Directory.GetCurrentDirectory(), "WordTemplates", "InnoBooks_Invoice_.docx");

                var companyInfo = invoice.Company;
                string companyName = companyInfo?.BusinessName ?? "";
                string companyEmail = companyInfo.Email??"";
                string companyPhone = FormatPhoneNumber(companyInfo?.Phone) ?? "";
                string companyAddress = Utils.GetFullAddress(
                    companyInfo?.Adress ?? "",
                    companyInfo?.Adress2 ?? "",
                    companyInfo?.City ?? "",
                    companyInfo?.Province ?? "",
                    companyInfo?.PostalCode ?? "",
                    ""
                );
                string clientName = invoice.Client?.ClientName ?? "";
            string clientAddress = Utils.GetFullAddress(
                    invoice.Client?.AddressLine1 ?? "",
                    invoice.Client?.AddressLine2 ?? "",
                    invoice.Client?.TownCity ?? "",
                    invoice.Client?.StateProvince ?? "",
                    invoice.Client?.Country?? "",
                    ""
                );
                string invoiceNumber = invoice.InvoiceNumber ?? "";
                DateTime invoiceDateInTimeZone = invoice.InvoiceDate.ToUtcDate(userTimeZoneId);
                string invoiceDate = invoiceDateInTimeZone.ToString("yyyy-MM-dd") ?? "";
                DateTime dueDateInTimeZone = invoice.DueDate.ToUtcDate(userTimeZoneId);
                string dueDate = dueDateInTimeZone.ToString("yyyy-MM-dd") ?? "";
                string description = invoice.Notes ?? "";
                string subTotal = $"${Math.Truncate(invoice.PaidAmount * 100) / 100:N2}";
                string discount = "$0";
                 string amount = $"${Math.Truncate(Math.Round(invoice.TotalAmount * 100, 10)) / 100:N2}";
            string tax = $"${Math.Truncate(invoice.TaxAmount * 100) / 100:N2}";

            IDictionary<string, dynamic>? replaceText = new Dictionary<string, dynamic>
                {
                    { "${{company_name}}", companyName },
                    { "${{company_currency}}", companyInfo.Currency??"CAD" },
                    { "${{company_email}}", companyEmail },
                    { "${{company_phone}}", companyPhone },
                    { "${{company_address}}", companyAddress },
                    { "${{company_country}}", companyInfo?.Country ?? "" },
                    { "${{client_name}}", clientName },
                    { "${{client_address}}", clientAddress },
                    { "${{invoice_number}}", invoiceNumber },
                    { "${{issue_date}}", invoiceDate },
                    { "${{due_date}}", dueDate },
                    { "${{description}}", description },
                    { "${{txtdescription}}", description!=""?"Description":"" },
                    { "${{total}}", subTotal },
                    { "${{discount}}", discount },
                    { "${{amount}}", amount },
                    { "${{tax}}", tax },
                };

                dynamic replaceImage = null;
            if (companyInfo.CompanyImage != null)
            {

                DigitalOcean digitalOcean = new DigitalOcean();
                var localImagePath = digitalOcean.DownloadImageToTempAsync(configuration, companyInfo.Id.ToString(), companyInfo.CompanyImage).GetAwaiter().GetResult();
                replaceImage = new Dictionary<string, byte[]>
                    {
                        { "LOGO_COMPANY",localImagePath },
                    };
            }


            List<ItemInvoice> listItemInvoice = invoice.ItemInvoices ?? [];

                if (filename == null) throw new Exception("Missing filename");
                if (templatePath == null) throw new Exception("Missing templatePath");

            using FileStream docStream = new FileStream(templatePath, FileMode.Open, FileAccess.Read, FileShare.Write);
            using WordDocument wordDocument = new WordDocument(docStream, FormatType.Docx);
            // Replace the image in the document
            if (replaceImage != null)
            {
                foreach (WSection section in wordDocument.Sections)
                {
                    foreach (WTable table in section.Body.Tables)
                    {
                        foreach (WTableRow row in table.Rows)
                        {
                            foreach (WTableCell cell in row.Cells)
                            {
                                foreach (WParagraph paragraph in cell.Paragraphs)
                                {
                                    foreach (ParagraphItem item in paragraph.ChildEntities)
                                    {
                                        if (item is WPicture picture)
                                        {
                                            string altText = picture.AlternativeText.Trim();
                                            if (replaceImage.ContainsKey(altText))
                                            {
                                                var imgBytes = replaceImage[altText];
                                                picture.LoadImage(imgBytes);

                                                using var img = SixLabors.ImageSharp.Image.Load(imgBytes);

                                                float maxWidthPt = 80.6f;   // 1.55"
                                                float maxHeightPt = 74.88f;  // 1.04"

                                                double dpiX = img.Metadata.HorizontalResolution > 0 ? img.Metadata.HorizontalResolution : 72f;
                                                double dpiY = img.Metadata.VerticalResolution > 0 ? img.Metadata.VerticalResolution : 72f;

                                                double imgWidthPt = img.Width * 72f / dpiX;
                                                double imgHeightPt = img.Height * 72f / dpiY;

                                                double ratio = Math.Min(maxWidthPt / imgWidthPt, maxHeightPt / imgHeightPt);

                                                picture.Width = (int)(imgWidthPt * ratio);
                                                picture.Height = (int)(imgHeightPt * ratio);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Create a table and add it to the document (if needed)
            if (listItemInvoice?.Count > 0)
            {
                WSection section = wordDocument.Sections[0];
                WTable table = section.Tables[2] as WTable;
                if (table == null) throw new Exception("Table not found");
                // reduce white space above table if none description
                table.TableFormat.Paddings.Bottom = 0;
                if (description == "" || description == null)
                {
                    table.TableFormat.Paddings.Top = 0;
                    int index = -1;
                    for (int i = 0; i < section.Body.ChildEntities.Count; i++)
                    {
                        if (section.Body.ChildEntities[i] == table)
                        {
                            index = i;
                            break;
                        }
                    }

                    if (index > 0 && section.Body.ChildEntities[index - 1] is WParagraph paragraphAbove)
                    {
                        if (string.IsNullOrWhiteSpace(paragraphAbove.Text))
                        {
                            section.Body.ChildEntities.Remove(paragraphAbove);
                        }
                        paragraphAbove.ParagraphFormat.AfterSpacing = 0;
                        paragraphAbove.ParagraphFormat.BeforeSpacing = 0;
                        paragraphAbove.ParagraphFormat.LineSpacingRule = LineSpacingRule.Exactly;
                        paragraphAbove.ParagraphFormat.LineSpacing = 0.8f;

                    }
                    if (index < section.Body.ChildEntities.Count - 1 && section.Body.ChildEntities[index + 1] is WParagraph paragraphBelow)
                    {

                        paragraphBelow.ParagraphFormat.BeforeSpacing = 0;
                        paragraphBelow.ParagraphFormat.AfterSpacing = 0;
                        paragraphBelow.ParagraphFormat.LineSpacingRule = LineSpacingRule.Exactly;
                        paragraphBelow.ParagraphFormat.LineSpacing = 0.8f;
                    }

                }

                for (int indexRow = 0; indexRow < listItemInvoice.Count; indexRow++)
                {
                    var itemInvoiceItem = listItemInvoice[indexRow];
                    WTableRow rowTable = AddRowTable(table);
                    string serviceNamePart = string.IsNullOrEmpty(itemInvoiceItem?.Service?.ServiceName) ? "" : " - " + itemInvoiceItem?.Service?.ServiceName;
                    string cellContent = $"{itemInvoiceItem.description}";

                    AddCellToRowTable(new CellContentParameters(
                            currentCell: rowTable.Cells[0],
                            content: cellContent ?? "",
                            isBold: false
                        ));
                    AddCellToRowTable(new CellContentParameters(
                        currentCell: rowTable.Cells[1],
                        content: $"${(itemInvoiceItem.rate ?? 0).ToString("F2")}" + Environment.NewLine +
                        $"{InvoiceHelper.GetTaxLabel(itemInvoiceItem.Taxes.Select(x => x.CompanyTax).ToList() ?? [])}",
                        isBold: false

                    ));
                    AddCellToRowTable(new CellContentParameters(
                        currentCell: rowTable.Cells[2],
                        content: $"{(itemInvoiceItem.qty ?? 0).ToString("F2")}",
                        isBold: false
                    ));
                    //AddCellToRowTable(new CellContentParameters(
                    //currentCell: rowTable.Cells[3],
                    //content: $"{InvoiceHelper.GetTaxLabel(itemInvoiceItem.Taxes.Select(x => x.CompanyTax).ToList() ?? [])}"
                    //));
                    AddCellToRowTable(new CellContentParameters(
                        currentCell: rowTable.Cells[3],
                        content: $"${(Math.Truncate((itemInvoiceItem.qty ?? 0) * 100) / 100 *
                        Math.Truncate((itemInvoiceItem.rate ?? 0) * 100) / 100):N2}",
                        isBold: true,
                        alignment: "right"
                    ));
                }
                // handle tax
                List<TaxItem> taxArray = new List<TaxItem>();
                for (int indexRow = 0; indexRow < listItemInvoice.Count; indexRow++)
                {
                    if (listItemInvoice[indexRow].Taxes.Count() > 0)
                    {
                        for (int indexRowTax = 0; indexRowTax < listItemInvoice[indexRow].Taxes.Count; indexRowTax++)
                        {
                            var taxData = listItemInvoice[indexRow].Taxes[indexRowTax].CompanyTax;

                            decimal taxAmount = (decimal)(listItemInvoice[indexRow].rate * listItemInvoice[indexRow].qty * (taxData.Amount / 100));

                            var existingTax = taxArray.FirstOrDefault(t => t.Name == taxData.Name);

                            if (existingTax != null)
                            {
                                existingTax.Total += taxAmount;
                                existingTax.Amount = (decimal)taxData.Amount;
                            }
                            else
                            {
                                taxArray.Add(new TaxItem
                                {
                                    Name = taxData.Name,
                                    Total = taxAmount,
                                    TaxeNumber = taxData.TaxeNumber,
                                    Amount = (decimal)taxData.Amount
                                });
                            }
                        }
                    }
                }
                if (taxArray.Count() > 0)
                {
                    taxArray.Reverse();
                    foreach (var item in taxArray)
                    {
                        WTable tabletax = section.Tables[3] as WTable;
                        if (tabletax == null) throw new Exception("Table not found");
                        if (tabletax.Rows.Count > 0)
                        {
                            WTableRow newRow = new WTableRow(section.Document);

                            WTableCell nameCell = new WTableCell(section.Document);
                            newRow.Cells.Add(nameCell);
                            WParagraph paragraph = new WParagraph(section.Document);

                            paragraph.ParagraphFormat.HorizontalAlignment = HorizontalAlignment.Right;
                            paragraph.ParagraphFormat.LineSpacingRule = LineSpacingRule.AtLeast;
                            paragraph.ParagraphFormat.LineSpacing = 12f;
                            paragraph.ParagraphFormat.BeforeSpacing = 0f;
                            paragraph.ParagraphFormat.AfterSpacing = 0f;

                            string content = $"{item.Name + $"( {item.Amount})"}%\n(#{item.TaxeNumber})";

                            string[] lines = content.Split('\n');
                            for (int i = 0; i < lines.Length; i++)
                            {
                                paragraph.AppendText(lines[i]);
                                if (i < lines.Length - 1)
                                {
                                    paragraph.AppendBreak(BreakType.LineBreak);
                                }
                            }

                            nameCell.ChildEntities.Add(paragraph);
                            //AddCellToRowTable(new CellContentParameters(
                            //    currentCell: nameCell,
                            //    content: $"{item.Name+$"( {item.Amount})"+"%"}\n(#{item.TaxeNumber})",
                            //    alignment: "right"
                            //));

                            WTableCell valueCell = new WTableCell(section.Document);
                            newRow.Cells.Add(valueCell);
                            string number = $"${Math.Floor(item.Total.Value * 100) / 100:N2}";

                            int totalWidth = 22;
                            int spaceCount = Math.Max(0, totalWidth - number.Length);

                            string contentWithSpaces = new string(' ', spaceCount) + number;
                            valueCell.AddParagraph().AppendText(contentWithSpaces);
                            if (valueCell.Paragraphs.Count > 0)
                            {
                                WParagraph para = valueCell.Paragraphs[0];
                                para.ParagraphFormat.BeforeSpacing = 10f;
                                valueCell.ChildEntities.Add(para);
                            }
                            if (nameCell.Paragraphs.Count > 0)
                            {
                                WParagraph para = nameCell.Paragraphs[0];
                                para.ParagraphFormat.BeforeSpacing = 10f;
                                nameCell.ChildEntities.Add(para);
                            }
                            tabletax.Rows.Insert(1, newRow);
                        }
                    }

                }
            }

            // Replace the text in the word document
            if (replaceText != null)
            {
                // Replace the text in the word document
                foreach (var replaceItem in replaceText)
                {
                    wordDocument.Replace(replaceItem.Key, replaceItem.Value, true, true);
                }
            }

            return ConvertWordToPDF(wordDocument);
        }
        }
}