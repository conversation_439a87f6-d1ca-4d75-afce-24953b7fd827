{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"InnoLogicielContext": "Host=localhost;Port=5432;Database=TestInnoBook;Username=inno;Password=******;"}, "ActivateAccountUrl": "https://test.innobooks.net/login", "NavigationInvite": "https://test.innobooks.net/navigation-invite", "Invoice": "https://test.innobooks.net", "ResetPasswordUrl": "https://test.innobooks.net/reset-password", "InviteUrlMember": "https://test.innobooks.net/activate-invite", "ENCRYPTION_KEY": "t79G3J4MjzqeW7dxywtpGH3q3YLsTJpKz2V8TR8IfIgpgqLBzpC6vBwdzu6XQC34", "Jwt": {"SecretKey": "t79G3J4MjzqeW7dxywtpGH3q3YLsTJpKz2V8TR8IfIgpgqLBzpC6vBwdzu6XQC34", "Issuer": "https://127.0.0.1:4300", "Audience": "https://127.0.0.1:4300"}, "Stripe": {"ApiKey": "STRIPE_API_KEY", "ApiSecret": "STRIPE_SECRET_KEY", "WebhookSecret": "WEBHOOK_SECRET_KEY", "RedirectUrl": "https://test.innobooks.net/billing"}, "AllowedHosts": "*"}