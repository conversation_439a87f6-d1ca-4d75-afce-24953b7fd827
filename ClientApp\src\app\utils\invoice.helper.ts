import { AddTax } from 'app/dto/interface/addTax.interface';
import { Tax } from 'app/dto/interface/tax.interface';

export const calculateTimeTrackingAmount = (time: string, rate: number) => {
  if (!time || !rate) return 0;
  const parts = time.split(":").map(Number);
  let hours = 0, minutes = 0, seconds = 0;
  if (parts.length === 3) {
    [hours, minutes, seconds] = parts; // HH:MM:SS
  } else if (parts.length === 2) {
    [hours, minutes] = parts; // HH:MM (default seconds to 0)
  } else if (parts.length === 1) {
    [hours] = parts; // HH (default minutes and seconds to 0)
  }
  const decimalHours = hours + minutes / 60 + seconds / 3600;
  return Number((decimalHours * rate).toFixed(2));
}

export const getNameTaxes = (taxes: Tax[] | AddTax[] = [], includeAmount: boolean = false): string => {
  const selectedTaxes = taxes.some((x: any) => x.companyTax || x.selected || x.selected === undefined)
    ? taxes
    : (taxes as Tax[]).filter(x => x.selected);

  return selectedTaxes
    .map((x: any) => {
      let name, amount;
      if (x.companyTax) {
        name = x.companyTax.name;
        amount = x.companyTax.amount;
      } else {
        name = x.name;
        amount = x.amount;
      }
      return includeAmount ? `${name} (${amount}%)` : name;
    })
    .filter(Boolean)
    .sort((a, b) => a.localeCompare(b))
    .join(', ');
};

export const calculateTotalInvoiceItem = (
  rate?: number,
  qty?: number
) => {
  if (!rate || !qty) return 0;
  return Number((rate * qty).toFixed(2));
};

export const calculateInvoiceTotal = (items: any[]) => {
  const { subtotal, grandTotalTax } = calculateGroupedTaxes(items);
  return Number((subtotal + grandTotalTax).toFixed(2));
};

export const calculateSubtotal = (items: any[]) => {
  return items.reduce((acc, item) => {
    const { rate, qty } = item;
    const amount = rate && qty ? Number((rate * qty).toFixed(2)) : 0;
    return acc + amount;
  }, 0);
};

export const calculateGroupedTaxes = (items: any[]) => {
  let subtotal = 0;
  let taxTotals: Record<string, {
    name: string;
    numberTax: string;
    amount: number;
    taxableAmount: number;
    total: number;
  }> = {};

  items.forEach(({ rate, qty, taxes }) => {
    if (!rate || !qty) return;
    const itemAmount = Number((rate * qty).toFixed(2));
    subtotal += itemAmount;

    if (!taxes || taxes.length === 0) return;

    const hasCompanyTax = taxes.some((t) => t.companyTaxId);
    const selectedTaxes = hasCompanyTax
      ? taxes.filter((t) => t.companyTaxId)
      : taxes.filter((t) => t.selected);

    selectedTaxes.forEach((tax) => {
      const name = tax.name || "Unknown Tax";
      const numberTax = tax.taxeNumber || "";
      const rate = Number(tax?.companyTax?.amount ?? tax.amount ?? 0);

      if (!taxTotals[name]) {
        taxTotals[name] = {
          name,
          numberTax,
          amount: rate,
          taxableAmount: 0,
          total: 0,
        };
      }

      taxTotals[name].taxableAmount += itemAmount;
    });
  });

  let grandTotalTax = 0;
  Object.values(taxTotals).forEach((tax) => {
    tax.total = Number((tax.taxableAmount * (tax.amount / 100)).toFixed(2));
    grandTotalTax += tax.total;
  });

  return {
    subtotal: Number(subtotal.toFixed(2)),
    totalTaxes: taxTotals,
    grandTotalTax: Number(grandTotalTax.toFixed(2)),
  };
};

export const calculateExpenses  = (items: any[]) => {
  let subtotal = 0;
  let taxTotals: Record<string, {
    name: string;
    numberTax: string;
    amount: number;
    taxableAmount: number;
    total: number;
  }> = {};

  items.forEach(({ total, taxes }) => {
    if (!total) return;
    subtotal += total;

    if (!taxes || taxes.length === 0) return;

    const hasCompanyTax = taxes.some((t) => t.companyTaxId);
    const selectedTaxes = hasCompanyTax
      ? taxes.filter((t) => t.companyTaxId)
      : taxes.filter((t) => t.selected);

    selectedTaxes.forEach((tax) => {
      const name = tax.name || "Unknown Tax";
      const numberTax = tax.taxeNumber || "";
      const rate = Number(tax?.companyTax?.amount ?? tax.amount ?? 0);

      if (!taxTotals[name]) {
        taxTotals[name] = {
          name,
          numberTax,
          amount: rate,
          taxableAmount: 0,
          total: 0,
        };
      }

      taxTotals[name].taxableAmount += total;
    });
  });

  let grandTotalTax = 0;
  Object.values(taxTotals).forEach((tax) => {
    tax.total = Number((tax.taxableAmount * (tax.amount / 100)).toFixed(2));
    grandTotalTax += tax.total;
  });

  return {
    subtotal: Number(subtotal.toFixed(2)),
    totalTaxes: taxTotals,
    grandTotalTax: Number(grandTotalTax.toFixed(2)),
  };
};
