import { environment } from 'environments/environment';
import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

const UrlApi = environment.HOST_API + "/api";

@Injectable({
  providedIn: 'root'
})
export class PlanService {
  private http = inject(HttpClient);

  constructor() { }

  getAllPlans(): Observable<any> {
    return this.http.get(UrlApi + '/Plan/GetAllPlans');
  }

  getCurrentPlan(): Observable<any> {
    return this.http.get(UrlApi + '/Plan/GetCurrentPlan');
  }

  calculatePlanPrice(planId: string, billingInterval: string = 'month', additionalUsers: number = 0): Observable<any> {
    return this.http.get(UrlApi + `/Plan/CalculatePlanPrice?planId=${planId}&billingInterval=${billingInterval}&additionalUsers=${additionalUsers}`);
  }
}
