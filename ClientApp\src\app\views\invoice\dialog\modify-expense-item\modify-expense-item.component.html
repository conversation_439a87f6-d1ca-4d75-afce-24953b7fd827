<app-inno-modal-wrapper
    [title]="data ? 'EXPENSES.DescriptionEditExpensesItem' : 'EXPENSES.NewExpensesItem'"
    (onClose)="handleClose()">
    <form [formGroup]="invoiceItemForm">
        <div class="w-full flex flex-col gap-[16px] px-[16px] pb-[16px]">
            <app-inno-form-input
                [label]="'EXPENSES.NEW_ADD_FORM.Description' | translate"
                [placeholder]="'EXPENSES.NEW_ADD_FORM.Description' | translate"
                [formControl]="f['description']"
                [value]="f['description'].value" />

            <app-inno-form-input
                type="number"
                [label]="'EXPENSES.NEW_ADD_FORM.Rate' | translate "
                placeholder="0.00"
                [formControl]="f['rate']"
                [value]="f['rate'].value"
                [errorMessages]="{ required: 'EXPENSES.NEW_ADD_FORM.ValidationRateRequired' | translate }" />

            <app-inno-form-input
                type="number"
                [label]="'EXPENSES.NEW_ADD_FORM.Quantity' | translate "
                [placeholder]="'EXPENSES.NEW_ADD_FORM.Quantity' | translate "
                [formControl]="f['qty']"
                [value]="f['qty'].value"
                [errorMessages]="{ required: 'EXPENSES.NEW_ADD_FORM.ValidationQuantityRequired' | translate }" />

            @if(data)
            {
            @for(tax of data.taxes;track tax;let i=$index)
            {
            <app-inno-form-input
                (onChange)="onChangeTax($event,i)"
                [label]="tax.companyTax.name"
                placeholder="0.00 "
                [value]="calculateTotalTax(tax.companyTax.amount)"
                [errorMessages]="{ required: 'EXPENSES.NEW_ADD_FORM.ValidationQuantityRequired' | translate }" />
            }
            }

            @if(listTaxName?.length) {
            <div class="w-full flex flex-col relative">
                <label
                    class="text-text-secondary text-text-sm-semibold mb-[2px]">Taxes</label>
                <p
                    class="text-text-primary text-text-sm-bold">
                    {{ listTaxName }}
                </p>
            </div>
            }

            <div class="w-full flex flex-col relative">
                <label
                    class="text-text-secondary text-text-sm-semibold mb-[2px]">Total</label>
                <p class="text-text-primary text-headline-md-bold">
                    ${{ total || 0 | decimal:2 | formatNumber }}
                </p>
            </div>

            <button class="button-link-primary" (click)="handleModifyTaxes()">
                {{'EXPENSES.NEW_ADD_FORM.AddUpdateTaxes'|translate}}
            </button>

        </div>
        <app-inno-modal-footer
            (onCancel)="handleCancel()"
            (onSubmit)="handleSubmit()" />
    </form>
</app-inno-modal-wrapper>
