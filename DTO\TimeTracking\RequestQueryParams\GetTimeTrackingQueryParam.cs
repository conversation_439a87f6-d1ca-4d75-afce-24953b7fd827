﻿using InnoBook.DTO.CoreModel;

namespace InnoBook.DTO.TimeTracking
{
    public class GetTimeTrackingQueryParam : PaginatedRequest
    {
        public string? TimeZone { get; set; }
        public string? FilterDate { get; set; }
        public string? Loggedby { get; set; }
        public Guid? ClientId { get; set; }
        public string? StartDate { get; set; }
        public string? EndDate { get; set; }
        public bool? IsUnBill { get; set; }
    }
}
