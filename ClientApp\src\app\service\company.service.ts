import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { Company } from '../dto/interface/company.interface';
import { Observable } from 'rxjs';
import { ICurrency } from 'app/dto/interface/Currency.interface';

const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class CompanyService {
  private http = inject(HttpClient)
  constructor() { }

  CreateCompany(payload: any): Observable<Company> {
    return this.http.post<Company>(UrlApi + '/Company', payload);
  }
  UpdateCompany(payload: any): Observable<Company> {
    return this.http.put<Company>(UrlApi + '/Company', payload);
  }
  RemoveImgCompany(): Observable<Company> {
    return this.http.put<Company>(UrlApi + '/Company/RemoveImgCompany', null);
  }
  UpdateFinancial(payload: any) {
    return this.http.post(UrlApi + '/Company/UpdateFinancial', payload);
  }

  GetCurrencyCompany(): Observable<ICurrency> {
    return this.http.get<ICurrency>(UrlApi + `/Company/GetCurrencyCompany?`);
  }
  GetInforCompany(): Observable<ICurrency> {
    return this.http.get<ICurrency>(UrlApi + `/Company/GetInforCompany`);
  }
}
