using InnoLogiciel.Server.Contexts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Newtonsoft.Json;
using System.Security.Claims;

namespace InnoBook.Attributes
{
    [AttributeUsage(AttributeTargets.Method, AllowMultiple = false)]
    public class OwnershipAttribute : Attribute, IAuthorizationFilter
    {
        private readonly Type _entityType;

        public OwnershipAttribute(Type entityType)
        {
            _entityType = entityType;
        }

        public void OnAuthorization(AuthorizationFilterContext context)
        {
            try
            {
                var userId = context.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                if (string.IsNullOrEmpty(userId))
                {
                    context.Result = new UnauthorizedResult();
                    return;
                }

                if (context.HttpContext.Request.Method == "PUT" || context.HttpContext.Request.Method == "DELETE")
                {
                    context.HttpContext.Request.EnableBuffering();
                    string body;
                    using (var reader = new StreamReader(
                            context.HttpContext.Request.Body,
                            encoding: System.Text.Encoding.UTF8,
                            detectEncodingFromByteOrderMarks: false,
                            bufferSize: 1024,
                            leaveOpen: true))
                    {
                        body = reader.ReadToEndAsync().Result;
                    }

                    // Reset the body position to 0
                    if (context.HttpContext.Request.Body.CanSeek)
                    {
                        context.HttpContext.Request.Body.Position = 0;
                    }
                    else
                    {
                        context.Result = new BadRequestObjectResult("Invalid request body format."); // Stream is not seekable
                        return;
                    }

                    if (string.IsNullOrEmpty(body))
                    {
                        context.Result = new BadRequestObjectResult("Request body is empty.");
                        return;
                    }


                    List<string> listId = new List<string>();
                    // Update object
                    if (context.HttpContext.Request.Method == "PUT")
                    {
                        // Put method
                        var id = JsonConvert.DeserializeObject<dynamic>(body)?.id;
                        if (id == null)
                        {
                            context.Result = new BadRequestObjectResult("Invalid request body format.");
                            return;
                        }
                        listId.Add(id.ToString());
                    }
                    else
                    {
                        // Delete method
                        if (body.Contains("[") && body.Contains("]"))
                        {
                            var ids = JsonConvert.DeserializeObject<List<string>>(body);
                            foreach (var id in ids)
                            {
                                listId.Add(id.ToString());
                            }
                        }
                        else
                        {
                            listId.Add(body.ToString());
                        }
                    }

                    var dbContext = context.HttpContext.RequestServices.GetService<InnoLogicielContext>();

                    // Find the entity type by CLR type name
                    IEntityType? entityType = dbContext.Model.GetEntityTypes()
                                                            .FirstOrDefault(e => e.ClrType.Name.Equals(_entityType.Name, StringComparison.OrdinalIgnoreCase));

                    string tableName = entityType?.GetTableName() ?? "";
                    if(string.IsNullOrEmpty(tableName))
                    {
                        context.Result = new BadRequestObjectResult("Invalid table namee.");
                        return;
                    }

                    var query = $@"SELECT count(0) FROM {tableName}
                                   WHERE id IN ({string.Join(",", listId.Select(id => $"'{id}'"))})
                                   and created_by = '{userId}'";

                    var res = dbContext.Database.SqlQueryRaw<int>(query).First();
                    if (res != listId.Count)
                    {
                        context.Result = new ForbidResult("You can only update your record");
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.Write("Invalid verify ownership" + ex.ToString());
            }
        }
    }
}
