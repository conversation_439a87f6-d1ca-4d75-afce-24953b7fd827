﻿using InnoBook.Attributes;
using InnoBook.DTO.CoreModel;
using InnoBook.Request.RequestPayment;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class PaymentController(IPaymentService paymentService, IHttpContextAccessor httpContextAccessor) : BaseController(httpContextAccessor)
    {
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("GetAllPayment")]
        public async Task<ActionResult<PaginatedResponse<IActionResult>>> GetAllPayment([FromQuery] PaginatedRequest query)
        {
            var payment = await paymentService.GetAllPayment(query, IdCompany);
            return Ok(payment);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("GetAllPaymentCompany")]
        public async Task<ActionResult<PaginatedResponse<IActionResult>>> GetAllPaymentCompany([FromQuery] PaginatedRequest query)
        {
            var payment = await paymentService.GetAllPaymentCompany(query, IdCompany);
            return Ok(payment);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("CreatedPayment")]
        public async Task<ActionResult> CreatedPayment(RequestPayment payment)
        {
            payment.CompanyId = Guid.Parse(IdCompany);
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var data = await paymentService.CreatedPayment(payment, IdUser);

            return Ok(data);
        }

    }
}
