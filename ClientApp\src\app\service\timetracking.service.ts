import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { ClientAndProjectQueryParam, Parameter, TimeTrackingQueryParam } from 'app/dto/interface/queryParameter.interface';
import { environment } from 'environments/environment';
import { Observable } from 'rxjs';
import { TimeTracking } from '../dto/interface/timeTracking.interface';
import { PaginatedResponse } from '../dto/interface/paginatedResponse.interface';
import { Client } from '../dto/interface/client.interface';
import { Member } from '../dto/interface/member.interface';
import { currentTimeZone, formatParamsQuery } from 'app/helpers/common.helper';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class TimetrackingService {

  private http = inject(HttpClient)
  constructor() { }

  CreateTimeTracking(payload: TimeTracking) {
    return this.http.post(UrlApi + `/TimeTracking/CreateTimeTracking`, payload);
  }

  GetAllTimeTracking(params: TimeTrackingQueryParam): Observable<PaginatedResponse<TimeTracking>> {
    const query = formatParamsQuery(params);
    return this.http.get<PaginatedResponse<TimeTracking>>(UrlApi + `/TimeTracking/GetAllTimeTracking`, { params: query });
  }

  GetProjectAndClientService(params: ClientAndProjectQueryParam): Observable<PaginatedResponse<Client>> {
    const query = formatParamsQuery(params);
    return this.http.get<PaginatedResponse<Client>>(UrlApi + `/TimeTracking/GetProjectAndClientService`, { params: query });
  }
  CalculationTotalTimeByUser(params: TimeTrackingQueryParam): Observable<string> {
    const query = formatParamsQuery(params);
    return this.http.get<string>(UrlApi + `/TimeTracking/CalculationTotalTimeByUser`, { params: query });
  }


  DeleteTimeTracking(payload: string[]): Observable<boolean> {
    return this.http.delete<boolean>(UrlApi + '/TimeTracking/DeleteTimeTracking', { body: payload });
  }

  UpdateTimeTracking(payload: TimeTracking): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + '/TimeTracking/UpdateTimeTracking', payload);
  }
}
