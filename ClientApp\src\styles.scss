@import "../node_modules/@syncfusion/ej2-angular-base/styles/material.css";
@import "./variables.scss";
@import "../node_modules/@syncfusion/ej2-buttons/styles/material.css";
@import "../node_modules/@syncfusion/ej2-inputs/styles/material.css";
@import "../node_modules/@syncfusion/ej2-popups/styles/material.css";
@import "../node_modules/@syncfusion/ej2-lists/styles/material.css";
@import "../node_modules/@syncfusion/ej2-splitbuttons/styles/material.css";
@import "../node_modules/@syncfusion/ej2-calendars/styles/material.css";
@import "../node_modules/@syncfusion/ej2-angular-calendars/styles/material.css";

// tailwind css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    // TEXT COLORS
    --text-primary: #101114;
    --text-secondary: #414246;
    --text-tertiary: #87888A;
    --text-placeholder: #87888A;
    --text-placeholder-slight: #BFC0C1;
    --text-disabled: #87888A;
    --text-white: #FFFFFF;
    --text-black: #000000;
    --text-informative: #537A52;
    --text-success: #067757;
    --text-danger: #D73A2F;
    --text-warning: #D86D15;
    --text-brand-tertiary: #A6BCA6;
    --text-brand-primary: #537A52;
    --text-brand-secondary: #4A6D49;

    // BACKGROUND COLORS
    --bg-overlay-brand: #ECF2EC;
    --bg-overlay-dark: #00000080;
    --bg-overlay-light: #FFFFFF80;
    --bg-primary: #FFFFFF;
    --bg-primary-fixed: #FFFFFF;
    --bg-primary-hover: #F7F7F8;
    --bg-primary-dark: #101114;
    --bg-secondary: #F7F7F8;
    --bg-secondary-hover: #E9EAEB;
    --bg-secondary-subtle: #F7F7F8;
    --bg-secondary-dark: #414246;
    --bg-danger-primary: #FCF3F2;
    --bg-danger-secondary: #FAE8E6;
    --bg-danger-strong: #D73A2F;
    --bg-danger-strong-hover: #B73129;
    --bg-success-primary: #F0FEFA;
    --bg-success-secondary: #E2FAF3;
    --bg-success-strong: #067757;
    --bg-warning-primary: #FDF5EC;
    --bg-warning-secondary: #FDE6D2;
    --bg-warning-strong: #D86D15;
    --bg-tertiary: #E9EAEB;
    --bg-selected: #E9EAEB;
    --bg-disabled: #E9EAEB;
    --bg-disabled-slight: #F7F7F8;
    --bg-brand-primary: #F6F9F6;
    --bg-brand-secondary: #C9D8C9;
    --bg-brand-strong: #537A52;
    --bg-brand-strong-hover: #4A6D49;

    // OBJECT COLORS
    --object-primary: #101114;
    --object-white: #FFFFFF;
    --object-secondary: #333438;
    --object-disabled: #87888A;
    --object-disabled-slight: #BFC0C1;
    --object-tertiary: #414246;
    --object-quaternary: #87888A;
    --object-contrast-black: #00000033;
    --object-contrast-white: #FFFFFF33;
    --object-brand-primary: #537A52;
    --object-brand-secondary: #81A081;
    --object-danger-primary: #D73A2F;
    --object-danger-secondary: #E06057;
    --object-success-primary: #067757;
    --object-success-secondary: #089A71;
    --object-warning-primary: #D86D15;
    --object-warning-secondary: #F37F20;

    // BORDER COLORS
    --border-primary: #E9EAEB;
    --border-primary-slight: #F7F7F8;
    --border-secondary: #BFC0C1;
    --border-disabled: #F7F7F8;
    --border-dark: #17181C;
    --border-duotone: #00000066;
    --border-white: #FFFFFF;
    --border-tertiary: #87888A;
    --border-brand: #537A52;
    --border-brand-secondary: #C9D8C9;
    --border-in-tertiary: #4A6D49;
    --border-danger: #D73A2F;
    --border-danger-slight: #FAE8E6;
    --border-success: #067757;
    --border-success-slight: #E2FAF3;
    --border-warning: #D86D15;
    --border-warning-slight: #FDE6D2;
  }

  input,
  textarea,
  select,
  button {
    outline: none;
  }
}

@layer utilities {

  /* **************** COMMON **************** */
  .css-container-full {
    @apply container;
    max-width: unset;
  }

  /* **************** BUTTON **************** */
  // Base button
  .css-base-button {
    border-radius: 8px;
    cursor: pointer;
    padding: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all .3s;

    &:disabled {
      pointer-events: none;
      @apply text-text-disabled;
      @apply bg-bg-disabled;
      // cursor: not-allowed;
      // opacity: 0.5;
    }
  }

  // Size button
  .css-button-md {
    height: 40px !important;
    padding: 10px 12px !important;
    @apply text-text-sm-semibold;

    img {
      width: 16px;
    }
  }

  // Variant button
  .css-button-primary {
    @apply css-base-button;
    @apply bg-object-brand-primary;
    @apply text-text-white;

    &:hover {
      @apply bg-bg-brand-strong-hover;
    }
  }

  .css-button-transparent {
    @apply css-base-button;
    @apply bg-transparent;
    @apply text-text-primary;

    &:hover {
      @apply bg-bg-secondary;
    }
  }

  .css-button-outline {
    border: 1px solid;
    @apply shadow-sm;
    @apply css-base-button;
    @apply border-border-primary;
    @apply bg-bg-primary;
    @apply text-text-secondary;


    &:hover {
      @apply bg-bg-secondary;
    }
  }

  .css-button-outline-primary {
    border: 1px solid;
    @apply shadow-sm;
    @apply css-base-button;
    @apply border-border-brand;
    @apply bg-bg-brand-primary;
    @apply text-text-brand-primary;


    &:hover {
      @apply bg-bg-brand-secondary;
    }
  }

  .css-button-link-danger {
    background-color: transparent;
    @apply css-base-button;
    @apply text-text-danger;
    @apply text-text-sm-semibold;
  }

  .css-button-link-primary {
    background-color: transparent;
    @apply css-base-button;
    @apply text-text-brand-primary;
    @apply text-text-sm-semibold;
    padding: 0 !important;
  }

  .css-button-danger {
    @apply css-base-button;
    @apply bg-object-danger-primary;
    @apply text-text-white;

    &:hover {
      @apply bg-bg-danger-strong-hover;
    }
  }

  .css-button-icon {
    @apply css-base-button;
    @apply bg-bg-primary;
    width: 32px;
    height: 32px;
    padding: 6px;

    img {
      width: 20px;
    }

    &:hover {
      @apply bg-bg-secondary
    }
  }

  /* **************** FORM **************** */
  // Filter dropdown
  .css-dropdown-invisible {
    cursor: pointer;
    padding: 8px 6px;
    border-radius: 8px;
    display: flex;
    cursor: pointer;
    align-items: center;
    gap: 12px;
    @apply bg-bg-primary;
    @apply text-text-sm-semibold;
    @apply text-text-primary;

    img {
      width: 16px;
    }

    &:hover {
      @apply bg-bg-secondary
    }
  }

  .css-dropdown-md {
    height: 40px;
    border: 2px solid;
    padding: 8px 12px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    @apply bg-bg-primary;
    @apply border-border-primary;
    @apply text-text-md-regular;
    @apply text-text-primary;

    img {
      width: 24px;
    }
  }

  .css-dropdown-sm {
    border: 2px solid;
    padding: 6px 8px;
    border-radius: 8px;
    cursor: pointer;
    @apply bg-bg-primary;
    @apply border-border-primary;
    @apply text-text-sm-regular;
    @apply text-text-primary;
  }
}

// CUSTOM MAT RADIO
.customRadio .mdc-radio {
  padding: 0 !important;
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.customRadio .mdc-label {
  padding-left: 0;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  @apply text-text-primary;
}

.customRadio .mdc-form-field {
  gap: 8px;
}

.customRadio .mat-mdc-radio-touch-target,
.customRadio .mat-mdc-radio-button .mat-radio-ripple {
  display: none !important;
}

.customRadio .mdc-radio .mdc-radio__native-control {
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

.customRadio .mdc-radio__background::before {
  display: none !important;
}

.customRadio .mdc-radio .mdc-radio__native-control:enabled:not(:checked)+.mdc-radio__background .mdc-radio__outer-circle {
  border: 1px solid #BFC0C1 !important;
}

.customRadio .mdc-radio .mdc-radio__native-control:enabled:checked+.mdc-radio__background .mdc-radio__outer-circle {
  border-color: #537A52 !important;
  border-width: 6px !important;
}

.customRadio .mdc-radio__background {
  width: 100%;
  height: 100%;
}

.customRadio .mdc-radio .mdc-radio__native-control:enabled+.mdc-radio__background .mdc-radio__inner-circle {
  border-color: white !important;
  transform: scale(0.4) !important;
}

/**************** CUSTOM EJS CSS ****************/
// Custom date picker
.customDatePickerV2 .e-input {
  padding: 0 !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  color: #101114 !important;
}

.customDatePickerV2 .e-input::selection,
.customDatePickerV2 .e-date-wrapper::before,
.customDatePickerV2 .e-date-wrapper::after,
.e-datepicker .e-focused-date.e-selected .e-day,
.e-datepicker .e-cell.e-selected .e-day {
  background-color: #537A52 !important;
  color: white !important;
}

.e-datepicker .e-today .e-day {
  border: 1px solid #537A52 !important;
  color: #537A52 !important;
}

.customDatePickerV2 .e-date-wrapper .e-icons.e-active,
.customDatePickerV2 .e-input,
.e-datepicker .e-today:not(.e-focused-date) .e-day,
.e-datepicker .e-today {
  color: #101114 !important;
}

// Custom schedule
.customSchedule {
  min-width: 1000px;
}

.customSchedule .e-header-cells {
  height: 40px !important;
  text-align: center !important;
  text-transform: uppercase !important;
  color: #87888A !important;
  font-weight: 700 !important;
  border-color: transparent !important;
  border-bottom-color: #F7F7F8 !important;
}

.customSchedule .e-work-cells {
  border-color: #E9EAEB !important;
}

.customSchedule .e-date-header {
  margin-left: auto !important;
  position: relative;
  @apply text-text-sm-bold;
  @apply text-text-tertiary;
}

.customSchedule .customScheduleCell {
  position: relative;
  height: 50px;
  padding-top: 10px
}

.customSchedule .customScheduleCell .textHour {
  @apply text-text-primary;
  @apply text-headline-sm-bold;
  text-align: center;
  margin-bottom: 0;
}

.customSchedule .customScheduleCell .textDescription {
  @apply text-text-primary;
  @apply text-text-xs-regular;
  text-align: center;
  margin-bottom: 0;
}

.customSchedule .e-work-cells:hover,
.customSchedule .e-work-cells.e-selected-cell {
  outline: 1px solid #537A52;
  background-color: #F6F9F6 !important;
  border-color: transparent !important;
}

.customSchedule .e-work-cells:hover .textHour,
.customSchedule .e-work-cells:hover .textDescription,
.customSchedule .e-work-cells.e-selected-cell .textHour,
.customSchedule .e-work-cells.e-selected-cell .textDescription {
  color: #537A52 !important;
}

.customSchedule .e-month-view .e-current-date .e-date-header,
.customSchedule .e-month-agenda-view .e-current-date .e-date-header {
  background-color: #537A52 !important;
}

.customSchedule .addButton {
  position: absolute;
  left: 5px;
  transform: translateY(-30px);
  border-radius: 8px;
  border: 1px solid;
  padding: 4px 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  @apply bg-bg-primary;
  @apply border-border-primary;
  @apply text-text-brand-primary;
  @apply text-text-sm-bold;
  box-shadow: 0px 0px 0px -2px rgba(0, 0, 0, 0.3);
  display: none;
}

.customSchedule .e-work-cells:hover .addButton {
  display: flex;
}

/**************** END EJS CSS ****************/
.mat-mdc-dialog-surface {
  background: white !important;
}

input[type=number]::-webkit-inner-spin-button,
input[type=number]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  margin: 0;
}

/* fallback */
@font-face {
  font-family: "Material Icons";
  font-style: normal;
  font-weight: 400;
  src: url(./assets/font/MaterialIcons-Regular.woff2) format("woff2");
}

.material-icons {
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

.btn_base {
  width: auto;
  height: auto;
  border: none;
  outline: none;
  border: 1px solid transparent;
  cursor: pointer;
  text-decoration: none;
  background-color: transparent;
  padding: 12px 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  transition: all 0.2s;

  @media screen and (max-width: 860px) {
    padding: 12px;
  }

  &.small {
    padding: 5px 8px;
  }

  // &:active {
  //   transform: scale(.95);
  //   transition: all .2s;
  // }

  &:hover {
    transition: all 0.2s;

    .iconActive {
      display: block !important;
    }

    .iconActive~.icon {
      display: none;
    }

    .icon.black {
      filter: invert(94%) sepia(10%) saturate(912%) hue-rotate(310deg) brightness(109%) contrast(89%);
    }
  }

  .icon {
    width: auto;
    height: 16px;
    object-fit: contain;
  }

  &.small {
    .icon {
      width: auto;
      height: 12px;
      object-fit: contain;
    }
  }

  .iconActive {
    display: none;
  }

  .name {
    font-size: 13px;
    line-height: 16px;
    font-family: "Stardom";
  }
}

/* TEXT LINK */
.text-link {
  color: #0089ef;
  text-decoration: none;
  cursor: pointer;
}

.text-link:hover {
  text-decoration: underline;
}

/* MAX-LINE */
.maxLine-2 {
  white-space: initial !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
}

.maxLine-3 {
  white-space: initial !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 3 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
}

/* BUTTON PRIMARY */
.btn_primary {
  border: none;
  outline: none;
  width: 100%;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  background: #0089ef;
  text-decoration: none;
  color: white;
  font-weight: 500;
  box-shadow: 0px 3px 1px -2px rgb(0 0 0 / 20%),
    0px 2px 2px 0px rgb(0 0 0 / 14%), 0px 1px 5px 0px rgb(0 0 0 / 12%);
}

.btn_primary:hover {
  box-shadow: 5px 5px lightgrey;
  transition: all 0.3s;
}

.btn_primary .icon {
  width: 16px;
  height: 16px;
  margin-right: 12px;
}

.btn_primary .name {
  color: white;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.disabledbutton {
  pointer-events: none;
  opacity: 0.4;
}

/* BUTTON OUTLINE */
.btn_outline {
  border: none;
  outline: none;
  padding: 9px 16px;
  background: #ffffff;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  border-radius: 4px;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -ms-border-radius: 4px;
  -o-border-radius: 4px;
  text-decoration: none;
  gap: 10px;
  border: 1px solid #dee2eb;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: #0f182e;
}

.btn_outline .icon {
  display: block;
  width: 16px;
  height: 16px;
  object-fit: cover;
}

.btn_outline:hover {
  background-color: #0f182e;
  color: white;
}

.btn_outline .name {
  color: #0f182e;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.btn_outline:hover .name {
  color: white;
}

.btn_outline:hover .icon:not(.active) {
  filter: invert(99%) sepia(99%) saturate(2%) hue-rotate(94deg) brightness(108%) contrast(101%);
}

// INPUT LABEL
.inputLabel {
  width: 100%;
  position: relative;
  display: flex;
  border: 1px solid #dee2eb;
  border-radius: 4px;
  align-items: center;
}

.inputLabel .txtLabel {
  position: absolute;
  z-index: 2;
  top: -15px;
  left: 4px;
  padding: 2px 6px;
  background-color: white;
  font-size: 12px;
}

.inputLabel .txtLabel.inputRequired:after {
  content: " *";
  color: #ea2a10;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
}

.inputLabel.checkbox {
  border: none;
}

.inputLabel.checkbox .txtInput {
  order: 1;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: 2px;
  cursor: pointer;
}

.inputLabel.checkbox .txtLabel {
  position: relative;
  top: unset;
  left: unset;
  transform: none;
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  cursor: pointer;
  position: relative;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

// input[type="checkbox"]:checked {
//   appearance: auto;
//   accent-color: #0f182e;
// }

.txtInput {
  border: none;
  outline: none;
  z-index: 1;
  position: relative;
  border-radius: 4px;
  width: 100%;
  height: 40px;
  font-size: 14px;
  line-height: 22px;
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 0;
  background-color: transparent;
}

mat-error {
  width: 100%;
  margin-top: 5px;
  font-size: 12px;
  line-height: 18px;
}

mat-error:empty {
  display: none;
}

// .inputLabelerrors {
//   margin-bottom: 22px;
// }

.inputLabelerrors .mat-mdc-form-field-error {
  width: 100%;
  position: absolute;
  font-size: 13px;
  line-height: 18px;
  margin-top: 5px;
  font-weight: 400;
  bottom: -5px;
  left: 0;
  transform: translateY(100%);
}

// END INPUT LABEL

// MODAL APP
.radiusModal-16px .mat-mdc-dialog-container {
  border-radius: 16px;
  overflow: hidden;
}

.backdropModal {
  background: rgba(0, 0, 0, 0.5);
}

.modalApp {
  padding: 24px;
  //  background: #F1E9DE;
  // box-shadow: 0px 2px 4px rgb(0 0 0 / 15%), 0px 2px 8px rgb(0 0 0 / 10%);
  border-radius: 12px;
  max-width: 750px;
  min-width: 280px;
  // width: 95vw;
  width: 100%;
  max-height: 85vh;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  position: relative;

  p {
    margin-bottom: 0;
  }
}

.modalApp .headerModal {
  width: 100%;
  position: relative;
  padding-top: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: space-between;
}

.modalApp .headerModal .titleModal {
  color: #0f182e;
  font-family: Roboto;
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: 26px;
}

.modalApp .headerModal .btnCloseModal {
  padding: 8px;
  border-color: #d9d3cc;
  position: absolute;
  top: 0px;
  right: 0px;
}

.modalApp .bodyModal {
  flex: 1;
  overflow: auto;
  margin-top: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
}

.modalApp .bodyModal .listSection {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 16px;
}

.modalApp .bodyModal .listInfoSection {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.modalApp .bodyModal .section {
  width: 100%;
}

.modalApp .bodyModal .titleSection {
  color: #0f182e;
  font-family: Roboto;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 22px;
}

.titleSection.rowModifyButton {
  position: relative;
  width: 100%;
}

.btnModify {
  color: #0089ef;
  font-size: 14px;
  font-style: normal;
  font-weight: 700;
  line-height: 20px;
  border: none;
  outline: none;
  cursor: pointer;
  background-color: transparent;
  position: absolute;
  right: 0;
  top: 0;
}

.modalApp .bodyModal .titleSection .desSection {
  display: block;
  color: #6c748a;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 16px;
  margin-top: 4px;
}

.modalApp .bodyModal .titleSection+.listInfoSection {
  margin-top: 16px;
}

.modalApp .customTabGroup .mat-mdc-tab-header {
  position: sticky;
  top: 0;
  background-color: #f1e9de;
  z-index: 10;
}

.modalApp .customTabGroup .mat-mdc-tab-header,
.modalApp .customTabGroup .mat-mdc-tab-body {
  padding-left: 24px !important;
  padding-right: 24px !important;
}

.modalApp .customDatePicker .e-date-wrapper {
  width: 100% !important;
}

.modalApp .wrapButtonAction {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  margin-top: 24px;
}

.modalApp .wrapButtonAction button {
  width: 100%;
}

.modalApp .wrapButtonAction button:first-child:last-child {
  width: auto;
  margin-left: auto;
}

/* Custom MAT CHECKBOX */
.custom-matCheckbox .mdc-checkbox {
  padding: 0;
  margin: 0;
  width: 20px;
  height: 20px;
  flex: unset;
  flex-shrink: 0;
}

.custom-matCheckbox .mdc-form-field {
  gap: 8px;
}

.custom-matCheckbox .mdc-label {
  color: #0f182e;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  padding-left: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-matCheckbox .mat-mdc-checkbox-touch-target {
  display: none;
}

.custom-matCheckbox .mdc-checkbox__native-control {
  width: 100% !important;
  height: 100% !important;
}

.custom-matCheckbox .mdc-checkbox__background {
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

.custom-matCheckbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate="true"])~.mdc-checkbox__background {
  border-radius: 4px !important;
  border: 1px solid #0f182e !important;
  background: #fff !important;
}

.custom-matCheckbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,
.custom-matCheckbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,
.custom-matCheckbox .mdc-checkbox__native-control[data-indeterminate="true"]:enabled~.mdc-checkbox__background {
  border-radius: 4px !important;
  background-color: #0f182e !important;
  border-color: #0f182e !important;
}

.custom-matCheckbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark {
  color: white !important;
}

.custom-matCheckbox-ios .mat-mdc-checkbox-touch-target,
.custom-matCheckbox-ios .mdc-checkbox__background {
  display: none;
}

// MatCheckbox iOS
.custom-matCheckbox-ios .mdc-checkbox {
  padding: 0;
  margin: 0;
  width: 24px;
  height: 12px;
  flex: unset;
  flex-shrink: 0;
}

.custom-matCheckbox-ios .mdc-form-field {
  gap: 8px;
}

.custom-matCheckbox-ios .mdc-label {
  color: #0f182e;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  padding-left: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-matCheckbox-ios .mdc-checkbox__native-control {
  appearance: none;
  width: 100% !important;
  height: 100% !important;
  opacity: 1;
  background-color: #9199af;
  border-radius: 40px;
  position: relative;
}

.custom-matCheckbox-ios .mdc-checkbox__native-control::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 3px;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: white;
  transform: translateY(-50%);
  transition: all 0.3s;
}

.custom-matCheckbox-ios .mdc-checkbox__native-control:checked {
  background-color: #18bd5a;
}

.custom-matCheckbox-ios .mdc-checkbox__native-control:checked:before {
  left: 15px;
  transition: all 0.3s;
}

// CUSTOM EJS MULTISELECT
.custom-ejs-multiselect {
  width: 100%;
}

.custom-ejs-multiselect .e-multiselect {
  min-height: 40px;
  position: relative;
}

.custom-ejs-multiselect .e-multiselect.e-control-wrapper {
  margin-bottom: 0;
  padding-left: 12px;
  padding-right: 12px;
  border-radius: 4px;
  border: 1px solid #dee2eb !important;
  background: #fff;
}

.custom-ejs-multiselect .e-multiselect.e-control-wrapper::before,
.custom-ejs-multiselect .e-multiselect.e-control-wrapper::after {
  display: none;
}

.custom-ejs-multiselect .e-multi-select-wrapper {
  padding-right: 0;
  display: flex;
  align-items: center;
}

.custom-ejs-multiselect .e-multi-select-wrapper .e-searcher {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}

.custom-ejs-multiselect .e-multi-select-wrapper .e-delim-values {
  padding-left: 0;
  color: #0f182e;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.custom-ejs-multiselect .e-multi-select-wrapper .e-delim-view.e-delim-values.e-delim-overflow,
.custom-ejs-multiselect .e-multi-select-wrapper .e-delim-view.e-delim-values.e-delim-total {
  display: flex !important;
}

.custom-ejs-multiselect .e-multi-select-wrapper .e-chips-close.e-close-hooker {
  top: 50% !important;
  transform: translateY(-50%);
  margin-top: 0;
  display: inline !important;
}

.custom-ejs-multiselect .e-multi-select-wrapper .e-delim-values:empty~.e-chips-close.e-close-hooker {
  display: none !important;
}

// END CUSTOM EJS MULTISELECT

// CUSTOM TABLE
.customTable p {
  margin-bottom: 0;
}

.customTable .e-table {
  table-layout: fixed;
}

.customTable {
  border: none !important;

  .e-pagerconstant {
    display: none;
  }

  .e-checkbox-wrapper .e-frame.e-check,
  .e-css.e-checkbox-wrapper .e-frame.e-check {
    @apply bg-bg-brand-strong
  }
}

.customTable .e-gridheader {
  border: none !important;

  thead {
    border-top: 1px solid;
    ;
    border-bottom: 1px solid;
    @apply border-border-primary;

    .e-columnheader {
      height: 48px;
    }

    tr {
      // .e-headercell.e-defaultcursor {
      //   padding-left: 0 !important;
      //   padding-right: 0 !important;
      // }

      th {
        .e-headercelldiv {
          padding-left: 0
        }

        .e-headertext {
          @apply text-text-sm-semibold;
          @apply text-text-tertiary;
        }
      }
    }
  }
}

.customTable.hideHeaderTable .e-gridheader thead {
  display: none;
}

.customTable .e-gridcontent {
  tr {
    .e-rowcell {
      border-bottom: 1px solid #E9EAEB !important;
      padding-top: 12px !important;
      padding-bottom: 12px !important;
      vertical-align: top;
    }

    .e-rowcell.e-gridchkbox {
      padding-left: 18px !important;
      padding-right: 0 !important;
    }
  }
}

.customTable~.e-pager {
  border: none;

  .e-pagerconstant {
    display: none;
  }

  .e-pagerdropdown {
    padding-left: 5px;
    padding-right: 5px;
    border: 2px solid;
    border-radius: 8px;
    @apply border-border-primary;
  }

  .e-currentitem,
  .e-currentitem.e-numericitem.e-focused {
    @apply bg-bg-brand-strong
  }
}

.customTable table {
  border-collapse: collapse !important;
}

.customTable.whiteRows .e-row[aria-selected="true"] {
  background-color: #f2f4f9 !important;
}

.customTable .e-detailrow .e-row:nth-child(odd),
.customTable .e-detailrow .e-row:hover {
  background-color: unset !important;
}

// .customTable .e-row:hover {
//   background-color: #f1f5ff !important;
// }

// .customTable .e-rowcell.rowTable {
.customTable .e-rowcell,
.textTable {
  font-size: 14px !important;
  line-height: 22px !important;
  color: #0f182e !important;
}

.customTable .e-rowcell {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
}

.customTable .e-rowcell,
.customTable .e-detailrowcollapse {
  background-color: transparent !important;
  border-color: transparent;
}

.customTable .e-detailrow .e-rowcell,
.customTable .e-detailrow .e-detailrowcollapse {
  border-top: 1px solid #edf0f6 !important;
}

.customTable .e-rowcell.rowTable a {
  text-decoration: none;
  color: #0089ef;
}

.customTable .e-rowcell,
.customTable .e-headercell,
.customTable .e-detailheadercell {
  padding-left: 10px !important;
  padding-right: 10px !important;
}

.customTable .e-gridheader .e-sortfilter .e-headercelldiv,
.customTable .e-gridheader .e-stackedheadercelldiv {
  padding: 0;
  padding-right: 10px !important;
}

.customTable .e-headercelldiv {
  height: auto !important;
  line-height: unset !important;
  margin: 0 !important;
}

.customTable .e-icon-gdownarrow,
.customTable .e-icon-grightarrow {
  text-decoration: none !important;
  padding: 8px;
  border-right: 1px solid #dee2eb;
  position: relative;
  height: 30px;
  width: 30px;
  display: block;
}

.customTable .e-icon-gdownarrow::before,
.customTable .e-icon-grightarrow::before {
  font-family: "Material Icons";
  font-size: 18px;
  position: absolute;
  top: 50%;
  left: 30%;
  transform: translate(-50%, -50%);
  color: #9199af;
}

.customTable .e-icon-grightarrow::before {
  content: "\e5cc";
}

.customTable .e-icon-gdownarrow::before {
  content: "\e5cf";
}

.customTable .e-row.row-dash {
  border: 1px dashed #9199af !important;
  border-bottom: 1.1px dashed #9199af !important;
  background-color: white !important;
}

.customTable .e-row .wrapIcSearch {
  width: 35px;
  height: 35px;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f2f4f9;
}

.customTable .e-row .wrapIcSearch i {
  color: #9199af;
  font-size: 18px;
}

.customTable .iconGrid {
  height: 26px;
  width: auto;
  object-fit: contain;
}

.customTable .actionTable {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.customTable .actionTable .zipplexButtonRed,
.customTable .actionTable .buttonLink {
  width: 100%;
}

.customTable .buttonActionItem {
  width: 24px;
  height: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border: none;
  outline: none;
  padding: 0;
  flex-shrink: 0;
  background-color: transparent;
}

.customTable .buttonActionItem:hover {
  outline: 1px solid #0f182e;
}

.customTable .buttonActionItem img {
  width: 16px;
  height: 16px;
  object-fit: cover;
  display: block;
}

.customTable .buttonEditName {
  width: auto;
  height: auto;
  cursor: pointer;
  border: none;
  outline: none;
  padding: 0;
  flex-shrink: 0;
  background-color: transparent;
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 2px 15px;
  border: 1px solid #dee2eb;
  border-radius: 4px;
}

// .customTable button {
//   max-height: 32px !important;
//   padding-left: 10px;
//   padding-right: 10px;
//   margin: 0;
//   line-height: unset;
//   box-shadow: none;
//   font-size: 12px;
// }

// .customTable .buttonEditName img {
//   width: 24px;
//   height: 24px;
//   object-fit: cover;
// }

.customTable.e-grid .e-detailrow .e-content {
  height: auto !important;
  max-height: 350px !important;
  overflow: auto;
}

.customTable .e-row.editMode .e-rowcell>*:not(.editMode),
.customTable .e-row .e-rowcell>.editMode {
  display: none;
}

.customTable .e-row.editMode .e-rowcell>.editMode {
  display: block;
}

.customTable .txtInput {
  background-color: white !important;
  text-align: center;
}

.customTable.e-grid .e-pager {
  background-color: white;
  position: relative;
}

.customTable.e-grid .e-pager::after,
.customTable.e-grid .e-pager::before {
  content: "";
  position: absolute;
  height: 100%;
  background-color: white;
  top: 0;
  width: 5px;
}

.customTable.e-grid .e-pager::after {
  left: -5px;
}

.customTable.e-grid .e-pager::before {
  right: -5px;
}

.customTable .customDatePicker .e-date-wrapper {
  height: 40px !important;
  padding: 8px;
  left: -1px;
  border-color: #dee2eb !important;
  background-color: white;
}

.customTable .customDatePicker input.e-input {
  font-size: 12px !important;
  line-height: unset !important;
}

.customTable .customDatePicker .e-input-group-icon.e-date-icon,
.customTable .customDatePicker *.e-control-wrapper .e-input-group-icon.e-date-icon {
  font-size: 14px;
}

.customTable .e-pager .e-pagercontainer {
  background: transparent;
  border: none;
  margin-bottom: 0;
}

.customTable .e-pager .e-numericitem {
  background-color: transparent !important;
}

.customTable .e-pager .e-currentitem {
  background-color: #0f182e !important;
}

.customTable .e-pager .e-prevpagedisabled,
.customTable .e-pager .e-prevpage,
.customTable .e-pager .e-nextpage,
.customTable .e-pager .e-nextpagedisabled,
.customTable .e-pager .e-lastpagedisabled,
.customTable .e-pager .e-lastpage,
.customTable .e-pager .e-firstpage,
.customTable .e-pager .e-firstpagedisabled {
  background: transparent;
}

.customTable .e-pagerdropdown .e-input-group {
  border: 1px solid #dee2eb !important;
  padding: 0px 10px !important;
  border-radius: 4px !important;
}

.customTable .e-pagesizes .e-input-group::before,
.customTable .e-pagesizes .e-input-group::after {
  background-color: #0f182e !important;
}

.customTable .e-pager .e-pagerconstant {
  margin-bottom: 5px;
}

.e-list-item {
  color: #0f182e !important;
}

.e-list-item.e-active {
  font-weight: bold;
}

// CUSTOM CHILD TABLE
.customTable.e-default .e-detailrow .e-lastrowcell {
  border: 0;
  padding: 0;
}

.customTable .e-detailcell {
  width: 100%;
}

.customChildTable {
  background-color: #f3f4f8;
}

.customChildTable .e-headercell {
  padding: 0 !important;
}

.customChildTable .e-rowcell {
  padding: 12px 8px !important;
  vertical-align: baseline;
}

.customChildTable .e-headercell {
  padding-top: 8px !important;
  padding-bottom: 8px !important;
}

// CK EDITOR
.textCKEditor p,
.textCKEditor p * {
  margin: 0 !important;
  font-size: 14px !important;
  font-style: normal !important;
  line-height: 22px !important;
}

.textCKEditor img {
  width: auto;
  height: auto !important;
  padding-top: 10px;
  margin-left: auto;
  margin-right: auto;
  object-fit: contain;
  width: 85% !important;
  max-width: 800px !important;
  margin-bottom: 25px;
  display: block;
}

.textCKEditor p:not(:first-child) {
  margin-top: 10px !important;
}

.e-grid .e-focused:not(.e-menu-item) {
  box-shadow: unset;
}

.text-grid-content {
  line-height: 15px;
  margin-top: 5px;
  margin-bottom: 10px;
  overflow: auto;
  overflow-wrap: break-word;
  text-wrap: wrap;
  text-align: start;
}

.matError {
  width: 100%;
  margin-top: 5px;
  font-size: 12px;
  line-height: 18px;
  color: $danger;
}

.inputLabel~.matError {
  display: none;
  text-align: left;
}

.inputLabel.inputLabelerrors~.matError {
  display: block;
}

.txtInput {
  height: 40px;
}

.txtLabel {
  background-color: transparent;
}

.flex-row-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.form-group {
  width: 100%;
}

.half-form-group {
  width: calc(50% - 0.25rem);
}

.search-bar {
  border-radius: 30px;
  border: 1.2px lightgray solid;
  width: 300px;
}

.icon-button {
  cursor: pointer;
  user-select: none;
  font-weight: 600;
  font-size: 18px;
  line-height: 22px;
}

.icon-button:hover {
  color: $button-gray-hover;
  opacity: 0.7 !important;
}

.icon-button-circle {
  $size: 35px;
  width: $size;
  height: $size;
  line-height: $size;
  border-radius: $size;
  border: 2px lightgray dashed;
  padding: 2.5px;
}

.icon-button-circle:hover {
  background-color: $button-gray-hover;
  opacity: 0.7 !important;
}

hr {
  border: 1px solid lightgray;
}

.new-item-dialog {
  max-width: 800px !important;
  min-width: 350px !important;
}

.e-dlg-header {
  width: 100% !important;
}

.btn-add-entry {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-weight: 600;
  font-size: 20px;
}

.btn-start-time {
  width: 300px;
  border: 2px lightgray solid;
}

.btn-dashed {
  border: 2px lightgray dashed;
  width: 100%;

  span {
    vertical-align: text-bottom;
    font-weight: 900;
  }
}

.btn-dashed:hover {
  background-color: $button-add-entry-hover-bg;
  color: $button-add-entry-hover-color;
  opacity: 0.8;

  span {
    color: $button-add-entry-hover-color;
  }
}

@media screen and (max-width: 460px) {
  .form-group {
    min-width: 170px;
  }

  .half-form-group {
    width: 100%;
  }

  .flex-row-between {
    flex-wrap: wrap;
  }
}

.input-content {
  max-width: 850px;
  margin: auto;
}

.popup-content {
  border: 1 solid lightgray;
}

// Override boostrap
.btn-success {
  background-color: $button-success-bg;
  color: white;
}

.btn-success:hover {
  background-color: $button-success-bg-hover;
}

.btn-outline-success:hover {
  background-color: $button-success-bg-hover;
}

.btn-custom:hover {
  background-color: $button-gray-hover;
}

::-webkit-scrollbar {
  width: 7px;
  margin-top: 20px;
  opacity: 0;
  height: 12px;
}

::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px #c2c2c9;
  border-radius: 10px;
  margin-top: 20px;
  margin-bottom: 20px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 6px #c2c2c9;
}

.e-grid .e-focused:not(.e-menu-item):not(.e-editedbatchcell) {
  box-shadow: none !important;
}

.custom_dialog {
  .mat-mdc-dialog-surface {
    border-radius: 10px !important;
  }

  .mat-dialog-container {
    padding: 1em;
    width: 100%;
    height: 100%;

  }
}

.mat-mdc-menu-panel.customize {
  max-width: 520px !important;

}

.text-inline {
  margin-right: 10px;
  margin-top: 10px;
  display: block;
  width: 200px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

}

.cdk-drag-preview {
  padding: 20px 10px;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.example-box:last-child {
  border: none;
}

.example-list.cdk-drop-list-dragging .example-box:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}