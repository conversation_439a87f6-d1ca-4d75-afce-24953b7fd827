import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { DataService } from 'app/service/data.service';
import { ItemAndServiceViewEnum } from 'app/enum/item-service.enum';
import { InnoSelectSearchProjectComponent } from 'app/component/inno-select-search-project/inno-select-search-project.component';
import { ToastService } from 'app/service/toast.service';
import { ServiceService } from './../../../service/service.service';
import { Project } from 'app/dto/interface/project.interface';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { SharedModule } from 'app/module/shared.module';
import { CommonModule } from '@angular/common';
import { Component, DestroyRef, OnInit, ViewChild } from '@angular/core';
import { ActionEventArgs, GridAllModule, GridComponent, PagerModule, SelectionSettingsModel, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { startWith, Subject, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { GetServiceRequestParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AvatarModule } from 'ngx-avatars';
import { MatTooltipModule } from '@angular/material/tooltip';
import { StoreService } from 'app/service/store.service';
import { getNameTaxes } from 'app/utils/invoice.helper';
import { ModifyItemsAndServiceDialog } from 'app/service/dialog/modify-items-and-service.dialog';

@Component({
  selector: 'app-service-management',
  templateUrl: './service-management.component.html',
  styleUrls: ['./service-management.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    PagerModule,
    GridAllModule,
    SharedModule,
    AvatarModule,
    MatTooltipModule,
    InnoInputSearchComponent,
    InnoTableActionComponent,
    InnoSelectSearchProjectComponent,
    InnoSpinomponent
  ],
  providers: [LayoutUtilsService],
})
export class ServiceManagementComponent implements OnInit {
  public isLoading = false;
  public getNameSelectedTaxes = getNameTaxes
  private _subscriptions: Subscription[] = [];
  public search: string = ''
  private searchSubject = new Subject<string>();
  public sort: SortGird
  public sortOptions: SortSettingsModel = { columns: [] };
  public selectionOptions?: SelectionSettingsModel;
  public projectName!: string
  public listProject: Project[] = []
  public dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  @ViewChild('grid') grid?: GridComponent;
  public columnName: string
  public direction: any
  constructor(
    private translate: TranslateService,
    private layoutUtilsService: LayoutUtilsService,
    private router: Router,
    public _storeService: StoreService,
    private destroyRef: DestroyRef,
    private activatedRoute: ActivatedRoute,
    private serviceService: ServiceService,
    private toastService: ToastService,
    private dataService: DataService,
    private modifyItemAndServiceDialog: ModifyItemsAndServiceDialog

  ) {
  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      this.LoadAllServiceByProjectAsync();
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }

  }


  LoadAllServiceByProjectAsync() {
    this.isLoading = true
    let params: GetServiceRequestParam = {
      Page: this.currentPage,
      PageSize: this.pageSizesDefault,
      Search: this.search,
      ...this.sort
    };

    this.serviceService.GetAllService(params).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        if (res) {
          this.isLoading = false
          this.totalPages = res.totalRecords
          this.dataSource = res.data
          if (this.columnName) {
            this.sortOptions = {
              columns: [{ field: this.columnName, direction: this.direction }]
            };

          }

        }
      }
    });
  }


  handleSearch(search: string) {
    this.searchSubject.next(search);
  }
  ngOnInit(): void {
    const sb = this.searchSubject.pipe(startWith('')).subscribe((search) => {
      this.search = search;
      this.LoadAllServiceByProjectAsync();

    });
    this._subscriptions.push(sb)

    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.tab !== 'Item') {
        if (queryParams?.page) {
          this.currentPage = queryParams.page;
          this.search = "";
          this.LoadAllServiceByProjectAsync();
        }
      }
    });
    this.dataService.reloadService.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res == true) {
        this.search = "";
        this.LoadAllServiceByProjectAsync()
      }
    })
  }

  handleEdit(item: any) {
    const dialogRef = this.modifyItemAndServiceDialog.open({
      mode: ItemAndServiceViewEnum.Service,
      isShowProject: true,
      serviceInfo: item
    })
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {
          this.dataService.reloadService.next(true)
        }
      })
    });
  }

  handleDelete(item: any) {
    const _title = this.translate.instant('ITEMS_SERVICES.DeleteService');
    const _description = this.translate.instant('COMMON.ConfirmDelete');

    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return

      this.serviceService.DeleteServices([item.id]).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.search = "";
          this.LoadAllServiceByProjectAsync();
          this.toastService.showSuccess(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
        }
        else {
          this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
        }
      })
    })
  }

  handleArchive(item: any) {
    this.serviceService.UpdateArchive([item.id]).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.search = "";
        this.LoadAllServiceByProjectAsync();
        this.toastService.showError(this.translate.instant("TOAST.Save"), this.translate.instant("TOAST.Success"));
      }
      else {
        this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
      }
    })
  }

  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        this.LoadAllServiceByProjectAsync();
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      this.LoadAllServiceByProjectAsync();
    }

  }
  ngOnDestroy(): void {

    if (this._subscriptions) {

      this._subscriptions.forEach((sb) => sb.unsubscribe());
    }
  }
}
