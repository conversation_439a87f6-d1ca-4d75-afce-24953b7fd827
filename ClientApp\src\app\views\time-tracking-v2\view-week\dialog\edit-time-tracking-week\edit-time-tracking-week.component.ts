import { AddTimeDialog } from './../../../../../service/dialog/dialog-add-time.dialog';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { ToastService } from 'app/service/toast.service';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { TimetrackingService } from 'app/service/timetracking.service';
import { Component, DestroyRef, EventEmitter, inject, Inject, OnInit, Output } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { SharedModule } from 'app/module/shared.module';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { StoreService } from 'app/service/store.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-edit-time-tracking-week',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [InnoModalWrapperComponent, InnoPopoverComponent, SharedModule, InnoEmptyDataComponent, InnoFormInputComponent, InnoModalFooterComponent],
  templateUrl: './edit-time-tracking-week.component.html',
  styleUrl: './edit-time-tracking-week.component.scss'
})
export class EditTimeTrackingWeekComponent {
  @Output() cancel: EventEmitter<any> = new EventEmitter<any>();
  @Output() submit: EventEmitter<any> = new EventEmitter<any>();
  listTimeTracking: any[] = [];

  private destroyRef = inject(DestroyRef);
  private timeTrackingServices = inject(TimetrackingService)
  public _storeService = inject(StoreService)
  private layoutUtilsService = inject(LayoutUtilsService)
  private toastService = inject(ToastService)
  private translate = inject(TranslateService);
  private addTimeDialog = inject(AddTimeDialog)
  constructor(public dialogRef: MatDialogRef<EditTimeTrackingWeekComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any) {
    if (this.data) {
      this.GetTimeTracking();
    }
  }

  static getComponent(): typeof EditTimeTrackingWeekComponent {
    return EditTimeTrackingWeekComponent;
  }
  handleClose() {
    this.dialogRef.close();
  }
  GetTimeTracking() {
    const payload: Parameter = {
      Page: 1,
      PageSize: 100,
      Search: "",
      ...this.data
    }

    this.timeTrackingServices.GetAllTimeTracking(payload)
      .pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
        if (res) {
          this.listTimeTracking = res.data;
        }
      }
      )

  }

  handleCancel() {
    this.dialogRef.close();
  }

  handleSubmit() {
    this.dialogRef.close(true);
  }
  handleDelete(item: any) {
    const _title = this.translate.instant('Delete TimeTracking !');
    const _description = this.translate.instant('Do you want to delete?');
    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return;

      this.timeTrackingServices.DeleteTimeTracking([item.id]).subscribe({
        next: (res) => {
          if (res) {
            this.toastService.showSuccess(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
            this.GetTimeTracking();
            return;
          }
          this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));


        },
        error: () => {
          this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
        }
      })
    })
  }
  handleEdit(data: any) {
    const item = { new: true, data }

    const dialogRef = this.addTimeDialog.open(item);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res: any) => {
        if (res) {
          this.GetTimeTracking();
        }
      });
    });
  }

}
