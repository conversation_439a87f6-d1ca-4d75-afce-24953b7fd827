<!-- Header page -->
<div class="w-full py-[24px] border-b border-border-primary">
  <div class="container-full flex justify-between items-center flex-wrap gap-2">
    <p class="text-text-primary text-headline-lg-bold">
      {{'EXPENSES.Title'|translate }}
    </p>

    <div class="flex items-center gap-2">
      <button class="button-outline button-size-md">
        <img src="../../../assets/img/icon/ic_download.svg" alt="icon">
        {{'EXPENSES.ExportButton'|translate }}
      </button>

      @if(isAdmin) {
      <button (click)="modifyExpenses()" class="button-size-md button-primary">
        <img src="../../../assets/img/icon/ic_add_white.svg" alt="icon">
        {{'EXPENSES.NewExpenseButton'|translate }}
      </button>
      }
    </div>
  </div>
</div>
<!-- End Header page -->

<div class="container-full mt-[24px] flex flex-wrap gap-[12px] items-center">
  <div class="w-full max-w-[300px]">
    <app-inno-input-search [value]="search" (onChange)="handleSearch($event)" />
  </div>

  <div class="w-[200px]">
    <app-inno-datepicker [placeholder]="'EXPENSES.DatePlaceholder' | translate"
      (onChange)="handleChangeDateFilter($event)" />
  </div>

</div>
@if(isLoading) {
<div class="container-full h-[60dvh] flex justify-center items-center">
  <app-inno-spin size="lg" />
</div>
}@else {
<div class="w-full mt-[12px]"
  [ngClass]="{ 'mb-28':_storeService.getIsRunning() }">
  <ejs-grid
    #grid
    class="customTable"
    [dataSource]="dataSource"
    [sortSettings]='sortOptions'
    (actionBegin)="onActionBegin($event)"
    [allowSorting]="true"
    [allowSelection]="true">
    <e-columns>
      <!-- <e-column type="checkbox" width="50"></e-column> -->
      <e-column [headerText]="'EXPENSES.GIRD.ExpenseName' | translate"
        width="200" field="expensesName">
        <ng-template #template let-data>
          <div class="w-full flex flex-col">
            @if(data.expensesName) {
            <p class="text-text-primary text-text-md-semibold line-clamp-1">
              {{ data?.expensesName }}
            </p>
            }

            @if(data?.categoryName) {
            <p class="text-text-tertiary text-text-sm-regular line-clamp-1">
              {{ data.categoryName }}
            </p>
            }

            @if(data?.itemName) {
            <p class="text-text-tertiary text-text-sm-regular line-clamp-1">
              {{ data.itemName }}
            </p>
            }
          </div>
        </ng-template>
      </e-column>
      <e-column [headerText]="'EXPENSES.GIRD.ProjectClient' | translate"
        width="200">
        <ng-template #template let-data>
          <p class="text-text-primary text-text-md-regular line-clamp-1">
            @if(data?.projectName) {
            [{{ data?.projectName }}]
            }
            {{ data?.clientName ?? '' }}
          </p>
        </ng-template>
      </e-column>

      <e-column [headerText]="'EXPENSES.GIRD.Date' | translate" width="120"
        field="date">
        <ng-template #template let-data>
          <p class="text-text-md-regular line-clamp-1 text-text-primary">
            {{data.date | date: _storeService.getdateFormat() }}
          </p>
        </ng-template>
      </e-column>

      <e-column [headerText]="'EXPENSES.GIRD.User' | translate" width="200">
        <ng-template #template let-data>
          <p class="text-text-primary text-text-md-semibold">
            @if(data?.inforUser?.firstName)
            {
            <div class="flex items-center">
              <ngx-avatars
                matTooltip="{{data?.inforUser?.firstName}} {{data?.inforUser?.lastName}} "
                [size]="35"
                bgColor="{{_storeService.getBgColor(data?.inforUser?.firstName.slice(0,1))}}"
                [name]="data?.inforUser.firstName.charAt(0) +' '+ (data?.inforUser?.lastName ? data?.inforUser?.lastName.charAt(0) : '')" />
              <span class="pl-1 line-clamp-1"> {{data?.inforUser?.firstName}}
                {{data?.inforUser?.lastName}}</span>
            </div>
            }
            @else{
            <div class="flex items-center">
              <ngx-avatars
                matTooltip="{{data?.inforUser?.email}}"
                [size]="35"
                bgColor="{{_storeService.getBgColor(data?.inforUser?.email.slice(0,1))}}"
                [name]="data?.inforUser?.email.slice(0,1)" />
              <span class="pl-1 line-clamp-1"> {{data?.inforUser?.email}}</span>
            </div>

            }
          </p>
        </ng-template>
      </e-column>

      <e-column [headerText]="'EXPENSES.GIRD.Amount' | translate" width="230"
        field="paidAmount">
        <ng-template #template let-data>
          <div class="w-full flex gap-[8px] flex-wrap">
            <app-inno-status [status]="data.status" text="Unbilled" />
            <p class="text-text-primary text-text-md-bold">
              ${{data.paidAmount | decimal:2 |formatNumber}}
            </p>

          </div>
        </ng-template>
      </e-column>

      @if(isAdmin) {
      <e-column width="100">
        <ng-template #template let-data>
          <div class=" flex items-center">
            <app-inno-table-action
              (onEdit)="modifyExpenses(data.id)"
              (onDelete)="handleDeleteExpense(data)" />
            <app-inno-popover [content]="contentPopover">
              <button target class="button-icon">
                <img class="w-[20px]"
                  src="../../../assets/img/icon/ic_three_dots_verticel.svg"
                  alt="Icon">
              </button>
            </app-inno-popover>
            <ng-template #contentPopover>
              <div class="flex w-[78px] flex-col">
                <button
                  (click)="MarkAsPaid(data.id)"
                  class="w-full h-[32px] text-text-sm-regular hover:bg-bg-secondary">
                  {{'COMMON.MarkBill' |translate}}
                </button>
              </div>
            </ng-template>
          </div>

        </ng-template>

      </e-column>
      }
    </e-columns>

  </ejs-grid>
  <ejs-pager [pageSize]='pageSizesDefault'
    [totalRecordsCount]='totalPages'
    [currentPage]="currentPage"
    [pageSizes]="pageSizes" (click)="onPageChange($event)">
  </ejs-pager>
</div>

}
