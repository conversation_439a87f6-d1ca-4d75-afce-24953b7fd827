﻿using InnoBook.DTO.Mail;
using InnoBook.Services.Interface;
using MailKit.Security;
using MimeKit;
using MimeKit.Cryptography;
using System.Net.Mail;
using SmtpClient = MailKit.Net.Smtp.SmtpClient;


namespace InnoBook.Services.Classes
{
    public class MailService(IConfiguration configuration) : IMailService
    {
        public bool SendMail(MailInfoAndContent infoAndContent)
        {
            var Signer = new DkimSigner(
                   "Files/DKIM.txt", // path to your privatekey
                    "innologiciel.com", // your domain name
                    configuration.GetSection("Smtp:DKIM_Selector").Value)
            //"default._domainkey.Lokatix.net") // The selector given on https://dkimcore.org/
            {
                HeaderCanonicalizationAlgorithm = DkimCanonicalizationAlgorithm.Simple,
                BodyCanonicalizationAlgorithm = DkimCanonicalizationAlgorithm.Simple,
                AgentOrUserIdentifier = "@Innologiciel.com", // your domain name
                QueryMethod = "dns/txt",
            };

            // Composing the whole email
            var message = new MimeMessage();
            message.From.Add(new MailboxAddress("InnoBooks", configuration.GetSection("Smtp:From").Value));
            message.To.Add(new MailboxAddress("", infoAndContent.To));
            message.Subject = infoAndContent.Subject;


            var headers = new HeaderId[] { HeaderId.From, HeaderId.Subject, HeaderId.To };

            var builder = new BodyBuilder();
            builder.TextBody = "";
            builder.HtmlBody = infoAndContent.Body;
            if (infoAndContent.Attachement != null)
                builder.Attachments.Add(infoAndContent.Attachement[0]);

            message.Body = builder.ToMessageBody();
            message.Prepare(EncodingConstraint.SevenBit);
            Signer.Sign(message, headers);
            // Sending the email
            try
            {


                using var client = new SmtpClient();
                // For demo-purposes, accept all SSL certificates (in case the server supports STARTTLS)
                client.ServerCertificateValidationCallback = (s, c, h, e) =>
                {
                    Console.WriteLine(e);
                    return true;
                };

                client.Connect(configuration.GetSection("Smtp:Host").Value, Convert.ToInt32(configuration.GetSection("Smtp:Port").Value), SecureSocketOptions.StartTls);
                client.Send(message);
                client.Disconnect(true);
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public bool SendMailTest(string email)
        {
            var info = new MailInfoAndContent
            {
                Body = "Test",
                To = email,
                Subject = "Test"
            };
            return SendMail(info);
        }

        public bool SendMailToken(string token, string email)
        {
            var info = new MailInfoAndContent
            {
                Body = "Voici votre jeton : " + token,
                To = email,
                Subject = "Code Validation"
            };
            return SendMail(info);
        }

        public bool SendMailTest(MailInfoAndContent infoAndContent)
        {
            MailMessage mail = new MailMessage
            {
                From = new MailAddress(configuration.GetSection("Smtp:From").Value, "Lokatix"),
                Subject = infoAndContent.Subject,
                Body = infoAndContent.Body,
                IsBodyHtml = true
            };

            mail.To.Add(infoAndContent.To);

            if (!string.IsNullOrEmpty(infoAndContent.CC))
                mail.CC.Add(infoAndContent.CC);

            if (!string.IsNullOrEmpty(infoAndContent.BCC))
                mail.Bcc.Add(infoAndContent.BCC);

            return true;

        }


    }
}
