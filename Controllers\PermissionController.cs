﻿using InnoBook.Attributes;
using InnoBook.Request.Role;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class PermissionController(IRoleServices roleServices,IHttpContextAccessor httpContextAccessor) : BaseController(httpContextAccessor)
    {
        [RequiredRoles(UserBusinessRole.Admin)]
        [HttpPost("AddRole")]
        public async Task<IActionResult> AddRole(RequestRole role)
        {
             var result= await roleServices.AddRole(role,IdUser);
            return Ok(result);

        }
     

    }
}
