﻿using InnoBook.DTO.CompanyTax;
using InnoBook.DTO.CoreModel;
using InnoBook.Entities;
using InnoBook.Request.Tax;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;

namespace InnoBook.Services.Classes
{
    public class TaxService(InnoLogicielContext context) : ITaxService
    {
        public async Task<List<RequestTax>> CreateTax(List<RequestTax> listTax)
        {

            try
            {
                var listTaxNew = listTax.Select(r => new Tax
                {
                    CompanyTaxId = r.CompanyTaxId,
                    ExpensesId = r.ExpensesId,
                    ItemInvoiceId = r.ItemInvoiceId,
                    ItemExpenseId = r.ItemExpenseId,
                    ItemId = r.ItemId,
                    ServiceId = r.ServiceId
                }).ToList();

                context.Taxs.AddRange(listTaxNew);
                await context.SaveChangesAsync();
                return listTax;

            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<PaginatedResponse<TaxItem>> GetAllTax(PaginatedRequest query, string companyId)
        {
            var companyGuid = Guid.Parse(companyId);

            var baseQuery = context.Taxs
                .Include(c => c.CompanyTax)
                .Where(c => c.CompanyTax.CompanyId == companyGuid);

            var totalRecords = await baseQuery.CountAsync();

            var taxData = await baseQuery
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            // Map vers TaxItem
            var items = taxData.Select(t => new TaxItem
            {
                Id = t.Id,
                Name = t.CompanyTax.Name,
                TaxeNumber = t.CompanyTax.TaxeNumber,
                Amount = t.CompanyTax.Amount,
                CompanyTaxId = t.CompanyTaxId
            }).ToList();

            return new PaginatedResponse<TaxItem>
            {
                Data = items,
                Page = query.Page,
                PageSize = query.PageSize,
                TotalRecords = totalRecords,
                TotalPage = (int)Math.Ceiling((double)totalRecords / query.PageSize)
            };
        }


        public async Task<List<RequestTax>> UpdateTax(List<RequestTax> listTax)
        {
            try
            {
                var taxEntities = listTax.Select(r => new Tax
                {
                    Id = r.Id,
                    CompanyTaxId = r.CompanyTaxId,
                    ExpensesId = r.ExpensesId,
                    ItemInvoiceId = r.ItemInvoiceId,
                    ItemExpenseId = r.ItemExpenseId,
                    ItemId = r.ItemId,
                    ServiceId = r.ServiceId
                }).ToList();
                context.Taxs.UpdateRange(taxEntities);
                await context.SaveChangesAsync();
                return listTax;

            }
            catch (Exception ex)
            {
                throw;
            }
        }
    }
}
