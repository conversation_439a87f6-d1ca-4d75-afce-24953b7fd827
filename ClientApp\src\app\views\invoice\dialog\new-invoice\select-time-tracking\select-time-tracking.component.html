<app-inno-modal-wrapper title="{{title}}" (onClose)="handleClose()">
  <div class="w-full p-[16px]">
    <div class="w-full flex flex-col relative">
      <div class="w-full">
        @if(isFetchingProject) {
        <div class="w-full py-2 flex justify-center items-center">
          <app-inno-spin />
        </div>
        } @else {
        @if(listInvoiceItem.length==0) {
        <div class="w-full">
          <app-inno-empty-data
            title="No result" />
        </div>
        } @else {
        <div class="overflow-auto w-full">
          <div class="selectProjectTableLayout">
            <div class="addBorderBottom w-full flex gap-[8px]">
              <div class="w-[16px] shrink-0">
                <app-inno-form-checkbox
                  [checked]="listIndexInvoiceSelected.length === listInvoiceItem.length"
                  (onChange)="handleCheckedAll($event)" />
              </div>
              <p class="text-text-tertiary text-text-sm-semibold">
                Description
              </p>
            </div>
            <p
              class="addBorderBottom text-text-tertiary text-text-sm-semibold">
              Project
            </p>
            <p
              class="addBorderBottom text-text-tertiary text-text-sm-semibold">
              Hourly
            </p>
            <p
              class="addBorderBottom text-text-tertiary text-text-sm-semibold">
              Service
            </p>
            <p
              class="addBorderBottom text-text-tertiary text-text-sm-semibold">
              User
            </p>
            <p
              (click)="sortDates('endTime')"
              [ngClass]="{'font-bold text-black': sortColumn === 'endTime'}"
              class="addBorderBottom text-text-tertiary text-text-sm-semibold text-center cursor-pointer">
              Hours
              @if(sortColumn=='endTime')
              {
              <span class="material-icons pl-1 !text-[15px]">
                {{ sortDirection === 'Ascending' ? 'arrow_upward' :
                'arrow_downward'
                }}
              </span>
              }
            </p>
            <p
              class="addBorderBottom text-text-tertiary text-text-sm-semibold text-center">
              Line Total

            </p>
            <p (click)="sortDates('date')"
              [ngClass]="{'font-bold text-black': sortColumn === 'date'}"
              class="addBorderBottom text-text-tertiary text-text-sm-semibold text-right cursor-pointer">
              Date
              @if(sortColumn=='date')
              {
              <span class="material-icons pl-1 !text-[15px]">
                {{ sortDirection === 'Ascending' ? 'arrow_upward' :
                'arrow_downward'
                }}
              </span>
              }
            </p>
          </div>
          @for(invoiceItem of listInvoiceItem; track invoiceItem; let i =
          $index) {
          <div class="selectProjectTableLayout">
            <div class="addBorderBottom w-full flex gap-[8px]">
              <div class="w-[16px] shrink-0">
                <app-inno-form-checkbox
                  [checked]="isCheckedIndex(i)"
                  (onChange)="handleToggleCheckedIndex(i)" />
              </div>
              <p class="text-text-primary text-text-sm-regular">
                {{ invoiceItem.description ?? '' }}
              </p>
            </div>
            <p class="addBorderBottom text-text-primary text-text-sm-regular">
              {{ invoiceItem.metadata?.timeTracking?.project?.projectName ??
              '-' }}
            </p>
            <p class="addBorderBottom text-text-primary text-text-sm-regular">
              {{invoiceItem.metadata?.timeTracking?.project?.hourlyRate?? '-'}}
            </p>
            <p class="addBorderBottom text-text-primary text-text-sm-regular">
              {{ invoiceItem.service
              ?.serviceName??
              '-' }}
            </p>
            <p class="addBorderBottom text-text-primary text-text-sm-regular">
              @if(invoiceItem.inforUser?.firstName)
              {
              <div class="flex items-center">

                <ngx-avatars
                  matTooltip="{{invoiceItem.inforUser?.firstName}} {{invoiceItem.inforUser?.lastName}} "
                  [size]="30"
                  bgColor="{{_storeService.getBgColor(invoiceItem.inforUser?.firstName.slice(0,1))}}"
                  [name]="invoiceItem.inforUser?.firstName.charAt(0) +' '+ (invoiceItem.inforUser?.lastName ? invoiceItem.inforUser?.lastName.charAt(0) : '')" />
                <span class="pl-1"> {{invoiceItem.inforUser?.firstName}}
                  {{invoiceItem.inforUser?.lastName}}</span>
              </div>

              }
              @else{
              <div class="flex items-center">
                <ngx-avatars
                  matTooltip="{{invoiceItem.inforUser?.email}}"
                  [size]="30"
                  bgColor="{{_storeService.getBgColor(invoiceItem.inforUser?.email.slice(0,1))}}"
                  [name]="invoiceItem.inforUser?.email.slice(0,1)" />
                <span class="pl-1"> {{invoiceItem.inforUser?.email}}</span>
              </div>

              }
            </p>
            <p
              class="addBorderBottom text-text-primary text-text-sm-regular text-center">
              {{ invoiceItem.metadata?.hours ?? '' }}
            </p>
            <p
              class="addBorderBottom text-text-primary text-text-sm-regular text-center">
              ${{calculateTotalInvoiceItem(invoiceItem.metadata?.hours, invoiceItem.metadata?.timeTracking?.project?.hourlyRate)
              | decimal:2 | formatNumber }}
            </p>
            <p
              class="addBorderBottom text-text-primary text-text-sm-regular text-right">
              {{ invoiceItem.date| date: _storeService.getdateFormat() }}
            </p>
          </div>
          }
        </div>
        <ejs-pager class="customTable" [pageSize]='pageSizesDefault'
          [totalRecordsCount]='totalPages'
          [currentPage]="currentPage"
          [pageSizes]="pageSizes" (click)="onPageChange($event)">
        </ejs-pager>

        <div
          class="w-full flex pt-[15px] pb-[3px] flex-wrap justify-end items-center gap-[16px]">
          <p class="text-text-primary text-text-sm-regular">
            Amount Due ({{_storeService.curencyCompany | async}})
          </p>
          <p class="text-headline-sm-bold text-text-primary">
            ${{ totalAmount() | decimal:2 | formatNumber }}
          </p>
        </div>
        }
        }
      </div>
    </div>
  </div>
  <div footer>
    <app-inno-modal-footer
      [isDisableSubmit]="!listIndexInvoiceSelected.length"
      (onCancel)="handleCancel()"
      (onSubmit)="handleSubmit()" />
  </div>
</app-inno-modal-wrapper>
