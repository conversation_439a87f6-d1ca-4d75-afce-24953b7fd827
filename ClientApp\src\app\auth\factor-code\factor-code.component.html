<app-innobook-modal-wrapper (onClose)="goBack()">
  <div class="w-full max-w-[400px]">
    <p class="text-headline-sm-bold text-text-primary text-center">
      Enter verification code
    </p>

    <div
      class="mx-auto mt-[12px] w-[50px] h-[50px] flex justify-center items-center bg-bg-brand-strong rounded-full">
      <i class="material-icons !text-bg-brand-primary !text-[30px]">mail</i>
    </div>

    <div class="w-full flex flex-col items-center gap-[4px] mt-[16px]">
      <p class="text-text-sm-regular text-center text-text-tertiary">
        We've sent a code to
      </p>
      <p class="text-text-sm-regular text-center font-semibold">
        {{ data.email }}
      </p>
      <p class="text-text-sm-regular text-center text-text-tertiary">
        {{'VERIFICATION.Instruction'|translate}}
      </p>
    </div>

    <div class="w-full mt-[30px]">
      <code-input #codeInput [codeLength]="6"
        (codeChanged)="onCodeChanged($event)" />
    </div>

    <button
      class="button-size-md button-primary w-full justify-center mt-[34px]">
      {{'VERIFICATION.LoginButton'|translate}}
    </button>

    <div class="w-full flex flex-col items-center gap-[4px] mt-[20px]">
      <p class="text-text-sm-regular text-center text-text-tertiary">
        {{'VERIFICATION.NotReceived'|translate}}
      </p>
      <p
        class="text-text-sm-regular cursor-pointer text-text-brand-primary text-center font-semibold"
        (click)="ResendCode()">
        {{'VERIFICATION.Resend'|translate}}
      </p>
    </div>
  </div>
</app-innobook-modal-wrapper>
