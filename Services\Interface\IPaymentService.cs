﻿using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Payment;
using InnoBook.Request.RequestPayment;
namespace InnoBook.Services.Interface
{
    public interface IPaymentService
    {
        public Task<RequestPayment> CreatedPayment(RequestPayment invoice, string UserId);
        public Task<bool> DeletePaymente(List<Guid?> listInvoice);
        public Task<PaginatedResponse<GetPaymentDTO>> GetAllPayment(PaginatedRequest query, string companyId);
        public Task<PaginatedResponse<GetPaymentDTO>> GetAllPaymentCompany(PaginatedRequest query, string companyId);

    }

}
