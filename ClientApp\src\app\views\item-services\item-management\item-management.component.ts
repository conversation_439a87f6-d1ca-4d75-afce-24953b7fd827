import { StoreService } from 'app/service/store.service';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { ItemAndServiceViewEnum } from 'app/enum/item-service.enum';
import { ServiceService } from './../../../service/service.service';
import { LayoutUtilsService } from './../../../core/services/layout-utils.service';
import { SharedModule } from 'app/module/shared.module';
import { CommonModule } from '@angular/common';
import { Component, DestroyRef, ViewChild } from '@angular/core';
import { ActionEventArgs, GridAllModule, GridComponent, PagerModule, SelectionSettingsModel, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { TranslateService } from '@ngx-translate/core';
import { debounceTime, Subject, Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastService } from 'app/service/toast.service';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Project } from 'app/dto/interface/project.interface';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { ItemService } from 'app/service/item.service';
import { DataService } from 'app/service/data.service';
import { AvatarModule } from 'ngx-avatars';
import { MatTooltipModule } from '@angular/material/tooltip';
import { getNameTaxes } from 'app/utils/invoice.helper';
import { ModifyItemsAndServiceDialog } from 'app/service/dialog/modify-items-and-service.dialog';

@Component({
  selector: 'app-item-management',
  templateUrl: './item-management.component.html',
  styleUrls: ['./item-management.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    PagerModule,
    GridAllModule,
    SharedModule,
    AvatarModule,
    MatTooltipModule,
    InnoInputSearchComponent,
    InnoTableActionComponent,
    InnoSpinomponent
  ],
  providers: [LayoutUtilsService],
})
export class ItemManagementComponent {
  public isLoading = false;
  public sort: SortGird
  public sortOptions: SortSettingsModel = { columns: [] };
  public getNameSelectedTaxes = getNameTaxes
  private _subscriptions: Subscription[] = [];
  public search: string = ''
  private searchSubject = new Subject<string>();
  public idTime: any
  public columnName: string
  public direction: any
  public selectionOptions?: SelectionSettingsModel;
  public projectId: string = ''
  public listProject: Project[] = []
  public dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  @ViewChild('grid') grid?: GridComponent;

  constructor(
    private translate: TranslateService,
    private layoutUtilsService: LayoutUtilsService,
    private router: Router,
    private destroyRef: DestroyRef,
    private activatedRoute: ActivatedRoute,
    private serviceService: ServiceService,
    private toastService: ToastService,
    public _storeService: StoreService,
    private dataService: DataService,
    private itemService: ItemService,
    private modifyItemAndServiceDialog: ModifyItemsAndServiceDialog

  ) {

  }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      this.LoadItem(this.currentPage, "");
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }

  }

  LoadItem(page: number, search, filter?: any) {
    this.isLoading = true
    let payload: Parameter = {
      Page: page,
      PageSize: this.pageSizesDefault,
      Search: search,
      Filter: filter

    }
    this.itemService.GetAllItem(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        this.totalPages = res.totalRecords
        this.dataSource = res.data
        this.isLoading = false
        if (this.columnName) {
          this.sortOptions = {
            columns: [{ field: this.columnName, direction: this.direction }]
          };
        }

      }
    });
  }



  handleSearch(search: string) {
    this.searchSubject.next(search);
  }
  ngOnInit(): void {
    const sb = this.searchSubject.pipe(debounceTime(550)).subscribe((search) => {
      this.search = search ?? "";
      this.LoadItem(this.currentPage, this.search);
    });

    this._subscriptions.push(sb)

    const sub = this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.tab !== 'Service') {
        if (queryParams?.page) {
          this.currentPage = queryParams.page
          const filter = {
            Sort: JSON.stringify(this.sort)
          }
          this.sort ? this.LoadItem(this.currentPage, "", filter) : this.LoadItem(this.currentPage, "")
        }
        else {
          this.LoadItem(this.currentPage, "")
        }
      }
    });
    this._subscriptions.push(sub);

    const rl = this.dataService.reloadItem.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.search = "";
        this.LoadItem(this.currentPage, "");
      }

    })
    this._subscriptions.push(rl);

  }
  creaFormDelete() {

  }

  handleEdit(item: any) {
    const dialogRef = this.modifyItemAndServiceDialog.open({
      mode: ItemAndServiceViewEnum.Item,
      serviceInfo: item
    })
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {
          this.search = "";
          this.LoadItem(this.currentPage, "");
        }
      })
    });
  }

  handleDelete(item: any) {
    const _title = this.translate.instant('ITEMS_SERVICES.DeleteItem');
    const _description = this.translate.instant('COMMON.ConfirmDelete');

    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return

      this.itemService.DeleteItem([item.id]).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.LoadItem(this.currentPage, "");
          this.toastService.showSuccess(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
        }
        else {
          this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
        }
      })
    })
  }

  handleArchive(item: any) {
    this.serviceService.UpdateArchive([item.id]).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.toastService.showSuccess(this.translate.instant("TOAST.Save"), this.translate.instant("TOAST.Success"));
      }
      else {
        this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
      }
    })
  }
  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        const filter = {
          Sort: JSON.stringify(this.sort)
        }
        this.LoadItem(this.currentPage, "", filter)
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      this.LoadItem(this.currentPage, "")
    }

  }
  ngOnDestroy(): void {

    if (this._subscriptions) {

      this._subscriptions.forEach((sb) => sb.unsubscribe());
    }
  }
}
