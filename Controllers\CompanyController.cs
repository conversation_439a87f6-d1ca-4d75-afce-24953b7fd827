﻿using InnoBook.Attributes;
using InnoBook.DTO.UserBusiness;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Interface;
using InnoBook.Request.Company;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class CompanyController(ICompanyService _company, IBusinessService _businessService
        , IHttpContextAccessor httpContextAccessor) : BaseController(httpContextAccessor)
    {

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant)]
        [HttpPost()]
        [Route("UpdateFinancial")]
        public async Task<IActionResult> UpdateFinancial(RequestCompany company)
        {
            company.Id = Guid.Parse(IdCompany);
            await _company.UpdateFinancial(company);
            return Ok();
        }

        [RequiredRoles(UserBusinessRole.All)]
        [HttpGet("GetCurrencyCompany")]
        public async Task<IActionResult> GetCurrencyCompany()
        {
            try
            {
              var data = await  _company.GetCurrencyCompany(Guid.Parse(IdCompany));

              if (data == null)
              {
               return Ok(null);
               }
              var result = new
              {
                  currency= data
              };
              return Ok(result);
            }catch(Exception ex)
            {
                return BadRequest(ex);
            } 


        }

        [RequiredRoles(UserBusinessRole.All)]
        [HttpGet("GetInforCompany")]
        public async Task<IActionResult> GetInforCompany()
        {
            var data = await _company.GetCompanyById(Guid.Parse(IdCompany));
            return Ok(data); ;
        }

        [RequiredRoles(UserBusinessRole.All)]
        [HttpPost]
        public async Task<ActionResult<RequestCompany>> PostCompany(RequestCompany company)
        {
            await _company.Insert(company,IdUser);
            var userBusiness = new CreateUserBusinessDTO()
            {
                CompanyId = company.Id,
                UserId = Guid.Parse(IdUser),
                Status = UserBusinessStatus.Active,
                UpdatedBy = Guid.Parse(IdUser),
                CreatedBy = Guid.Parse(IdUser),
                Role = UWIPConstants.Admin
            };

            await _businessService.CreateUserBusinessCompany(Guid.Parse(IdUser), userBusiness);

            return company;

        }
        [RequiredRoles(UserBusinessRole.Admin)]
        [HttpPut]
        [Route("RemoveImgCompany")]
        public async Task<ActionResult> RemoveImgCompany()
        {
            var reuslt = await _company.RemoveImgCompany(IdCompany);
            return Ok(reuslt);
        }
        

        [RequiredRoles(UserBusinessRole.Admin)]
        [HttpPut]
        public async Task<ActionResult> PutCompany(RequestCompany company)
        {
           
            var reuslt =await _company.Update(company,IdCompany);
            return Ok(reuslt);
        }
    }
}
