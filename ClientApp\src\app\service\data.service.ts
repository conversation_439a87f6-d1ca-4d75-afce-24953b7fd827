import { Injectable, signal } from "@angular/core";
import { InvoiceViewEnum } from "app/enum/invoice.enum";
import { TimeTrackingViewEnum } from "app/enum/time-tracking.enum";
import { BehaviorSubject, Subject } from "rxjs";
import { TimeTrackingFilter } from "../dto/interface/timeTrackingFilter.interface";
import { CreateNewTimerInfo } from "../dto/interface/createNewTimerInfo.interface";
import { InvoiceFilter } from "../dto/interface/invoiceFilter.interface";

@Injectable({ providedIn: 'root' })
export class DataService {

  /* ======= START TIME TRACKING BLOCK ======= */
  private behaviorTimeTrackingTypeView = new BehaviorSubject<TimeTrackingViewEnum>(TimeTrackingViewEnum.Day);
  private behaviorTimeTrackingDate = new BehaviorSubject(undefined);
  private behaviorTimeTrackingFilter = new BehaviorSubject<Partial<TimeTrackingFilter>>({
    typeView: TimeTrackingViewEnum.Day,
    userSelected: undefined,
    clientSelected: undefined,
    projectSelected: undefined,
    startDate: undefined,
    endDate: undefined,
    dateSelected: new Date(),
    textSearch: "",
  });
  private behaviorTimeTrackingCreateTimer = new BehaviorSubject<CreateNewTimerInfo | undefined>(undefined);
  private behaviorisInternalClient = new BehaviorSubject<boolean>(false);
  private behaviorTimeTrackingShowingTimer = new BehaviorSubject<boolean>(false);
  public reloadItem = new Subject<boolean>();
  public reloadService = new Subject<boolean>();
  isEstimate = signal(false);
  SetisInternalClient(value: boolean) {
    this.behaviorisInternalClient.next(value);
  }
  getisInternalClient() {
    return this.behaviorisInternalClient.value;
  }

  SetNewTimeTrackingTypeView(value: TimeTrackingViewEnum) {
    this.behaviorTimeTrackingTypeView.next(value);
  }

  SetNewTimeTrackingDate(value: any) {
    this.behaviorTimeTrackingDate.next(value);
  }

  SetNewTimeTrackingShowingTimer(value: boolean) {
    this.behaviorTimeTrackingShowingTimer.next(value);
  }

  SetNewTimeTrackingCreateTimerInfo(value: CreateNewTimerInfo | undefined) {
    this.behaviorTimeTrackingCreateTimer.next(value);
  }

  SetNewTimeTrackingFilter(value: TimeTrackingFilter) {
    this.behaviorTimeTrackingFilter.next(value);
  }

  triggerRefreshListTimeTracking() {
    this.behaviorTimeTrackingFilter.next({ ...this.behaviorTimeTrackingFilter.value });
  }

  GetTimeTrackingTypeView() {
    return this.behaviorTimeTrackingTypeView.asObservable();
  }

  GetTimeTrackingDate() {
    return this.behaviorTimeTrackingDate.asObservable();
  }

  GetTimeTrackingShowingTimer() {
    return this.behaviorTimeTrackingShowingTimer.asObservable();
  }

  GetTimeTrackingShowingTimerValue() {
    return this.behaviorTimeTrackingShowingTimer.value;
  }

  GetTimeTrackingCreateTimerInfo() {
    return this.behaviorTimeTrackingCreateTimer.asObservable();
  }

  GetTimeTrackingCreateTimerInfoValue() {
    return this.behaviorTimeTrackingCreateTimer.value;
  }

  GetTimeTrackingFilter() {
    return this.behaviorTimeTrackingFilter.asObservable();
  }

  GetTimeTrackingFilterValue() {
    return this.behaviorTimeTrackingFilter.value;
  }
  SetResume(data: any) {
    localStorage.setItem("ResumeData", JSON.stringify(data))
  }
  getResume() {
    if (localStorage.getItem("ResumeData"))
      return JSON.parse(localStorage.getItem("ResumeData")?.toString()!)
  }



  /*  END TIME TRACKING BLOCK */

  /* ======= START INVOICE BLOCK =======  */
  private behaviorInvoiceFilter = new BehaviorSubject<Partial<InvoiceFilter>>({
    typeView: InvoiceViewEnum.Created_Tab,
    textSearch: "",
  });

  SetNewInvoiceFilter(value: Partial<InvoiceFilter>) {
    const currentData = this.behaviorInvoiceFilter.value
    this.behaviorInvoiceFilter.next({ ...currentData, ...value });
  }

  ResetInvoiceFilter() {
    this.behaviorInvoiceFilter.next({});
  }

  triggerRefreshInvoice() {
    this.behaviorInvoiceFilter.next({ ...this.behaviorInvoiceFilter.value });
  }

  GetInvoiceFilter() {
    return this.behaviorInvoiceFilter.asObservable();
  }

  GetInvoiceFilterValue() {
    return this.behaviorInvoiceFilter.value;
  }
  /*  END INVOICE BLOCK */
}
