using InnoBook.DTO.Mail;
using InnoBook.Services.Interface;
using System.Security.Cryptography;
using System.Text;

namespace UData.RPA.Web.Mobile.Middleware
{
    public class CSPMiddleware(RequestDelegate next, IWebHostEnvironment env, IMailService mailService)
    {
        public async Task Invoke(HttpContext context)
        {
            if (env.IsDevelopment())
            {
                context.Response.Headers.Append("Content-Security-Policy",
                    $"default-src 'self'; " +
                    $"style-src 'self' 'unsafe-hashes' 'unsafe-inline'; " +
                    $"style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com https://accounts.google.com;" +
                    $"script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                    $"script-src-elem 'self' https://fonts.googleapis.com https://fonts.googleapis.com https://accounts.google.com " +
                    $"https://appleid.cdn-apple.com; " +
                    $"connect-src 'self' https://localhost:* http://localhost:* ws://localhost:*; " +
                    $"font-src 'self' data:; " +
                    $"frame-src 'self' https://accounts.google.com; " +
                    $"img-src * data: blob:; " +
                    $"frame-ancestors 'self' https://accounts.google.com;");

                context.Response.Headers.Append("X-Frame-Options", "SAMEORIGIN");
                context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
                context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
                context.Response.Headers.Append("XReferrer-Policy", "strict-origin-when-cross-origin");
                context.Response.Headers.Append("X-Permitted-Cross-Domain-Policies", "none");
                context.Response.Headers.Append("Permissions-Policy", "geolocation=(), camera=()");
            }
            else { 
                var nonce = Convert.ToBase64String(RandomNumberGenerator.GetBytes(16));
                context.Items["Nonce"] = nonce;

                context.Response.Headers.Append("Content-Security-Policy",
                    $"default-src 'self'; " +
                    $"style-src 'self' 'unsafe-hashes' 'unsafe-inline'; " +
                    $"style-src-elem 'self' 'unsafe-inline' https://fonts.googleapis.com https://accounts.google.com;" +
                    $"script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
                    $"script-src-elem 'self' https://fonts.googleapis.com https://fonts.googleapis.com https://accounts.google.com " +
                    $"https://appleid.cdn-apple.com;" +
                    $"connect-src 'self'; " +
                    $"font-src 'self' data:; " +
                    $"frame-src 'self' https://accounts.google.com; " +
                    $"img-src * data: blob:;");


                context.Response.Headers.Append("X-Frame-Options", "SAMEORIGIN");
                context.Response.Headers.Append("X-XSS-Protection", "1; mode=block");
                context.Response.Headers.Append("X-Content-Type-Options", "nosniff");
                context.Response.Headers.Append("XReferrer-Policy", "strict-origin-when-cross-origin");
                context.Response.Headers.Append("X-Permitted-Cross-Domain-Policies", "none");
                context.Response.Headers.Append("Permissions-Policy", "geolocation=(), camera=()");
            }
            await next(context);
          
        }
    }
}