﻿using InnoBook.DTO;
using InnoBook.DTO.Client;
using InnoBook.DTO.CoreModel;
using InnoBook.Request.Client;
namespace InnoBook.Services.Interface
{
    public interface IClientService
    {
        Task<PaginatedResponse<RequestAllClient>> GetAllClientAsync(GetClientRequestParam query,string companyId);
        Task<bool> Insert(RequestClient model, string IdUser, string IdCompany);
        Task<CalculationClient> CalculationClient(string companyId);
        Task<bool> Update(RequestClient model, string UserId);
        Task<GetClientDTO> GetClientById( string Id);
        Task<bool> DeleteClient(List<Guid?> listClient);
        List<GetClientTimeTracking> ClientTimeTracking(Guid companyId);
    }
}
