import { ToastService } from 'app/service/toast.service';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { Component, DestroyRef, ElementRef, EventEmitter, inject, Input, Output, ViewChild, ɵallowSanitizationBypassAndThrow } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { CompanyService } from 'app/service/company.service';

@Component({
  selector: 'app-inno-upload',
  templateUrl: './inno-upload.component.html',
  styleUrls: ['./inno-upload.component.scss'],
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [TranslateModule]
})
export class InnoUploadComponent {
  @Input() isMultiple?: boolean = false;
  @Input() imageUrl?: string;
  @Input() canEdit?: boolean = false;
  @Input() isBussiness?: boolean = false;
  @Input() accept?: 'image' | 'document' = 'image';
  @Input() placeholder?: string = 'COMMON.UploadPicture';
  @Output() onChange = new EventEmitter<any>();

  public fileDropped = false;
  public previewFileUpload: Array<string | ArrayBuffer> | null = [];
  private layoutUtilsService = inject(LayoutUtilsService)
  private translate = inject(TranslateService);
  private toastService = inject(ToastService);
  private companyService = inject(CompanyService);
  destroyRef = inject(DestroyRef);
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  get acceptInput() {
    switch (this.accept) {
      case 'image':
        return 'image/*'
      case 'document':
        return 'application/*'
      default:
        return '*'
    }
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    const dropZone = event.currentTarget as HTMLElement;
    dropZone.classList.add('active');
  }

  onDragLeave(event: DragEvent) {
    const dropZone = event.currentTarget as HTMLElement;
    dropZone.classList.remove('active');
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    const dropZone = event.currentTarget as HTMLElement;
    dropZone.classList.remove('active');

    const files = event.dataTransfer?.files;
    if (files && files.length > 0) {
      this.processFile(files);
      this.fileDropped = true;
    }
  }

  onFileSelect(event: Event) {
    const target = event.target as HTMLInputElement;
    this.processFile(target.files as FileList ?? []);
    this.fileDropped = true;
  }

  triggerFileInput() {
    this.fileInput.nativeElement.click();
  }

  processFile(files: FileList | undefined[]) {
    if (!files.length) return
    this.onChange.emit(files)

    switch (this.accept) {
      case 'image': {
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const reader = new FileReader();
          reader.onload = () => {
            this.previewFileUpload = []
            this.imageUrl = ""
            this.previewFileUpload?.push(reader.result as any);
          };
          reader.readAsDataURL(file);
        }
        return
      }

      default: { }
    }
  }
  handleRemoveFile(e: any) {
    e.stopPropagation()
    const _title = this.translate.instant('TOAST.Delete');
    const _description = this.translate.instant('TOAST.DeleteImg');
    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return

      if (this.isBussiness && this.canEdit) {

        this.companyService.RemoveImgCompany().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
          if (res) {
            this.imageUrl = undefined;
            this.fileDropped = false
            this.previewFileUpload = []
            this.onChange.emit(null)
            this.toastService.showSuccess(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
          }
          else {
            this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
          }
        })
      }

    })


  }
  handleRemoveFileUpload(e: any) {
    e.stopPropagation()
    this.fileDropped = false
    this.imageUrl = undefined
    this.previewFileUpload = []
    this.onChange.emit(null)
  }
}
