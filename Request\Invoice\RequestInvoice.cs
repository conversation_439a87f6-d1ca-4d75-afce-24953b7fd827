﻿using InnoBook.Entities;
using System.ComponentModel.DataAnnotations.Schema;

namespace InnoBook.Request.Invoice
{
    public class RequestInvoice
    {
        public Guid Id { get; set; }
        public Guid CompanyId { get; set; }
        public Guid ClientId { get; set; }
        public Guid? ProjectId { get; set; }
        public Guid? ItemId { get; set; }
        public Guid? ContractorId { get; set; }

        public string? InvoiceNumber { get; set; }
        public bool IsEstimate { get; set; } = false;
        public string? Img { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public string? Reference { get; set; }
        public string? Notes { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal Rate { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public string? Description { get; set; }
        public bool isArchive { get; set; }
        public bool isActive { get; set; } = true;
        public bool isPaid { get; set; } = false;
        public decimal TimeAmount { get; set; }
        public int Status { get; set; } // Consider an enum for invoice status
        public int Position { get; set; }
        public string? base64 { get; set; }

        public string? type { get; set; }
        public string? filename { get; set; }
        public List<Payment>? Payments { get; set; }
        public List<ItemInvoice>? ItemInvoices { get; set; }
    }
}
