<app-inno-popover
  position="bottom-start"
  [content]="templateSearchProject"
  [isClickOnContentToClose]="false"
  [isClearPadding]="true"
  (onOpen)="loadData()">

  @if(templateTrigger) {
  <ng-container target *ngTemplateOutlet="templateTrigger;"></ng-container>
  } @else {
  <button target class="dropdown-invisible flex gap-[4px]">
    Select project or client <img class="w-[16px] translate-y-[2px]"
      src="../../../assets/img/icon/ic_arrow_down_gray.svg" alt="Icon">
  </button>
  }

  <ng-template #templateSearchProject>
    <div class="min-w-[320px]">
      <app-inno-input-search-result
        [placeholder]="'TAG.Search'|translate"
        [data]="[]"
        (onChange)="handleSearch($event)"
        [isNotFound]="!listOptionPreview.length"
        [isEmptyData]="!listOptionOriginal.length"
        [isLoading]="isLoading"
        [defaultValue]="defaultTextSearch"
        [optionTemplate]="optionTemplate"
        [footerTemplate]="footerTemplate">
        <ng-template #optionTemplate let-item>
          <div class="w-full py-[6px]">
            <app-inno-form-checkbox>IOS platform</app-inno-form-checkbox>
          </div>
        </ng-template>
        <ng-template #footerTemplate>
          <div class="w-full flex flex-col gap-[12px]">
            <button
              class="p-[12px] gap-[12px] text-text-brand-primary text-text-sm-semibold w-full flex items-center hover:bg-bg-brand-primary rounded-md cursor-pointer">
              <img src="../../../assets/img/icon/ic_add_green.svg" alt="Icon">
              {{'TAG.CreateTag'|translate}}
            </button>
            <div class="w-full flex justify-end gap-[6px]">
              <button class="button-outline button-size-md"
                (click)="handleCloseSearchResult()">
                {{'BUTTON.Cancel'|translate}}
              </button>
              <button class="button-primary button-size-md">
                {{'BUTTON.Save'|translate}}
              </button>
            </div>
          </div>
        </ng-template>
      </app-inno-input-search-result>
    </div>
  </ng-template>
</app-inno-popover>
