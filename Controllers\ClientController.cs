﻿using InnoBook.Attributes;
using InnoBook.DTO.Client;
using InnoBook.DTO.CoreModel;
using InnoBook.Request.Client;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class ClientController(IClientService _client, IHttpContextAccessor httpContextAccessor) : BaseController(httpContextAccessor)
    {

        [RequiredRoles(UserBusinessRole.Admin)]
        [HttpDelete("DeleteClient")]
        public async Task<IActionResult> DeleteClient(List<Guid?> listClient)
        {
            try
            {
                var result = await _client.DeleteClient(listClient);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return StatusCode(500, ex.Message);
            }
        }

        [RequiredRoles(UserBusinessRole.All)]
        [HttpGet("ClientTimeTracking")]
        public async Task<ActionResult> ClientTimeTracking()
        {
            var data =  _client.ClientTimeTracking(Guid.Parse(IdCompany));
            return Ok(data);
        }

        [RequiredRoles(UserBusinessRole.Admin)]
        [HttpPost("UpdateClient")]
        public async Task<ActionResult> UpdateClient(RequestClient client)
        {
            try
            {
                var result = await _client.Update(client,IdUser);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager)]
        [HttpGet("AllClient")]
        public async Task<ActionResult<PaginatedResponse<IActionResult>>> GetAllClient([FromQuery] GetClientRequestParam query)
        {
            query.Role = HttpContext.GetBusinessRole();
            query.UserId = IdUser;
            var cliens = await _client.GetAllClientAsync(query, IdCompany);
            return Ok(cliens);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant)]
        [HttpGet("CalculationClient")]
        public async Task<ActionResult> CalculationClient()
        {
            var project = await _client.CalculationClient(IdCompany);
            return Ok(project);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant)]
        [HttpGet("GetClientById")]
        public async Task<ActionResult> GetClientById(string id)
        {
            var data = await _client.GetClientById(id);
            return Ok(data);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant)]
        [HttpPost("Create")]
        public async Task<ActionResult<RequestClient>> PostClient(RequestClient client)
        {
            try
            {
                var result = await _client.Insert(client, IdUser, IdCompany);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }
    }
}
