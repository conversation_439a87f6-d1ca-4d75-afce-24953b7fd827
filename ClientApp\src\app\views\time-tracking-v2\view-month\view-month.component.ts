import { Component, inject, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, ViewChild } from '@angular/core';
import { CellClickEventArgs, DayService, EventSettingsModel, MonthService, RenderCellEventArgs, ScheduleComponent, ScheduleModule } from '@syncfusion/ej2-angular-schedule';
import { SharedModule } from 'app/module/shared.module';
import { DataService } from 'app/service/data.service';
import { ActivatedRoute } from '@angular/router';
import { TimetrackingService } from 'app/service/timetracking.service';
import { Subscription } from 'rxjs';
import { Parameter, TimeTrackingQueryParam } from 'app/dto/interface/queryParameter.interface';
import { TimeTrackingViewEnum } from 'app/enum/time-tracking.enum';
import { formatDateFilter, getLabelDate, isSameDate, sumHours } from 'app/helpers/common.helper';
import { GroupDayTrackingComponent } from '../group-day-tracking/group-day-tracking.component';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { InnoPaginationComponent } from 'app/component/inno-pagination/inno-pagination.component';
import { createElement, Internationalization } from '@syncfusion/ej2-base';
import { TimeTrackingProvider } from '../time-tracking.provider';
import { AddEntryDialog } from '../../../service/dialog/add-entry.dialog';

@Component({
  selector: 'app-view-month',
  templateUrl: './view-month.component.html',
  styleUrls: ['./view-month.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    ScheduleModule,
    GroupDayTrackingComponent,
    InnoSpinomponent,
    InnoPaginationComponent
  ],
  providers: [
    DayService, MonthService,
  ]
})
export class ViewMonthComponent implements OnInit, OnDestroy {
  timeId: any
  public dataSource: any[] = []
  public totalPages = 1;
  private pageSizesDefault: number = 10
  public isLoading: boolean = false
  public eventSettings: EventSettingsModel = { dataSource: [] };
  public selectedScheduleDate: Date = new Date()
  public dataSourceSelectedScheduleDate: any[] = []
  public isLoadingSelectedScheduleDate: boolean = false
  public labelSelectedScheduleDate: string = getLabelDate(this.selectedScheduleDate)

  private dataService = inject(DataService)
  private activatedRoute = inject(ActivatedRoute);
  private timeTrackingServices = inject(TimetrackingService)
  private timeTrackingProvider = inject(TimeTrackingProvider)
  private unsubscribe: Subscription[] = [];
  public currentDate: Date = new Date();
  @ViewChild('scheduleObj') scheduleObj!: ScheduleComponent;
  public views: Array<string> = ['Month'];
  constructor(private addEntryDialog: AddEntryDialog) { }

  public instance: Internationalization = new Internationalization();
  getDateHeaderText: Function = (value: Date) => {
    return this.instance.formatDate(value, { skeleton: "Ed" });
  };

  onRenderCell(args: RenderCellEventArgs): void {
    if (args.elementType == 'workCells' || args.elementType == 'monthCells') {
      const listTrackingOfDay = this.dataSource.filter(x => isSameDate(args.date!, new Date(x.date)))

      const listHour = listTrackingOfDay.map(x => x.endTime)
      const ele: HTMLElement = createElement('div', {
        innerHTML: `
          <button class="addButton">
            <img class="w-[16px]" src="../../../../assets/img/icon/ic_add_green.svg" alt="Icon">
            Add
          </button>
          ${listHour?.length ? `
              <div class="translate-y-[10px]">
                <p class="textHour">
                  ${sumHours(listHour)}
                </p>
                <p class="textDescription">
                  ${listHour.length} logs
                </p>
              </div>`: ''
          }
        `,
        className: 'customScheduleCell'
      });
      (args.element).appendChild(ele);
      const button = ele.querySelector('.addButton');
      if (button) {
        button.addEventListener('click', (e) => {
          e.stopPropagation()
          this.addEntryDialog.open({ date: args.date });
        });
      }
    }
  }
  handleFocus() {
    if (this.selectedScheduleDate) {

      this.timeId = setTimeout(() => {
        const today = new Date(this.selectedScheduleDate);
        today.setHours(0, 0, 0, 0);
        const timestamp = today.getTime();

        const selector = `.e-work-cells[data-date="${timestamp}"]`;
        const cell = document.querySelector(selector);

        if (cell instanceof HTMLTableCellElement) {
          this.scheduleObj.selectCell(cell);
        }
      }, 10);
    }

  }

  ngOnInit(): void {
    this.unsubscribe.push(
      this.dataService.GetTimeTrackingFilter().subscribe((filter) => {
        if (filter.typeView !== TimeTrackingViewEnum.Month) return

        this.timeTrackingProvider.reloadTimeTrackingData()
          .subscribe({
            next: (res: any) => {
              if (res) {
                const data = res?.data ?? []
                this.dataSource = data
                this.scheduleObj.refresh();
                this.handleFocus();
              }
            },
            complete: () => {
              this.isLoading = false
            }
          })

        this.getTimeTrackingForSelectedScheduleDate()
        const dateSelected = filter.dateSelected ?? undefined
        let nextMonth = new Date(dateSelected);
        nextMonth.setMonth(nextMonth.getMonth());
        if (this.scheduleObj) {
          this.scheduleObj.selectedDate = nextMonth;
        }
      })
    )
  }

  async getTimeTrackingForSelectedScheduleDate(args?: {
    page?: number, search?: string
  }) {
    this.isLoadingSelectedScheduleDate = true
    await new Promise(resolve => setTimeout(resolve, 100));

    const page = this.activatedRoute.snapshot.queryParams['page'] || 1;
    const currentFilter = this.dataService.GetTimeTrackingFilterValue()
    const clientSelected = currentFilter.clientSelected ?? undefined
    const projectSelected = currentFilter.projectSelected ?? undefined
    const userSelected = currentFilter.userSelected ?? undefined

    const queryParams: TimeTrackingQueryParam = {
      Page: page,
      PageSize: this.pageSizesDefault,
      Search: args?.search ?? '',
      filterDate: formatDateFilter(this.selectedScheduleDate),
      loggedBy: userSelected,
      clientId: clientSelected,
      projectId: projectSelected,
    }

    this.unsubscribe.push(
      this.timeTrackingServices.GetAllTimeTracking(queryParams).subscribe({
        next: (res: any) => {
          this.totalPages = res?.totalPage ?? 1
          this.dataSourceSelectedScheduleDate = res?.data ?? []
        },
        complete: () => {
          this.isLoadingSelectedScheduleDate = false
        }
      })
    )
  }

  onCellClick(args: CellClickEventArgs): void {
    args.cancel = true;
    this.selectedScheduleDate = args.startTime ?? new Date()
    this.labelSelectedScheduleDate = getLabelDate(this.selectedScheduleDate)
    this.getTimeTrackingForSelectedScheduleDate()
    this.scheduleObj.selectCell(args.element as any)
  }

  onCellDoubleClick(args: any): void {
    args.cancel = true;
  }

  handleChangePagesize(pageSize: number) {
    this.pageSizesDefault = pageSize
    this.triggerRefreshListTimeTracking()
  }

  triggerRefreshListTimeTracking() {
    this.dataService.triggerRefreshListTimeTracking()
  }

  ngOnDestroy(): void {
    this.unsubscribe.forEach((s) => s.unsubscribe());
    if (this.timeId) {
      clearTimeout(this.timeId)
    }
  }
}
