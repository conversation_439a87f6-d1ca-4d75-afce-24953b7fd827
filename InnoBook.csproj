<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <TypeScriptTarget>ES2021</TypeScriptTarget>
  </PropertyGroup>

  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <TypeScriptTarget>ES2021</TypeScriptTarget>
  </PropertyGroup>

  <PropertyGroup Label="Configuration" Condition="'$(Configuration)|$(Platform)'=='Test|AnyCPU'">
    <TypeScriptTarget>ES2021</TypeScriptTarget>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <SpaRoot>ClientApp\</SpaRoot>
    <ImplicitUsings>enable</ImplicitUsings>
	  <DefaultItemExcludes>$(DefaultItemExcludes);$(SpaRoot)node_modules\**</DefaultItemExcludes>

	  <!-- Set this to true if you enable server-side prerendering -->
	  <BuildServerSideRenderer>false</BuildServerSideRenderer>
	  <AssemblyName>InnoBook</AssemblyName>
	  <StartupObject></StartupObject>
	  <Configurations>Debug;Release;Test</Configurations>
	  <TypeScriptModuleKind>commonjs</TypeScriptModuleKind>
  </PropertyGroup>
	<ItemGroup>
		<Content Update="appsettings.json">
			<TransformOnBuild>true</TransformOnBuild>
		</Content>
		<Content Update="appsettings.Test.json" CopyToPublishDirectory="Never">
			<IsTransformFile>true</IsTransformFile>
			<DependentUpon>appsettings.json</DependentUpon>
		</Content>
		<Content Update="appsettings.Development.json" CopyToPublishDirectory="Never">
			<IsTransformFile>true</IsTransformFile>
			<DependentUpon>appsettings.json</DependentUpon>
		</Content>
		<Content Update="appsettings.Release.json" CopyToPublishDirectory="Never">
			<IsTransformFile>true</IsTransformFile>
			<DependentUpon>appsettings.json</DependentUpon>
		</Content>
	</ItemGroup>

	<ItemGroup>
    <PackageReference Include="AWSSDK.S3" Version="*******" />
    <PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
    <PackageReference Include="MailKit" Version="4.12.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.SpaProxy" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.SpaServices.Extensions" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.SlowCheetah" Version="4.0.50">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="MimeKit" Version="4.12.0" />
    <PackageReference Include="Minio" Version="6.0.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.8" />
    <PackageReference Include="Stripe.net" Version="48.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
    <PackageReference Include="Syncfusion.DocIORenderer.Net.Core" Version="29.2.4" />
    <PackageReference Include="System.Drawing.Common" Version="9.0.5" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.10.0" />
    <PackageReference Include="TimeZoneConverter" Version="7.0.0" />
  </ItemGroup>

  <ItemGroup>
    <!-- Don't publish the SPA source files, but do show them in the project files list -->
    <Compile Remove="innologiciel.client\**" />
    <Compile Remove="InnoLogiciel.Server\**" />
    <Content Remove="$(SpaRoot)**" />
    <Content Remove="innologiciel.client\**" />
    <Content Remove="InnoLogiciel.Server\**" />
    <EmbeddedResource Remove="innologiciel.client\**" />
    <EmbeddedResource Remove="InnoLogiciel.Server\**" />
    <None Remove="$(SpaRoot)**" />
    <None Remove="innologiciel.client\**" />
    <None Remove="InnoLogiciel.Server\**" />
    <None Include="$(SpaRoot)**" Exclude="$(SpaRoot)node_modules\**" />
  </ItemGroup>

  <ItemGroup>
    <None Update="EmailTemplates\InviteMember.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="EmailTemplates\RecoverPassword.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="EmailTemplates\SendCodeLogin.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
       <None Update="EmailTemplates\InvoiceEmail.html">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Files\DKIM.txt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="WordTemplates\InnoBooks_Invoice_.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
<ItemGroup>
  <Content Include="Uploads\**\*" CopyToOutputDirectory="PreserveNewest" />
</ItemGroup>
	<Target Name="DebugEnsureNodeEnv" BeforeTargets="Build" Condition=" '$(Configuration)' == 'Debug' And !Exists('$(SpaRoot)node_modules') ">
		<!-- Ensure Node.js is installed -->
		<Exec Command="node --version" ContinueOnError="true">
			<Output TaskParameter="ExitCode" PropertyName="ErrorCode" />
		</Exec>
		<Error Condition="'$(ErrorCode)' != '0'" Text="Node.js is required to build and run this project. To continue, please install Node.js from https://nodejs.org/, and then restart your command prompt or IDE." />
		<Message Importance="high" Text="Restoring dependencies using 'npm'. This may take several minutes..." />
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm install --force" />
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm run build innobook -- --configuration debug" />
	</Target>

	<Target Name="PublishRunWebpack" AfterTargets="ComputeFilesToPublish">
		<!-- As part of publishing, ensure the JS resources are freshly built in production mode -->
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm install --force" />
		<Exec WorkingDirectory="$(SpaRoot)" Condition=" '$(Configuration)' == 'Release'" Command="npm run build innobook -- --configuration production" />
		<Exec WorkingDirectory="$(SpaRoot)" Condition=" '$(Configuration)' == 'Test'" Command="npm run build innobook -- --configuration test" />
		<Exec WorkingDirectory="$(SpaRoot)" Command="npm run build:ssr -- --prod" Condition=" '$(BuildServerSideRenderer)' == 'true' " />

		<!-- Include the newly-built files in the publish output -->
		<ItemGroup>
			<DistFiles Include="$(SpaRoot)dist\**; $(SpaRoot)dist-server\**" />
			<DistFiles Include="$(SpaRoot)node_modules\**" Condition="'$(BuildServerSideRenderer)' == 'true'" />
			<ResolvedFileToPublish Include="@(DistFiles->'%(FullPath)')" Exclude="@(ResolvedFileToPublish)">
				<RelativePath>%(DistFiles.Identity)</RelativePath>
				<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
			</ResolvedFileToPublish>
		</ItemGroup>
	</Target>
</Project>
