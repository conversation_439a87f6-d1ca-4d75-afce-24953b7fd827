﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace InnoLogiciel.Server.Migrations
{
    /// <inheritdoc />
    public partial class removecompanyIdTaxe : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_company_tax_companies_company_id",
                table: "company_tax");

            migrationBuilder.AlterColumn<Guid>(
                name: "id",
                table: "company_tax",
                type: "uuid",
                nullable: false,
                defaultValueSql: "gen_random_uuid()",
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddForeignKey(
                name: "fk_company_tax_companies_company_id",
                table: "company_tax",
                column: "company_id",
                principalTable: "companies",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "fk_company_tax_companies_company_id",
                table: "company_tax");

            migrationBuilder.AlterColumn<Guid>(
                name: "id",
                table: "company_tax",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldDefaultValueSql: "gen_random_uuid()");

            migrationBuilder.AddForeignKey(
                name: "fk_company_tax_companies_company_id",
                table: "company_tax",
                column: "company_id",
                principalTable: "companies",
                principalColumn: "id");
        }
    }
}
