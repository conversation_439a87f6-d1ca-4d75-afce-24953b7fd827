<div class="overflow-x-auto">
  <ejs-schedule
    #scheduleObj
    currentView="Month"
    [selectedDate]="currentDate"
    class="customSchedule"
    width="100%"
    height="650px"
    [views]="views"
    [showHeaderBar]="false"
    [workDays]="[0, 1, 2, 3, 4, 5, 6]"
    [allowDragAndDrop]="false"
    [allowMultiCellSelection]="false"
    [allowMultiRowSelection]="false"
    (renderCell)="onRenderCell($event)"
    (cellClick)="onCellClick($event)"
    (cellDoubleClick)="onCellDoubleClick($event)">
  </ejs-schedule>
</div>

<div class="w-full mt-[24px] h-auto ">
  @if(isLoadingSelectedScheduleDate) {
  <div class="w-full py-3 flex justify-center items-center">
    <app-inno-spin />
  </div>
  } @else {
  <app-group-day-tracking [dayName]="labelSelectedScheduleDate"
    [dataSource]="dataSourceSelectedScheduleDate" />
  @if(dataSourceSelectedScheduleDate.length) {
  <div class="container-full mt-[12px]">
    <app-inno-pagination
      [totalPages]="totalPages"
      (onChangePagesize)="handleChangePagesize($event)"
      (callbackGoToPage)="triggerRefreshListTimeTracking()" />
  </div>
  }
  }
</div>
