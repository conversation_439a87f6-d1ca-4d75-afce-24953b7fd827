import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { environment } from 'environments/environment';
import { Member } from '../dto/interface/member.interface';
import { Observable } from 'rxjs';
import { MemberBusiness } from '../dto/interface/memberBusiness.interface';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class MemberService {

  constructor() { }
  private http = inject(HttpClient)

  InviteMember(payload: any): Observable<MemberBusiness> {
    return this.http.post<MemberBusiness>(UrlApi + `/Member/InviteMember`, payload);
  }
  CountMemberBusiness(): Observable<Number> {
    return this.http.get<Number>(UrlApi + `/Member/CountMemberBusiness`);
  }


  GetAllMembersByProjectId(payload: Parameter) {
    return this.http.get(UrlApi + `/Member/GetAllMembersByProjectIdPage=${payload.Page}&PageSize=${payload.PageSize}&Search=${payload.Search}&ProjectId=${payload.ProjectId}`);
  }
}
