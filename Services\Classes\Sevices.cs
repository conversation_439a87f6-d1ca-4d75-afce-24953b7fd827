using InnoBook.DTO;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Service;
using InnoBook.Entities;
using InnoBook.Extension;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;

namespace InnoBook.Services.Classes
{
    public class Sevices(InnoLogicielContext context) : IService
    {

        private ServiceDTO MapToServiceDTO(Service service)
        {
            return new ServiceDTO
            {
                Id = service.Id,
                ServiceName = service.ServiceName,
                Description = service.Description,
                Rate = service.Rate,
                CreatedBy = service.CreatedBy,
                CreatedAt = service.CreatedAt,
                isActive = service.isActive,
                Taxes = service.Taxes.Select(t => new TaxItem
                {
                    Id = t.Id,
                    CompanyTaxId = t.CompanyTaxId,
                    Name = t.CompanyTax.Name,
                    TaxeNumber = t.CompanyTax.TaxeNumber
                }).ToList()
            };
        }


        public async Task<ServiceDTO> CreatedService(ServiceDTO dto, Guid companyId, string userId)
        {
            using (var transaction = context.Database.BeginTransaction())
            {
                try
                {
                    // Handle for case user add new companyTax
                    var addedCompanyTaxes = await HandleAddOrUpdateTaxes(dto.Taxes, companyId, userId);

                    var newService = new Service
                    {
                        Id = Guid.NewGuid(),
                        ServiceName = dto.ServiceName,
                        Description = dto.Description,
                        Rate = dto.Rate,
                        CompanyId = companyId,
                        CreatedBy = userId,
                        CreatedAt = DateTime.UtcNow,
                        isActive = true,
                        Taxes = addedCompanyTaxes
                    };

                    context.Services.Add(newService);
                    await context.SaveChangesAsync();
                    await transaction.CommitAsync();
                    var serviceDetail = await GetServiceById(newService.Id);
                    return serviceDetail;
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
        }

        public async Task<PaginatedResponse<ServiceDTO>> GetAllService(GetServicesRequestParams query, Guid companyId)
        {
            var data = context.Services.Include(c => c.Projects)
                .Where(c => c.CompanyId == companyId && c.isActive == true)
                .Include(x => x.Taxes).ThenInclude(x => x.CompanyTax)
                .AsQueryable();

            // Filtrage par recherche
            if (!string.IsNullOrEmpty(query.Search))
            {
                data = data.Where(c => c.ServiceName.ToLower().Contains(query.Search.ToLower()));
            }

            // Filter Project Id
            if (query.ProjectId != null)
            {

                // Filter Is Belong to the project
                if (query.IsInProject != null && query.IsInProject == false)
                {
                    data = data.Where(c => !c.Projects.Any(p => p.Id == query.ProjectId));
                }
                else
                {
                    data = data.Where(c => c.Projects.Any(p => p.Id == query.ProjectId));
                }
            }

            // Tri dynamique
            if (!string.IsNullOrEmpty(query.ColumnName) && !string.IsNullOrEmpty(query.Direction))
            {
                data = SortExtensions.DynamicSort(data, query.ColumnName, query.Direction != "Ascending");
            }

            // total record before apply pagination
            int total = await data.CountAsync();

            // Pagination
            if (query.Page > 0 && query.PageSize > 0)
            {
                data = data.Skip((query.Page - 1) * query.PageSize).Take(query.PageSize);
            }

            // Mappage de chaque Service vers ServiceDTO
            var resultDto = await data.Select(service => new ServiceDTO
            {
                Id = service.Id,
                ServiceName = service.ServiceName,
                Description = service.Description,
                Rate = service.Rate,
                CreatedBy = service.CreatedBy,
                CreatedAt = service.CreatedAt,
                isActive = service.isActive,
                Taxes = service.Taxes.Select(t => new TaxItem
                {
                    Id = t.Id,
                    CompanyTaxId = t.CompanyTaxId,
                    Name = t.CompanyTax.Name,
                    TaxeNumber = t.CompanyTax.TaxeNumber
                }).ToList()
            }).ToListAsync();

            return new PaginatedResponse<ServiceDTO>
            {
                Data = resultDto,
                Page = query.Page,
                TotalPage = (total + query.PageSize - 1) / query.PageSize,
                PageSize = query.PageSize,
                TotalRecords = total
            };
        }

        public async Task<bool> DeleteServices(List<Guid?> listServices)
        {
            try
            {

                var listItemDetail = context.Services.FirstOrDefault(x => listServices.Contains(x.Id));
                listItemDetail.isActive = false;
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public async Task<bool> UpdateArchive(List<Guid?> listServices)
        {
            try
            {

                var listItemDetail = context.Services.FirstOrDefault(x => listServices.Contains(x.Id));
                listItemDetail.isArchive = true;
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        public async Task<bool> Update(ServiceDTO model, string companyId, string userId)
        {
            using (var tracnsaction = context.Database.BeginTransaction())
            {
                try
                {
                    var service = await context.Services.FirstOrDefaultAsync(x => x.Id == model.Id);
                    if (service == null) return false;
                    // Handle add or update new Company taxes
                    var addedTaxes = await HandleAddOrUpdateTaxes(model.Taxes, Guid.Parse(companyId), userId);

                    service.UpdatedAt = DateTime.UtcNow;
                    service.UpdatedBy = userId;

                    // Gestion des taxes
                    var existingTaxes = await context.Taxs.Where(x => x.ServiceId == model.Id).ToListAsync();
                    var modelTaxIds = model.Taxes?.Select(p => p.CompanyTaxId).ToHashSet() ?? [];

                    var taxesToRemove = existingTaxes.Where(x => !modelTaxIds.Contains(x.CompanyTaxId)).ToList();
                    if (taxesToRemove.Any())
                        context.Taxs.RemoveRange(taxesToRemove);

                    // Add taxes to service
                    addedTaxes.Where(c => !existingTaxes.Select(x => x.CompanyTaxId).Contains(c.CompanyTaxId))
                              .ToList().ForEach(tax => tax.ServiceId = service.Id);
                    context.Taxs.AddRange(addedTaxes);

                    // Edit existing taxes in service
                    var updatedTaxes = existingTaxes.Where(c => modelTaxIds.Contains(c.CompanyTaxId)).ToList();
                    updatedTaxes.ForEach(tax =>
                    {
                        tax.UpdatedAt = DateTime.UtcNow;
                        tax.UpdatedBy = userId;
                    });

                    // Mise � jour des champs
                    if (!string.IsNullOrWhiteSpace(model.ServiceName))
                        service.ServiceName = model.ServiceName;

                    if (!string.IsNullOrWhiteSpace(model.Description))
                        service.Description = model.Description;

                    if (model.Rate.HasValue)
                        service.Rate = model.Rate;

                    await context.SaveChangesAsync();
                    await context.Database.CommitTransactionAsync();
                    return true;
                }
                catch
                {
                    await context.Database.RollbackTransactionAsync();
                    return false;
                }
            }
        }
        public async Task<ServiceDTO> GetServiceById(Guid serviceId)
        {
            var data = await context.Services
                .Where(x => x.isActive == true)
                .Include(c => c.Projects)
                .Include(c => c.Taxes)
                .ThenInclude(x => x.CompanyTax)
                .FirstOrDefaultAsync(x => x.isActive == true && x.Id == serviceId);

            if (data == null)
                return null;

            return MapToServiceDTO(data);
        }

        private async Task<List<Tax>> HandleAddOrUpdateTaxes(List<TaxItem>? taxes, Guid companyId, string userId)
        {
            // Handle for case user add new companyTax
            var addedCompanyTaxes = taxes.Where(c => c.CompanyTaxId == Guid.Empty)
                                      .Select(t => new CompanyTax()
                                      {
                                          Amount = t.Amount,
                                          TaxeNumber = t.TaxeNumber,
                                          CompanyId = companyId,
                                          CreatedBy = userId,
                                          Name = t.Name,
                                      }).ToList();
            context.CompanyTax.AddRange(addedCompanyTaxes);
            // Handle for update taxes
            var updatedCompanyTaxes = taxes.Where(c => c.CompanyTaxId != Guid.Empty)
                                               .Select(t => new CompanyTax()
                                               {
                                                   Id = t.CompanyTaxId,
                                                   Amount = t.Amount,
                                                   TaxeNumber = t.TaxeNumber,
                                                   CompanyId = companyId,
                                                   CreatedBy = userId,
                                                   Name = t.Name,
                                               }).ToList();
            context.CompanyTax.UpdateRange(updatedCompanyTaxes);
            await context.SaveChangesAsync();

            // Convert added CompanyTax to Tax, to add taxes into item
            addedCompanyTaxes.AddRange(updatedCompanyTaxes);
            return addedCompanyTaxes.Select(tax => new Tax
            {
                Id = Guid.NewGuid(),
                CompanyTaxId = tax.Id,
                CreatedBy = userId
            }).ToList();
        }
    }
}
