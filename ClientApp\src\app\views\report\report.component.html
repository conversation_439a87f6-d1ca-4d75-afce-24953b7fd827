<div class="w-full pb-3">
    <!-- Header page -->
    <div class="w-full py-[24px] border-b border-border-primary">
        <div
            class="container-full flex justify-between items-center flex-wrap gap-2">
            <p class="text-text-primary text-headline-lg-bold">
                {{'REPORT.Title'|translate}}
            </p>
        </div>
    </div>
</div>
<div class="p-6 bg-gray-50 min-h-screen">
    <!-- Top Nav Tiles -->
    <div class="flex flex-wrap justify-between mb-6 gap-3">
        @for(item of topNavItems; track item)
        {
        <div
            class="flex flex-col items-center rounded-md overflow-hidden w-[150px] shadow hover:shadow-md cursor-pointer">
            <div class="bg-bg-selected w-full flex justify-center py-6">
                <div
                    class="bg-bg-primary rounded-full p-3 w-16 h-16 shadow-sm">
                    <img class=" shrink-0 w-8 h-8"
                        src="../../../assets/img/svg/{{item.icon }}"
                        alt="icon menu">
                </div>
            </div>
            <div
                class="bg-white w-full p-6 h-full text-center text-m font-medium">
                {{ item.label }}
            </div>
        </div>
        }

    </div>
    @for(menu of MenuReport; track menu)
    {
    <h2 class="text-lg font-bold mb-4">{{menu.title}}</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        @for(child of menu.submenu; track child)
        {
        <app-report-card
            [card]="child"></app-report-card>
        }
    </div>

    }

</div>
