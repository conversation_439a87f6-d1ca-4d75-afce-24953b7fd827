import { DestroyRef, inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { calculateElapsedTime, formatSecondsFromTimeHours, formatTimeHoursFromSeconds } from 'app/helpers/common.helper';
import { DataService } from 'app/service/data.service';
import { ToastService } from 'app/service/toast.service';
import { TimeTrackingProvider } from 'app/views/time-tracking-v2/time-tracking.provider';
import { firstValueFrom } from 'rxjs';
import { CreateNewTimerInfo } from '../../dto/interface/createNewTimerInfo.interface';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class InnoTimerProvider {

  private dataService = inject(DataService)
  private authenticationService = inject(AuthenticationService)
  private toastService = inject(ToastService)
  private timeTrackingProvider = inject(TimeTrackingProvider)
  private destroyRef = inject(DestroyRef);
  private translate = inject(TranslateService);
  constructor() { }
  setCacheService(item) {
    localStorage.setItem("cacheService", JSON.stringify(item))
  }
  getCacheService() {
    if (localStorage.getItem("cacheService")) return JSON.parse(localStorage.getItem("cacheService"))
  }
  setCacheClientProject(item) {
    localStorage.setItem("cacheClientProject", JSON.stringify(item))
  }

  getCacheClientProject() {
    if (localStorage.getItem("cacheClientProject")) return JSON.parse(localStorage.getItem("cacheClientProject"))

  }
  clearCache() {
    localStorage.removeItem("cacheClientProject");
    localStorage.removeItem("cacheService")
  }
  getTimerInfoFromServer = async (): Promise<{ isRunningTimerOnServer: boolean, totalSecondsTimerOnServer: number }> => {
    const result = { isRunningTimerOnServer: false, totalSecondsTimerOnServer: 0 }

    const res = await firstValueFrom(this.authenticationService.CheckTimer()) as Record<string, any>;
    const isRunning = res?.['isRunning'] ?? false;
    const timer = res?.['timer'] ?? '00:00:00';
    const timerStartTime = res?.['timerStartTime']

    const totalSecondsTimerInDatabase = formatSecondsFromTimeHours(timer)
    if (isRunning) {
      // Invalid timer. Because the timer is running, but the timerStartTime is null
      if (!timerStartTime) return result

      const { hours, minutes, seconds } = calculateElapsedTime({ startTime: timerStartTime, endTime: new Date() });
      const totalSecondsTimerRunning = formatSecondsFromTimeHours(`${hours}:${minutes}:${seconds}`)

      result.isRunningTimerOnServer = true;
      result.totalSecondsTimerOnServer = totalSecondsTimerInDatabase + totalSecondsTimerRunning;
    } else {
      result.isRunningTimerOnServer = false;
      result.totalSecondsTimerOnServer = totalSecondsTimerInDatabase;
    }

    return result
  }

  updateTimeTrackingTimerInfo = (data: Partial<CreateNewTimerInfo>) => {
    const currentValue = this.dataService.GetTimeTrackingCreateTimerInfoValue()
    const newValue = { ...currentValue, ...data }
    this.dataService.SetNewTimeTrackingCreateTimerInfo(newValue)
  }

  handleUpdateActualTimer = async (newHours: string) => {
    if (newHours.split(':').length !== 3) newHours += ':00' // Ensure format: HH:MM:SS

    const { isRunningTimerOnServer } = await this.getTimerInfoFromServer()

    const payloadUpdateTimerOnServer: Record<string, any> = {
      timer: newHours,
    }
    const payloadUpdateTimerOnLocal: Partial<CreateNewTimerInfo> = {
      totalSeconds: formatSecondsFromTimeHours(newHours),
    }

    if (isRunningTimerOnServer) {
      payloadUpdateTimerOnServer['isRunning'] = true
      payloadUpdateTimerOnServer['timerStartTime'] = new Date()

      payloadUpdateTimerOnLocal.timerStatus = 'running'
    } else {
      payloadUpdateTimerOnServer['isRunning'] = false
      payloadUpdateTimerOnServer['timerStartTime'] = null

      payloadUpdateTimerOnLocal.timerStatus = 'paused'
    }

    this.authenticationService.UpdateTimer(payloadUpdateTimerOnServer)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.updateTimeTrackingTimerInfo(payloadUpdateTimerOnLocal)
      })
  }

  handlePauseOrResumeTime = async () => {
    const { timerStatus } = this.dataService.GetTimeTrackingCreateTimerInfoValue() || {}
    const timerInfo = this.dataService.GetTimeTrackingCreateTimerInfoValue()
    // Case: open 2 tabs at the same time, and start the timer in one tab, pause the timer in the other tab
    const { isRunningTimerOnServer, totalSecondsTimerOnServer } = await this.getTimerInfoFromServer()
    switch (timerStatus) {
      case 'running':
        if (isRunningTimerOnServer) {
          const payload = {
            isRunning: false,
            timerStartTime: null,
            timer: formatTimeHoursFromSeconds(totalSecondsTimerOnServer)
          }
          this.authenticationService.UpdateTimer(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe()
          this.updateTimeTrackingTimerInfo({ timerStatus: 'paused', totalSeconds: timerInfo?.totalSeconds })
        } else {
          return this.updateTimeTrackingTimerInfo({ timerStatus: 'paused', totalSeconds: timerInfo?.totalSeconds })
        }
        break;

      case 'paused':
      default:
        if (isRunningTimerOnServer) {
          localStorage.setItem("isRunning", 'true')
          this.updateTimeTrackingTimerInfo({ timerStatus: 'running', totalSeconds: timerInfo?.totalSeconds })
        } else {
          const payload = {
            isRunning: true,
            timerStartTime: new Date()
          }
          this.authenticationService.UpdateTimer(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe()
          const billableisInternal = this.dataService.getisInternalClient();
          this.updateTimeTrackingTimerInfo({ timerStatus: 'running', totalSeconds: timerInfo?.totalSeconds, billable: billableisInternal ? false : true })
          localStorage.setItem("isRunning", 'true')
        }
        if (localStorage.getItem("isRunning") === 'true') {
          this.dataService.SetNewTimeTrackingShowingTimer(true)
        }
        break
    }
  }

  handleResetTimer = () => {
    const payload = {
      isRunning: false,
      timer: "00:00:00",
      timerStartTime: null
    }
    this.authenticationService.UpdateTimer(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe()
    this.dataService.SetNewTimeTrackingShowingTimer(false)
    this.dataService.SetNewTimeTrackingCreateTimerInfo(undefined)
    this.toastService.showSuccess(this.translate.instant("TIMETRACKING.Timerdiscarded"));
    localStorage.removeItem("isRunning")
    localStorage.removeItem("ResumeData")
  }
  handleSuccessTimer = () => {
    const payload = {
      isRunning: false,
      timer: "00:00:00",
      timerStartTime: null
    }
    this.clearCache()
    this.authenticationService.UpdateTimer(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe()
    this.dataService.SetNewTimeTrackingShowingTimer(false)
    this.dataService.SetNewTimeTrackingCreateTimerInfo(undefined)

  }

  createTimeTrackingFromTimer = async () => {
    if (!localStorage.getItem("ResumeData")) {
      const { isRunningTimerOnServer, totalSecondsTimerOnServer } = await this.getTimerInfoFromServer()
      if (!isRunningTimerOnServer && !localStorage.getItem("isRunning")) {
        return this.toastService.showInfo("This timer is not running", "Please reload this window to start the timer")
      }

      const timerInfo = this.dataService.GetTimeTrackingCreateTimerInfoValue()
      const payload: any = {
        endTime: timerInfo?.totalSeconds == totalSecondsTimerOnServer ? formatTimeHoursFromSeconds(totalSecondsTimerOnServer) : formatTimeHoursFromSeconds(timerInfo?.totalSeconds || 0),
        billable: this.dataService.getisInternalClient() ? false : timerInfo?.billable ?? false,
        date: timerInfo?.date,
        description: timerInfo?.description ?? '',
        clientId: timerInfo?.workingInfo?.metadata?.objectClient?.id,
        projectId: timerInfo?.workingInfo?.metadata?.type == 'project' ? timerInfo?.workingInfo?.value : null,
        serviceId: timerInfo?.serviceInfo?.value
      }

      this.timeTrackingProvider.handleCreateTimeTracking({
        payload,
        optional: {
          callbackSuccess: () => this.handleSuccessTimer()
        }
      })
    } else {

      const { isRunningTimerOnServer, totalSecondsTimerOnServer } = await this.getTimerInfoFromServer()
      if (!isRunningTimerOnServer && !localStorage.getItem("isRunning")) {
        return this.toastService.showInfo("This timer is not running", "Please reload this window to start the timer")
      }

      const timerInfo = this.dataService.GetTimeTrackingCreateTimerInfoValue()
      const payload: any = {
        endTime: timerInfo?.totalSeconds == totalSecondsTimerOnServer ? formatTimeHoursFromSeconds(totalSecondsTimerOnServer) : formatTimeHoursFromSeconds(timerInfo?.totalSeconds || 0),
        billable: this.dataService.getisInternalClient() ? false : timerInfo?.billable ?? false,
        date: timerInfo?.date,
        description: timerInfo?.description ?? '',
        clientId: timerInfo?.workingInfo?.metadata?.objectClient?.id,
        projectId: timerInfo?.workingInfo?.metadata?.type == 'project' ? timerInfo?.workingInfo?.value : null,
        serviceId: timerInfo?.serviceInfo?.value
      }

      this.timeTrackingProvider.handleUpdateTimeTracking({
        payload,
        optional: {
          callbackSuccess: () => this.handleSuccessTimer()
        }
      })
    }
  }
}
