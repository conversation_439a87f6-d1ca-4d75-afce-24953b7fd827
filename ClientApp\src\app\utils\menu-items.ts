import { Role } from "app/enum/role.enum";

export interface MenuItem {
  url: string;
  text: string;
  id: string;
  icon: string;
  active?: boolean;
  expand?: boolean;
  children?: MenuItem[];
  permissions?: string[],
  group?: string
}
export const MENU_ITEMS: MenuItem[] = [
  {
    url: '/estimates',
    text: "MENU.Estimates",
    id: 'estimates',
    icon: 'ic_estimate.svg',
    permissions: [Role.Admin, Role.Manager],
    children: [],
    group: 'Tracking'
  },

  {
    url: '/time-tracking',
    text: "MENU.TimeTracking",
    id: 'time_tracking',
    icon: 'ic_time_tracking.svg',
    permissions: [Role.Admin, Role.Manager, Role.Employee, Role.Contractor],
    children: [],
    group: 'Tracking'
  },
  {
    url: '/dashboard',
    text: "MENU.Dashboard",
    id: 'Dashboard',
    icon: 'ic_dashboard.svg',
    permissions: [Role.All],
    children: [],
    group: 'Analyze'
  },
  {
    url: '/reports',
    text: "MENU.Reports",
    id: 'reports',
    icon: 'ic_report.svg',
    permissions: [Role.Admin, Role.Manager, Role.Accountant],
    children: [],
    group: 'Analyze'
  },
  {
    url: '/clients',
    text: "MENU.Clients",
    id: 'client',
    icon: 'ic_client.svg',
    permissions: [Role.Admin, Role.Manager],
    children: [],
    group: 'Manager'
  },
  {
    url: '/projects',
    text: "MENU.Projects",
    id: 'projects',
    icon: 'ic_project.svg',
    permissions: [Role.Admin, Role.Manager, Role.Employee, Role.Contractor],
    children: [],
    group: 'Manager'
  },
  {
    url: '/pos',
    text: "MENU.Pos",
    id: 'pos',
    icon: 'ic_post.svg',
    permissions: [Role.Admin, Role.Manager],
    children: [],
    group: 'Manager'
  },
  {
    url: '/invoices',
    text: "MENU.Invoices",
    id: 'invoices',
    permissions: [Role.Admin, Role.Manager, Role.Accountant, Role.Contractor],
    icon: 'ic_invoice.svg',
    children: [
      // {
      //   url: '/recurring-templates',
      //   text: 'Recurring Templates',
      //   id: 'recurring_templates',
      //   icon: '',
      // },
      // {
      //   url: '/retainers',
      //   text: 'Retainers',
      //   id: 'retainers',
      //   icon: '',
      // }
    ],
    group: 'Manager'
  },
  {
    url: '/payments',
    text: 'Payments',
    id: 'payments',
    icon: 'ic_payment.svg',
    permissions: [Role.Admin, Role.Manager, Role.Accountant],
    // children: [
    //   {
    //     url: '/checkout-links',
    //     text: 'Checkout Links',
    //     id: 'checkout_links',
    //     icon: '',
    //   }
    // ],
  },
  {
    url: '/expenses',
    text: "MENU.Expenses",
    id: 'expenses',
    icon: 'ic_expense.svg',
    permissions: [Role.Admin, Role.Manager, Role.Employee, Role.Accountant],
    children: [
      {
        url: '/expenses/upload',
        text: "MENU.Upload",
        id: 'upload',
        icon: '',
      }
    ],
    group: 'Manager'
  },

  {
    url: '/items-and-services',
    text: "MENU.ItemServices",
    id: 'reports',
    icon: 'ic_project.svg',
    permissions: [Role.Admin, Role.Manager],
    children: [],
    group: 'Manager'
  },
  {
    url: '/members',
    text: "MENU.TeamMembers",
    id: 'members',
    icon: 'ic_team.svg',
    permissions: [Role.Admin],
    children: [],
    group: 'Manager'
  },
  {
    url: '/settings',
    text: "MENU.Settings",
    id: 'settings',
    icon: 'ic_setting.svg',
    permissions: [Role.Admin],
    children: [
      {
        url: '/billing',
        text: "SETTINGS.Billing.Title",
        id: 'billing',
        icon: '',
      }
    ],
    group: 'Manager'
  },
];
