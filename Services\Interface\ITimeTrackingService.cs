﻿using InnoBook.DTO.CoreModel;
using InnoBook.DTO.TimeTracking;
namespace InnoBook.Services.Interface
{
    public interface ITimeTrackingService
    {
        Task<PaginatedResponse<TimeTrackingDTO>> GetAllTimTracking(GetTimeTrackingQueryParam query, string UserId, string companyId, string role);
        Task<TimeTrackingRequest> CreateTimeTracking(TimeTrackingRequest timeTracking, Guid UserId, string companyId);
        Task<PaginatedResponse<ProjectAndClientDTO>> GetProjectAndClientService(ProjectAndClientQueryParam query, string UserId, string companyId, string role);
        public Task<bool> DeleteTimeTracking(List<Guid?> listTimeTracking);
        public CalculationTotalTimeDTO CalculationTotalTimeByUser(GetTimeTrackingQueryParam filter, string companyUid);
        public Task<bool> UpdateTimeTracking(TimeTrackingRequest TimeTracking, Guid UserId);
    }
}
