import { CdnService } from './../../../service/cdn.service';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { DataService } from 'app/service/data.service';
import { AddEstimateDialog } from './../../../service/dialog/add-estimate.dialog';
import { MenuActions } from 'app/utils/action-detail-invoice';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { PhoneMaskPipe } from 'app/pipes/phoneMask.pipe';
import { ItemInvoice } from './../../../dto/interface/ItemInvoice.interface';
import { DuplicateInvoiceDialog } from './../../../service/dialog/dupicate-invoice.dialog';
import { SpinnerService } from 'app/service/spinner.service';
import { DecimalPipe } from './../../../pipes/decimal.pipe';
import { LayoutUtilsService } from '../../../core/services/layout-utils.service';
import { UserBusiness } from 'app/dto/interface/userBusiness.interface';
import { SharedModule } from 'app/module/shared.module';
import { Location } from '@angular/common';
import { InvoiceService } from '../../../service/invoice.service';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, inject, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatMenuModule } from '@angular/material/menu';
import { ToastService } from 'app/service/toast.service';
import { Invoice } from 'app/dto/interface/invoice.interface';
import { GridAllModule, Pager, PagerDropDown, PagerModule } from '@syncfusion/ej2-angular-grids';
import { TranslateService } from '@ngx-translate/core';
import { AvatarModule } from 'ngx-avatars';
import { InnoStatusComponent } from 'app/component/inno-status/inno-status.component';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { getFullAddress } from 'app/helpers/common.helper';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { ShareLinkDialog } from '../../../service/dialog/share-link.dialog';
import {
  CdkDragDrop,
  CdkDrag,
  CdkDropList,
  CdkDropListGroup,
  moveItemInArray,
  transferArrayItem,
  DragDropModule,
} from '@angular/cdk/drag-drop';
import { calculateGroupedTaxes, calculateTotalInvoiceItem, getNameTaxes } from 'app/utils/invoice.helper';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-estimate-details',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [
    SharedModule,
    MatFormFieldModule,
    AvatarModule,
    RouterModule,
    MatMenuModule,
    GridAllModule,
    PagerModule,
    DragDropModule,
    InnoStatusComponent,
    InnoPopoverComponent,
    InnoSpinomponent,
    FormatNumberPipe,
    PhoneMaskPipe,
    DecimalPipe,
    CdkDropListGroup,
    CdkDropList,
    CdkDrag
  ],
  templateUrl: './estimate-details.component.html',
  styleUrl: './estimate-details.component.scss'
})
export class EstimateDetailsComponent implements OnInit {
  imageUrl!: string;
  estimateInfo: Invoice | undefined = undefined
  InforBussiness!: UserBusiness;
  clientId!: string;
  reference!: string;
  note!: string;
  nameTax: string = ''
  sumtax: number = 0;
  subtotal: number = 0;
  qty!: number;
  total!: number;
  rate!: number;
  selectedDate!: string;
  selectedDueDate!: string;
  clientName!: string;
  _id!: string;
  taxArray: { name: string, total: number, numberTax: string, amount: number }[] = [];
  public calculateGroupedTaxes = calculateGroupedTaxes
  public getNameTaxes = getNameTaxes
  protected activatedRoute = inject(ActivatedRoute);
  private dataService = inject(DataService)
  router = inject(Router);
  destroyRef = inject(DestroyRef);
  private layoutUtilsService = inject(LayoutUtilsService)
  private translate = inject(TranslateService);
  private _toastService = inject(ToastService)
  public _storeService = inject(StoreService)
  private duplicateInvoiceDialog = inject(DuplicateInvoiceDialog)
  private authenticationService = inject(AuthenticationService)
  private _invoiceService = inject(InvoiceService)
  private cdnService = inject(CdnService)
  constructor(
    private location: Location,
    private spinnerService: SpinnerService,
    private addEstimateDialog: AddEstimateDialog,
    private shareLinkDialog: ShareLinkDialog) {
    this.InforBussiness = this._storeService.get_UserBusiness();
  }
  get filteredMenu() {
    return MenuActions.filter(action =>
      action.permissions.includes(this.authenticationService.getBusinessRole())
    );
  }
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  handleDuplicate() {
    const dialogRef = this.duplicateInvoiceDialog.open({ id: this._id, isInvoice: false });

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) return
      })
    });

  }
  handleMarkAsPaid() {
    this._invoiceService.MarkAsPaid(this._id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this._toastService.showSuccess("Mark As Paid", "Success");
        this.GetInvoiceById(this._id);
      }
    }
    )
  }


  handleBack() {
    this.location.back();
  }

  get businessInfo() {
    return {
      businessName: this.estimateInfo?.company?.businessName ?? '',
      businessPhoneNumber: this.estimateInfo?.company?.phone ?? '',
      businessAddress: getFullAddress({
        addressLine1: this.estimateInfo?.company?.adress ?? '',
        addressLine2: this.estimateInfo?.company?.adress2 ?? '',
        stateProvince: this.estimateInfo?.company?.province ?? '',
        postalCode: this.estimateInfo?.company?.postalCode ?? '',
        country: this.estimateInfo?.company?.country ?? '',
      }),
    }
  }

  ngOnInit(): void {
    this.activatedRoute.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params: any) => {
      if (params?.id) {
        this._id = params?.id
        this.GetInvoiceById(params?.id);
      }

    });

  }
  _handleData(_data: any) {

    this.selectedDueDate = this.formatDate(new Date(this.estimateInfo?.dueDate ?? 0));
    this.selectedDate = this.formatDate(new Date(this.estimateInfo?.invoiceDate ?? 0));
    this.note = this.estimateInfo?.notes
    this.rate = this.estimateInfo?.rate ?? 0
    this.qty = this.estimateInfo?.timeAmount ?? 0
    this.total = this.estimateInfo?.paidAmount ?? 0
    this.reference = this.estimateInfo?.reference
    this.imageUrl = this.estimateInfo?.company?.companyImage

  }

  GetImg(fileName: string) {

    this.cdnService.GetFile(fileName).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        const reader = new FileReader();
        reader.onload = () => {
          this.imageUrl = reader.result as string;
        };
        reader.readAsDataURL(res);
      }
    }
    )

  }
  GetInvoiceById(_id: string) {
    this._invoiceService.GetInvoiceById(_id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.estimateInfo = res;
        if (this.estimateInfo.img) {
          this.GetImg(this.estimateInfo.img)
        }
        else {
          this.GetImg(this.estimateInfo?.company?.companyImage)
        }
        this._handleData(res)
        this.calculateAllTax();
      }

    })
  }
  calculateTotal(item: any) {
    let totalTax = 0
    totalTax = item.rate * item.qty
    return this._formatTotal(this.CheckIsNaN(totalTax));

  }
  handleDownloadPDF() {
    this.spinnerService.show();
    this._invoiceService.PrintInvoiceById(this._id, this.estimateInfo.invoiceNumber);
  }

  _formatTotal(total: number) {
    return Math.floor(total * 1000) / 1000
  }

  calculateAllTax() {
    this.taxArray = [];
    this.sumtax = 0;
    const resultTax = calculateGroupedTaxes(this.estimateInfo?.itemInvoices)
    this.taxArray = Object.values(resultTax.totalTaxes);
    this.sumtax = resultTax.grandTotalTax;
    this.subtotal = this.estimateInfo.itemInvoices.reduce((subTotal, item) => subTotal + calculateTotalInvoiceItem(item.rate, item.qty), 0);
  }

  EditEstimate() {
    this.dataService.isEstimate.set(true)
    const dialogRef = this.addEstimateDialog.open(this.estimateInfo);

    dialogRef.then((c) => {
      c.afterClosed().subscribe(result => {
        if (result) {
          this.GetInvoiceById(this._id);
        }
      });
    });
  }


  handleArchive() {
    this._invoiceService.UpdateArchive(this._id, true).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.GetInvoiceById(this._id);
        this._toastService.showSuccess("Save", "Success");
      }
      else {
        this._toastService.showError("Fail", "Fail");
      }
    })
  }
  handleDelete() {
    this.layoutUtilsService.alertDelete({
      title: this.translate.instant('Delete Estimate !'),
      description: this.translate.instant('Do you want to delete?')
    }).then(isConfirm => {
      if (!isConfirm) return

      this._invoiceService.DeleteInvoice([this._id], false).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.GetInvoiceById(this._id);
          this._toastService.showSuccess("Delete", "Success");
        }
        else {
          this._toastService.showError("Fail", "Fail");
        }
      })
    })
  }

  ShareLink() {
    this.shareLinkDialog.open(this._id);
  }
  handleConvertToInvoice() {
    const _title = this.translate.instant('Convert To Invoice !');
    const _description = this.translate.instant('Do you want to convert?');

    this.layoutUtilsService.alertConfirm({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return

      this._invoiceService.ConvertToInvoice(this._id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.handleBack();
          this._toastService.showSuccess("Update", "Success");
        }
        else {
          this._toastService.showError("Fail", "Fail");
        }
      })
    })
  }

  handleFunctionInDevelopment() {
    this._toastService.showInfo("The feature is in development.")
  }
  CheckIsNaN(value: any) {
    if (isNaN(value)) {
      return 0
    }
    return value;

  }
  getFullName(item: any) {
    if (item?.firstName && item?.lastName) {
      return item?.firstName + " " + item?.lastName
    }
    else {
      return item?.email ?? ""
    }

  }
  handleMarkAsSent() {
    this._invoiceService.MarkAsSent(this._id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this._toastService.showSuccess("Mark As Sent", "Success");
        this.GetInvoiceById(this._id);
      }
    }
    )
  }
  handleAction(action: string) {
    switch (action) {
      case 'paid':
        this.handleMarkAsPaid();
        break;
      case 'sent':
        this.handleMarkAsSent();
        break;
      case 'duplicate':
        this.handleDuplicate();
        break;
      case 'download':
        this.handleDownloadPDF();
        break;
      case 'print':
        this.handleFunctionInDevelopment();
        break;
      case 'archive':
        this.handleArchive();
        break;
      case 'delete':
        this.handleDelete();
        break;
      default:
        this.handleFunctionInDevelopment();
        break;
    }
  }
  drop(event: CdkDragDrop<ItemInvoice[], any, any>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
      let payload = {
        fromInvoiceId: event.container.data[event.previousIndex].id,
        dropInvoiceId: event.container.data[event.currentIndex].id,
        fromIndex: event.container.data[event.previousIndex].position,
        dropIndex: event.container.data[event.currentIndex].position
      }
      //  this._invoiceService.ChangePositionItemInvoice(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe()
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );


    }
  }
}
