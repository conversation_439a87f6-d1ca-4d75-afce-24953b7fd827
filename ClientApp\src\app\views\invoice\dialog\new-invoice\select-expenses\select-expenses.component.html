<app-inno-modal-wrapper title="{{title}}" (onClose)="handleClose()">
    <div class="w-full p-[16px]">
        <div class="w-full flex flex-col relative">
            <div class="w-full">
                @if(isFetchingProject) {
                <div class="w-full py-2 flex justify-center items-center">
                    <app-inno-spin />
                </div>
                } @else {
                @if(listExpenses.length==0) {
                <div class="w-full">
                    <app-inno-empty-data
                        title="No result" />
                </div>
                } @else {
                <div class="overflow-auto w-full">
                    <div class="selectProjectTableLayout">
                        <div class="addBorderBottom w-full flex gap-[8px]">
                            <div class="w-[16px] shrink-0">
                                <app-inno-form-checkbox
                                    [checked]="listIndexInvoiceSelected.length === listExpenses.length"
                                    (onChange)="handleCheckedAll($event)" />
                            </div>
                            <p class="text-text-tertiary text-text-sm-semibold">
                                Expenses Name
                            </p>
                        </div>
                        <p
                            class="addBorderBottom text-text-tertiary text-text-sm-semibold">
                            Project/Client
                        </p>

                        <p
                            class="addBorderBottom text-text-tertiary text-text-sm-semibold">
                            User
                        </p>

                        <p
                            class="addBorderBottom text-text-tertiary text-text-sm-semibold text-center">
                            Line Total

                        </p>
                        <p (click)="sortDates('date')"
                            [ngClass]="{'font-bold text-black': sortColumn === 'date'}"
                            class="addBorderBottom text-text-tertiary text-text-sm-semibold text-right cursor-pointer">
                            Date
                            @if(sortColumn=='date')
                            {
                            <span class="material-icons pl-1 !text-[15px]">
                                {{ sortDirection === 'Ascending' ?
                                'arrow_upward' :
                                'arrow_downward'
                                }}
                            </span>
                            }
                        </p>
                    </div>
                    @for(expensesItem of listExpenses; track expensesItem; let
                    i =
                    $index) {
                    <div class="selectProjectTableLayout">
                        <div class="addBorderBottom w-full flex gap-[8px]">
                            <div class="w-[16px] shrink-0">
                                <app-inno-form-checkbox
                                    [checked]="isCheckedIndex(i)"
                                    (onChange)="handleToggleCheckedIndex(i)" />
                            </div>
                            <p class="text-text-primary text-text-sm-regular">
                                {{ expensesItem.expensesName ?? '' }}
                            </p>
                        </div>
                        <p
                            class="addBorderBottom text-text-primary text-text-sm-regular">
                            @if(expensesItem?.projectName) {
                            [{{ expensesItem?.projectName }}]
                            }
                            {{ expensesItem?.clientName ?? '' }}
                        </p>

                        <p
                            class="addBorderBottom text-text-primary text-text-sm-regular">
                            @if(expensesItem.inforUser?.firstName)
                            {
                            <div class="flex items-center">

                                <ngx-avatars
                                    matTooltip="{{expensesItem.inforUser?.firstName}} {{expensesItem.inforUser?.lastName}} "
                                    [size]="30"
                                    bgColor="{{_storeService.getBgColor(expensesItem.inforUser?.firstName.slice(0,1))}}"
                                    [name]="expensesItem.inforUser?.firstName.charAt(0) +' '+ (expensesItem.inforUser?.lastName ? expensesItem.inforUser?.lastName.charAt(0) : '')" />
                                <span class="pl-1">
                                    {{expensesItem.inforUser?.firstName}}
                                    {{expensesItem.inforUser?.lastName}}</span>
                            </div>

                            }
                            @else{
                            <div class="flex items-center">
                                <ngx-avatars
                                    matTooltip="{{expensesItem.inforUser?.email}}"
                                    [size]="30"
                                    bgColor="{{_storeService.getBgColor(expensesItem.inforUser?.email.slice(0,1))}}"
                                    [name]="expensesItem.inforUser?.email.slice(0,1)" />
                                <span class="pl-1">
                                    {{expensesItem.inforUser?.email}}</span>
                            </div>

                            }
                        </p>
                        <p
                            class="addBorderBottom text-text-primary text-text-sm-regular text-center">
                            ${{ expensesItem.total | decimal:2 |
                            formatNumber }}
                        </p>
                        <p
                            class="addBorderBottom text-text-primary text-text-sm-regular text-right">
                            {{ expensesItem.date| date:
                            _storeService.getdateFormat() }}
                        </p>
                    </div>
                    }
                </div>
                <ejs-pager class="customTable" [pageSize]='pageSizesDefault'
                    [totalRecordsCount]='totalPages'
                    [currentPage]="currentPage"
                    [pageSizes]="pageSizes" (click)="onPageChange($event)">
                </ejs-pager>

                <div
                    class="w-full flex pt-[15px] pb-[3px] flex-wrap justify-end items-center gap-[16px]">
                    <p class="text-text-primary text-text-sm-regular">
                        Amount Due ({{_storeService.curencyCompany | async}})
                    </p>
                    <p class="text-headline-sm-bold text-text-primary">
                        ${{ totalAmount() | decimal:2 | formatNumber }}
                    </p>
                </div>
                }
                }
            </div>
        </div>
    </div>
    <div footer>
        <app-inno-modal-footer
            [isDisableSubmit]="!listIndexInvoiceSelected.length"
            (onCancel)="handleCancel()"
            (onSubmit)="handleSubmit()" />
    </div>
</app-inno-modal-wrapper>
