<app-inno-modal-wrapper [title]="'GENERATEINVOICE.GenerateNewInvoice'"
  (onClose)="closeDialog()">
  <div class="w-full p-[16px]">
    <div class="w-full flex flex-col gap-[16px]">
      <!-- Select client -->
      <app-inno-form-select-search
        #selectSearchClientElement
        [label]="'GENERATEINVOICE.Client'|translate"
        [options]="clientOptions"
        [formControl]="f['clientId']"
        [value]="f['clientId'].value"
        [placeholder]="'GENERATEINVOICE.SelectClient'|translate"
        [errorMessages]="{ required: 'GENERATEINVOICE.ClientRequired'|translate }"
        [customOptionTemplate]="clientOptionTemplate">
        <ng-template #clientOptionTemplate let-item>
          <div (click)="handleSelectClient(item)"
            class="w-full flex p-[8px] items-center gap-[10px] hover:bg-bg-secondary rounded-md cursor-pointer"
            [class.selected]="item.value === f['clientId'].value">
            <ngx-avatars
              [size]="32"
              [name]="item.label" />
            <div class="w-full">
              <p
                class="line-clamp-1 text-text-primary text-text-sm-regular txtTitle">
                {{ item.label }}
              </p>
            </div>
          </div>
        </ng-template>
      </app-inno-form-select-search>
      <!-- Select date -->
      <div class="w-full flex flex-col relative">
        <label
          class="text-text-secondary text-text-sm-semibold mb-[2px]">{{'GENERATEINVOICE.DateRange'|translate}}
        </label>
        <app-inno-popover
          #rangeDatePopover
          position="bottom-start"
          [content]="rangeDateTemplate"
          [isClickOnContentToClose]="false"
          [isClearPadding]="true">
          <button target class="dropdown-md w-full">
            <div class="w-full text-left line-clamp-1 text-text-primary">
              {{ getSelectedRangeDateValue }}
            </div>
            <img class="shrink-0"
              src="../../../../assets/img/icon/ic_arrow_down_gray.svg"
              alt="Icon">
          </button>
          <ng-template #rangeDateTemplate>
            <div
              class="w-full shadow-md rounded-md border border-border-primary-slight bg-bg-primary">
              <div
                class="w-full p-[8px] max-h-[300px] max-w-[500px] overflow-auto">
                @for(item of rangDateOptions; track item) {
                <div
                  (click)="handleSelectDate(item)"
                  class="min-w-[300px] w-full flex p-[8px] items-center gap-[10px] rounded-md cursor-pointer hover:bg-bg-brand-primary"
                  [class.selected]="item.value === rangeDateOptionSelected.value">
                  <div class="w-full">
                    <p
                      class="line-clamp-1 text-text-primary text-text-sm-regular txtTitle">
                      {{ item.label }}
                    </p>
                  </div>
                </div>
                }
                @if(rangeDateOptionSelected.value === rangDateType.custom) {
                <div
                  class="w-full p-[8px] flex flex-col sm:flex-row items-center gap-[16px]">
                  <div class="w-full sm:w-[200px]">
                    <app-inno-form-datepicker
                      label="Start date"
                      placeholder="Select start date"
                      [value]="rangeDateOptionSelected.metadata?.startDate"
                      (onChange)="handleSelectCustomDate('startDate', $event)" />
                  </div>
                  <div class="w-full sm:w-[200px]">
                    <app-inno-form-datepicker
                      label="End date"
                      placeholder="Select end date"
                      [value]="rangeDateOptionSelected.metadata?.endDate"
                      (onChange)="handleSelectCustomDate('endDate', $event)" />
                  </div>
                </div>
                @if(isShowCustomDateError) {
                <app-inno-error-message
                  message="Invalid date: Start date must be earlier than end date." />
                }
                }
              </div>
            </div>
          </ng-template>
        </app-inno-popover>
      </div>

      <!-- Choose Expenses -->
      <div class="w-full flex flex-col relative">
        <label
          class="text-text-secondary text-text-sm-semibold mb-[2px]">{{'GENERATEINVOICE.ChooseExpenses'|translate}}
        </label>
        <mat-radio-group class="flex flex-col gap-[8px]"
          [(ngModel)]="selectedRadio"
          (change)="radioChange($event)">
          <mat-radio-button class="customRadio" [value]="1">
            {{'GENERATEINVOICE.AllUnbilledExpensesClient'|translate}}
          </mat-radio-button>
          @if(!selectedRadio)
          {
          <mat-radio-button class="customRadio" [value]="2">
            {{'GENERATEINVOICE.OnlyUnbilledExpensesProjects'|translate}}
          </mat-radio-button>
          }
          <mat-radio-button class="customRadio" [value]="0">
            {{'GENERATEINVOICE.NoExpenses'|translate}}
          </mat-radio-button>
        </mat-radio-group>
      </div>

      <!-- Choose project -->
      @if(!f['clientId'].value) {
      <div class="w-full">
        <app-inno-empty-data
          [title]="'EMPTY.EmptyProject'| translate"
          [description]="'COMMON.DescriptionEmptyProject'| translate" />
      </div>
      } @else {
      <div class="w-full flex flex-col relative">

        <div class="w-full">
          @if(isFetchingProject) {
          <div class="w-full py-2 flex justify-center items-center">
            <app-inno-spin />
          </div>
          } @else {
          @if(listInvoiceItem.length==0) {
          <div class="w-full">
            <app-inno-empty-data
              title="No result" />
          </div>
          } @else if(!selectedRadio) {
          <label
            class="text-text-secondary text-text-sm-semibold mb-[2px]">
            {{'GENERATEINVOICE.ChooseProject'|translate}}
          </label>
          <div class="overflow-auto w-full">
            <div class="selectProjectTableLayout">
              <div class="addBorderBottom w-full flex gap-[8px]">
                <div class="w-[16px] shrink-0">
                  <app-inno-form-checkbox
                    [checked]="listIndexInvoiceSelected.length === listInvoiceItem.length"
                    (onChange)="handleCheckedAll($event)" />
                </div>
                <p class="text-text-tertiary text-text-sm-semibold">
                  {{'GENERATEINVOICE.TableHeaders.Description'|translate}}
                </p>
              </div>
              <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold">
                {{'GENERATEINVOICE.TableHeaders.User'|translate}}
              </p>
              <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold">
                {{'GENERATEINVOICE.TableHeaders.Project'|translate}}
              </p>
              <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold text-right">
                {{'GENERATEINVOICE.TableHeaders.Hours'|translate}}
              </p>
              <p
                class="addBorderBottom text-text-tertiary text-text-sm-semibold text-right">
                {{'GENERATEINVOICE.TableHeaders.LineTotal'|translate}}
              </p>
            </div>
            @for(invoiceItem of listInvoiceItem; track invoiceItem; let i =
            $index) {
            <div class="selectProjectTableLayout">
              <div class="addBorderBottom w-full flex gap-[8px]">
                <div class="w-[16px] shrink-0">
                  <app-inno-form-checkbox
                    [checked]="isCheckedIndex(i)"
                    (onChange)="handleToggleCheckedIndex(i)" />
                </div>
                <p class="text-text-primary text-text-sm-regular">
                  {{ invoiceItem.description ?? '' }}
                </p>
              </div>
              <div class="w-full">
                <p class="text-text-primary text-text-sm-regular">
                  @if(invoiceItem?.inforUser?.firstName)
                  {
                  <div class="flex items-center">
                    <ngx-avatars
                      [size]="30"
                      bgColor="{{_storeService.getBgColor(invoiceItem?.inforUser?.firstName.slice(0,1))}}"
                      [name]="invoiceItem?.inforUser.firstName.charAt(0) +' '+ (invoiceItem?.inforUser?.lastName ? invoiceItem?.inforUser?.lastName.charAt(0) : '')" />
                    <span class="pl-1 line-clamp-1">
                      {{invoiceItem?.inforUser?.firstName}}
                      {{invoiceItem?.inforUser?.lastName}}</span>
                  </div>
                  }
                  @else{
                  <div class="flex items-center">
                    <ngx-avatars
                      [size]="30"
                      bgColor="{{_storeService.getBgColor(invoiceItem?.inforUser?.email.slice(0,1))}}"
                      [name]="invoiceItem?.inforUser?.email.slice(0,1)" />
                    <span class="pl-1 line-clamp-1">
                      {{invoiceItem?.inforUser?.email}}</span>
                  </div>

                  }
                </p>
              </div>
              <p class="addBorderBottom text-text-primary text-text-sm-regular">
                {{ invoiceItem.metadata?.timeTracking?.project?.projectName ??
                '-' }}
              </p>
              <p
                class="addBorderBottom text-text-primary text-text-sm-regular text-right">
                {{ invoiceItem.metadata?.hours ?? '' }}
              </p>
              <p class="addBorderBottom text-text-primary text-text-sm-regular text-right">
                ${{
                      calculateTotalInvoiceItem(invoiceItem?.rate, invoiceItem?.qty) | decimal:2 | formatNumber
                }}
              </p>
            </div>
            }
            <ejs-pager class="customTable" [pageSize]='pageSizesDefault'
              [totalRecordsCount]='totalPages'
              [currentPage]="currentPage"
              [pageSizes]="pageSizes" (click)="onPageChange($event)">
            </ejs-pager>
          </div>
          <div
            class="w-full flex pt-[15px] pb-[3px] flex-wrap justify-end items-center gap-[16px]">
            <p class="text-text-primary text-text-sm-regular">
              {{'GENERATEINVOICE.AmountDue'|translate}}
              ({{_storeService.curencyCompany | async}})
            </p>
            <p class="text-headline-sm-bold text-text-primary">
              ${{ totalAmount() | decimal:2 | formatNumber }}
            </p>
          </div>
          }
          <!-- handle expenses -->
          @else{
          <app-genrate-invoice-expenses (cancel)="closeDialog()"
            [valueRadio]="selectedRadio"
            [clientId]="f['clientId'].value"
            [listProjectId]="listProjectId" />
          }
          }
        </div>
      </div>
      }
    </div>
  </div>
  @if(!selectedRadio)
  {
  <div footer>
    <app-inno-modal-footer
      (onCancel)="handleCancel()"
      (onSubmit)="handleSubmit()" />
  </div>
  }

</app-inno-modal-wrapper>
