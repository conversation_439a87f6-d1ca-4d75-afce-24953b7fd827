﻿using InnoLogiciel.Server.Entities;
using System.ComponentModel.DataAnnotations.Schema;

namespace InnoBook.Entities
{
    public class Company : BaseGuidEntity
    {
        public string? BusinessName { get; set; }
        public string? CompanyImage  { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Country { get; set; }
        public string? Adress { get; set; }
        public string? Adress2 { get; set; }
        public string? City { get; set; }
        public string? Province { get; set; }
        public string? PostalCode { get; set; }
        public string? TimeZone { get; set; }
        public string? StartWeekOn { get; set; }
        public string? DateFormat { get; set; }
        public string? Note { get; set; }
        public decimal? Rate { get; set; }
        public string? FiscalMonth { get; set; }
        public string? Currency { get; set; }
        public int? FiscalDay { get; set; }
        public bool IsPremiumCompany { get; set; } = false;

    

        // Navigation properties (optional)
        public List<Client>? Clients { get; set; }
        public List<UserBusiness>? UserBusinesses { get; set; }

        public List<Project>? Projects { get; set; } 

        public List<Member>? Members { get; set; }

        public List<Invoice>? Invoices { get; set; }
        public List<Payment>? Payments { get; set; }

        public List<Expenses>? Expenses { get; set; }
        public List<Merchant>? Merchants { get; set; }
        public List<Category>? Categorys { get; set; }
        public List<CategoryItem>? CategoryItems { get; set; }
        public List<CompanyTax>? CompanyTaxs { get; set; }

        // Stripe
        public string? StripeCustomerId { get; set; }
    }
}
