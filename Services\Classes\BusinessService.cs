﻿using InnoBook.DTO.Business;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Members;
using InnoBook.DTO.TimeTracking;
using InnoBook.DTO.UserBusiness;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Extension;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using InnoLogiciel.Server.Contexts;
using InnoLogiciel.Server.Enum.User;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System;


namespace InnoBook.Services.Classes
{
    public class BusinessService(InnoLogicielContext _context) : IBusinessService
    {


        public async Task<PaginatedResponse<GetBusiness>> GetAllUserBusiness(GetUserBusinessQueryParam filter, string userId, string companyId, string role)
        {
            var query = _context.UserBusinesses.Include(c => c.User)
                               .Where(c => c.CompanyId.ToString() == companyId);

            // Filter by search
            if (!string.IsNullOrEmpty(filter.Search))
            {
                query = query.Where(c => c.User != null && c.User.FirstName != null
                                && c.User.FirstName.ToLower().Contains(filter.Search.ToLower()));
            }

            // Restrict user get user business
            if(!UWIPConstants.HIGH_LEVEL_ROLES.Contains(role))
            {
                query = query.Where(c => c.UserId == Guid.Parse(userId));
            }

            // Select data
            var data = query.Select(c => new
            {
                Id = c.Id,
                UserId = c.UserId,
                FirstName = c.User.FirstName,
                LastName = c.User.LastName,
                Email = c.User.Email,
                Role = c.Role,
                Status = c.Status,
            });


            // Custom Sort
            if (!string.IsNullOrEmpty(filter.ColumnName) && !string.IsNullOrEmpty(filter.Direction))
            {
                data = SortExtensions.DynamicSort(data, filter.ColumnName, filter.Direction == "Ascending" ? false : true);

            }

            // Pagination
            if (filter.Page > 0 && filter.PageSize > 0)
            {
                data = data.Skip((filter.Page - 1) * filter.PageSize)
                             .Take(filter.PageSize);
            }

            var totalRecords = await data.CountAsync();
            var result = await data.AsNoTracking().ToListAsync();
            return new PaginatedResponse<GetBusiness>
            {
                Data = result.Select(c => new GetBusiness
                {
                    Id = c.Id,
                    User = new DTOUserMember
                    {
                        Id = c.UserId,
                        FirstName = c.FirstName,
                        LastName = c.LastName,
                        Email = c.Email
                    },
                    Role = c.Role,
                    Status = c.Status,
                }).ToList(),
                Page = filter.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)filter.PageSize),
                PageSize = filter.PageSize,
                TotalRecords = totalRecords
            };
        }
        public async Task<List<GetBusinessDTO>> GetUserBusiness(Guid userId)
        {
            return await _context.UserBusinesses.Include(c => c.Company).Where(c => c.UserId == userId)
                                                .Select(c => new GetBusinessDTO
                                                {
                                                    BusinessId = c.Id,
                                                    BusinessName = c.Company.BusinessName,
                                                    Role = c.Role ?? UserBusinessRole.Employee,
                                                    Status = c.Status,
                                                    isOwner=c.CreatedBy==userId.ToString()?true:false
                                                }).ToListAsync();

        }
        public async Task<GetUserBusiness> GetDetailUserInBusiness(Guid userId, Guid CompanyId)
        {
            return await _context.UserBusinesses.Include(c => c.Company)
                                                .Select(c => new GetUserBusiness
                                                {
                                                    CompanyId = (Guid)c.CompanyId,
                                                    BusinessId = c.Id,
                                                    User = c.User != null ? new DTOUserMember
                                                    {
                                                        Id = c.User.Id,
                                                        FirstName = c.User.FirstName,
                                                        LastName = c.User.LastName,
                                                        Email = c.User.Email
                                                    } : null,
                                                    BusinessName = c.Company.BusinessName,
                                                    Role = c.Role ?? UserBusinessRole.Employee,
                                                    CreatedBy = c.Company.CreatedBy,
                                                    UserId = userId,
                                                    Status = c.Status,
                                                }).FirstOrDefaultAsync(c => c.User.Id == userId && c.CompanyId == CompanyId) ?? new DTO.Business.GetUserBusiness();
        }

        public async Task CreateUserBusiness(Guid userId, CreateUserBusiness data)
        {
            using (var transaction = _context.Database.BeginTransaction())
            {
                try
                {
                    var company = new Company()
                    {
                        BusinessName = data.Name,
                        Note = data.Note,
                        Country = data.Country,
                        Email = data.Email,
                        CreatedBy = userId.ToString(),
                    };
                    await _context.Companies.AddAsync(company);
                    await _context.SaveChangesAsync();
                    // handle  internal client
                    var totalClient = _context.Clients.Count(c => c.CreatedBy == userId.ToString());
                    if (totalClient == 0)
                    {
                        var client = new Client()
                        {
                            ClientName = data.Name,
                            IsInternal = true,
                            CompanyId = company.Id,
                            EmailAddress = data.Email,
                            CreatedBy = userId.ToString(),
                        };

                        await _context.Clients.AddAsync(client);
                    }

                    var userBusiness = new UserBusiness()
                    {
                        Company = company,
                        UserId = userId,
                        Role = UWIPConstants.Admin,
                        Status = Enum.UserBusinessStatus.Active,
                        UpdatedBy = userId.ToString(),
                        CreatedBy = userId.ToString(),
                    };
                    await _context.UserBusinesses.AddAsync(userBusiness);
                    await _context.SaveChangesAsync();

                    await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    throw;
                }
            }
        }

        public async Task<GetBusinessByIdDTO> GetBusinessById(Guid BusinessId, Guid UserId)
        {
            var data = await _context.UserBusinesses.Include(c => c.Company)
                                                 .Where(c => c.Id == BusinessId && c.UserId.Equals(UserId))
                                               .Select(c => new GetBusinessByIdDTO
                                               {
                                                    BusinessId=c.Id,
                                                   BusinessName = c.Company.BusinessName,
                                                   DateFormat = c.Company.DateFormat,
                                                   Role = (c.Role ?? UserBusinessRole.Employee),
                                                   isOwner = c.CreatedBy == UserId.ToString() ? true : false
                                               }).FirstOrDefaultAsync();
            return data;
        }


        public async Task<ResultAddMember?> AddMemberBusiness(MemberBusiness member, Guid authId)
        {
            using(var transaction = await _context.Database.BeginTransactionAsync())
            {
                try
                {
                    UserBusinessStatus status = UserBusinessStatus.Active;
                    var user = await _context.Users.FirstOrDefaultAsync(x => x.Email == member.Email);
                    if (user == null)
                    {
                        user = new User
                        {
                            CreatedAt = DateTime.UtcNow,
                            Email = member.Email,
                            Status = UserStatus.Invite
                        };
                        status = UserBusinessStatus.Invite;
                        await _context.Users.AddAsync(user);
                        await _context.SaveChangesAsync();
                    }

                    var userBusiness = await _context.UserBusinesses.FirstOrDefaultAsync(x => x.UserId == user.Id && x.CompanyId == member.CompanyId);
                    if (userBusiness == null)
                    {
                        userBusiness = new UserBusiness
                        {
                            CompanyId = member.CompanyId,
                            UserId = user.Id,
                            Role = UserBusinessRole.Employee,
                            Status = status,
                            UpdatedBy = authId.ToString(),
                            CreatedBy = authId.ToString()
                        };
                        await _context.UserBusinesses.AddAsync(userBusiness);

                        user.FirstName = member.Firstname;
                        user.LastName = member.Lastname;
                        user.UpdatedBy = authId.ToString();
                        _context.Users.Update(user);

                        await _context.SaveChangesAsync();
                    }

                    await transaction.CommitAsync();

                    return new ResultAddMember
                    {
                        Email = user.Email,
                        Status = user.Status,
                        IdBusiness = userBusiness.Id.ToString()
                    };
                }
                catch (Exception ex)
                {
                    await transaction.RollbackAsync();
                    throw;
                }
            }
        }


        public async Task<UserBusinessDTO> UpdateRoleMember(Guid userId, string role, Guid companyId)
        {
            var data = await _context.UserBusinesses
                .Include(ub => ub.User)
                .Include(ub => ub.Company)
                .FirstOrDefaultAsync(c => c.UserId == userId && c.CompanyId == companyId);

            if (data == null)
                throw new ArgumentNullException(nameof(data), "UserBusiness not found.");

            data.Role = role;
            _context.UserBusinesses.Update(data);
            await _context.SaveChangesAsync();

            // Mapper vers DTO (manuellement ici)
            return new UserBusinessDTO
            {
                Id = data.Id,
                UserId = data.UserId,
                CompanyId = data.CompanyId,
                Role = data.Role,
                Status = (int)data.Status
            };
        }


        public async Task<UserBusinessStatus> GetUserStatus(string businessId)
        {
            var data = await _context.UserBusinesses.FirstOrDefaultAsync(c => c.Id == Guid.Parse(businessId));
            if (data == null)
                throw new ArgumentNullException("NoData");

            return data.Status;
        }

        public async Task<bool> DeleteMemberBusiness(Guid memberId, Guid CompanyId)
        {
            try
            {
                var listItemDetail = _context.UserBusinesses.Where(x => x.UserId == memberId && x.CompanyId == CompanyId).ToList();
                _context.UserBusinesses.RemoveRange(listItemDetail);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task CreateUserBusinessCompany(Guid userId, CreateUserBusinessDTO data)
        {
            var entity = new UserBusiness
            {
                Id = Guid.NewGuid(),
                UserId = userId,
                CompanyId = data.CompanyId,
                Status = data.Status,
                Role = data.Role
            };

            await _context.UserBusinesses.AddAsync(entity);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateMemberByEmailAsync(string email, string role)
        {
            var data = await _context.Users.FirstOrDefaultAsync(c => c.Email == email);
            var data_meber = await _context.UserBusinesses.FirstOrDefaultAsync(c => c.UserId == data.Id);
            data_meber.Role = role;
            _context.UserBusinesses.Update(data_meber);
            await _context.SaveChangesAsync();
        }

        public string GetCompanyId(string businessId)
        {
            var business = _context.UserBusinesses.FirstOrDefault(c => c.Id == Guid.Parse(businessId));
            return business.CompanyId.ToString();
        }

        public async Task<bool> UpdateStatus(string businessId, UserBusinessStatus status)
        {
            try
            {
                var business = _context.UserBusinesses.FirstOrDefault(c => c.Id == Guid.Parse(businessId));
                business.Status = status;
                _context.Update(business);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
