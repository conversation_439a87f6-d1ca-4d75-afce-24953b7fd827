﻿using InnoBook.DTO.CompanyTax;
using InnoBook.DTO.CoreModel;
using InnoBook.Entities;
using InnoBook.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;

namespace InnoBook.Services.Classes
{
    public class CompanyTaxService(InnoLogicielContext context) : ICompanyTaxService
    {
        public async Task<bool> Create(List<CompanyTaxDTO> tistTax, Guid CompanyId)
        {
            try
            {
                var listCompanyTax = await context.CompanyTax
                    .Where(x => x.CompanyId == CompanyId)
                    .ToListAsync();

                var listRemoveTax = listCompanyTax
                    .Where(x => !tistTax.Select(p => p.Id).Contains(x.Id))
                    .ToList();

                if (listRemoveTax.Any())
                {
                    context.CompanyTax.RemoveRange(listRemoveTax);
                }

                foreach (var itemTax in tistTax)
                {
                    var existing = await context.CompanyTax.FirstOrDefaultAsync(e => e.Id == itemTax.Id);
                    if (existing != null)
                    {
                        existing.Amount = itemTax.Amount;
                        existing.Name = itemTax.Name;
                        existing.TaxeNumber = itemTax.TaxeNumber;
                        existing.UpdatedBy = itemTax.CreatedBy;
                    }
                    else
                    {
                        context.CompanyTax.Add(new CompanyTax
                        {
                            Id = itemTax.Id != Guid.Empty ? itemTax.Id : Guid.NewGuid(),
                            Amount = itemTax.Amount,
                            Name = itemTax.Name,
                            TaxeNumber = itemTax.TaxeNumber,
                            CreatedBy = itemTax.CreatedBy,
                            CompanyId = itemTax.CompanyId,
                        });
                    }
                }

                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        public async Task<PaginatedResponse<CompanyTaxDTO>> GetAllCompanyTax(PaginatedRequest query, string CompanyId)
        {
            var baseQuery = context.CompanyTax
                .Where(c => c.CompanyId.ToString() == CompanyId);

            if (!string.IsNullOrEmpty(query.Search))
            {
                baseQuery = baseQuery.Where(c => c.Name.ToLower().Contains(query.Search.ToLower()));
            }

            var totalRecords = await baseQuery.CountAsync();

            var data = await baseQuery
                .OrderBy(c => c.Name)
                .Skip((query.Page - 1) * query.PageSize)
                .Take(query.PageSize)
                .ToListAsync();

            // Mapping entities to DTOs
            var result = data.Select(c => new CompanyTaxDTO
            {
                Id = c.Id,
                Amount = c.Amount,
                Name = c.Name,
                TaxeNumber = c.TaxeNumber,
                CreatedBy = c.CreatedBy,
            }).ToList();

            return new PaginatedResponse<CompanyTaxDTO>
            {
                Data = result,
                Page = query.Page,
                TotalPage = (totalRecords + query.PageSize - 1) / query.PageSize,
                PageSize = query.PageSize,
                TotalRecords = totalRecords
            };
        }


        public Task<bool> Update(CompanyTaxDTO model, Guid CompanyId, string UserId)
        {
            throw new NotImplementedException();
        }
    }
}
