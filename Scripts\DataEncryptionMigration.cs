using InnoBook.Entities;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace InnoBook.Scripts
{
    /// <summary>
    /// <PERSON>ript to migrate existing unencrypted data to encrypted format
    /// This should be run once after implementing encryption
    /// </summary>
    public class DataEncryptionMigration
    {
        private readonly InnoLogicielContext _context;
        private readonly IEncryptionService _encryptionService;
        private readonly ILogger<DataEncryptionMigration> _logger;

        public DataEncryptionMigration(
            InnoLogicielContext context, 
            IEncryptionService encryptionService,
            ILogger<DataEncryptionMigration> logger)
        {
            _context = context;
            _encryptionService = encryptionService;
            _logger = logger;
        }

        /// <summary>
        /// Main method to run the migration
        /// </summary>
        public async Task RunMigrationAsync()
        {
            _logger.LogInformation("Starting data encryption migration...");

            try
            {
                // Migrate Users
                await MigrateUsersAsync();

                // Migrate Clients
                await MigrateClientsAsync();

                // Migrate Companies
                await MigrateCompaniesAsync();

                _logger.LogInformation("Data encryption migration completed successfully!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during data encryption migration");
                throw;
            }
        }

        /// <summary>
        /// Migrate user data to encrypted format
        /// </summary>
        private async Task MigrateUsersAsync()
        {
            _logger.LogInformation("Migrating Users data...");

            var users = await _context.Users
                .Where(u => !string.IsNullOrEmpty(u.FirstName) || !string.IsNullOrEmpty(u.LastName))
                .ToListAsync();

            _logger.LogInformation($"Found {users.Count} users to migrate");

            var batchSize = 50;
            var totalBatches = (int)Math.Ceiling((double)users.Count / batchSize);

            for (int batch = 0; batch < totalBatches; batch++)
            {
                var batchUsers = users.Skip(batch * batchSize).Take(batchSize).ToList();
                
                _logger.LogInformation($"Processing batch {batch + 1}/{totalBatches} ({batchUsers.Count} users)");

                foreach (var user in batchUsers)
                {
                    try
                    {
                        // Check if data is already encrypted (basic check)
                        if (IsLikelyEncrypted(user.FirstName) && IsLikelyEncrypted(user.LastName))
                        {
                            _logger.LogDebug($"User {user.Id} appears to already be encrypted, skipping");
                            continue;
                        }

                        // Store original values for logging
                        var originalFirstName = user.FirstName;
                        var originalLastName = user.LastName;

                        // Encrypt the user data
                        user.FirstName = await _encryptionService.EncryptAsync(user.FirstName);
                        user.LastName = await _encryptionService.EncryptAsync(user.LastName);
                        user.UpdatedAt = DateTime.UtcNow;

                        _logger.LogDebug($"Encrypted user {user.Id}: '{originalFirstName}' -> encrypted, '{originalLastName}' -> encrypted");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to encrypt user {user.Id}");
                        throw;
                    }
                }

                // Save batch
                await _context.SaveChangesAsync();
                _logger.LogInformation($"Saved batch {batch + 1}/{totalBatches}");
            }

            _logger.LogInformation($"Successfully migrated {users.Count} users");
        }

        /// <summary>
        /// Migrate client data to encrypted format
        /// </summary>
        private async Task MigrateClientsAsync()
        {
            _logger.LogInformation("Migrating Clients data...");

            var clients = await _context.Clients
                .Where(c => c.isActive && 
                           (!string.IsNullOrEmpty(c.FirstName) || 
                            !string.IsNullOrEmpty(c.LastName) || 
                            !string.IsNullOrEmpty(c.PhoneNumber) ||
                            !string.IsNullOrEmpty(c.AddressLine1) ||
                            !string.IsNullOrEmpty(c.AddressLine2)))
                .ToListAsync();

            _logger.LogInformation($"Found {clients.Count} clients to migrate");

            var batchSize = 50;
            var totalBatches = (int)Math.Ceiling((double)clients.Count / batchSize);

            for (int batch = 0; batch < totalBatches; batch++)
            {
                var batchClients = clients.Skip(batch * batchSize).Take(batchSize).ToList();
                
                _logger.LogInformation($"Processing batch {batch + 1}/{totalBatches} ({batchClients.Count} clients)");

                foreach (var client in batchClients)
                {
                    try
                    {
                        // Check if data is already encrypted
                        if (IsLikelyEncrypted(client.FirstName) && IsLikelyEncrypted(client.LastName))
                        {
                            _logger.LogDebug($"Client {client.Id} appears to already be encrypted, skipping");
                            continue;
                        }

                        // Encrypt the client data
                        client.FirstName = await _encryptionService.EncryptAsync(client.FirstName);
                        client.LastName = await _encryptionService.EncryptAsync(client.LastName);
                        client.PhoneNumber = await _encryptionService.EncryptAsync(client.PhoneNumber);
                        client.BusinessPhoneNumber = await _encryptionService.EncryptAsync(client.BusinessPhoneNumber);
                        client.MobilePhoneNumber = await _encryptionService.EncryptAsync(client.MobilePhoneNumber);
                        client.AddressLine1 = await _encryptionService.EncryptAsync(client.AddressLine1);
                        client.AddressLine2 = await _encryptionService.EncryptAsync(client.AddressLine2);
                        client.UpdatedAt = DateTime.UtcNow;

                        _logger.LogDebug($"Encrypted client {client.Id}: {client.ClientName}");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"Failed to encrypt client {client.Id}");
                        throw;
                    }
                }

                // Save batch
                await _context.SaveChangesAsync();
                _logger.LogInformation($"Saved batch {batch + 1}/{totalBatches}");
            }

            _logger.LogInformation($"Successfully migrated {clients.Count} clients");
        }

        /// <summary>
        /// Migrate company data to encrypted format
        /// </summary>
        private async Task MigrateCompaniesAsync()
        {
            _logger.LogInformation("Migrating Companies data...");

            var companies = await _context.Companies
                .Where(c => !string.IsNullOrEmpty(c.Phone) || 
                           !string.IsNullOrEmpty(c.Adress) || 
                           !string.IsNullOrEmpty(c.Adress2))
                .ToListAsync();

            _logger.LogInformation($"Found {companies.Count} companies to migrate");

            foreach (var company in companies)
            {
                try
                {
                    // Check if data is already encrypted
                    if (IsLikelyEncrypted(company.Phone) && IsLikelyEncrypted(company.Adress))
                    {
                        _logger.LogDebug($"Company {company.Id} appears to already be encrypted, skipping");
                        continue;
                    }

                    // Encrypt the company data
                    company.Phone = await _encryptionService.EncryptAsync(company.Phone);
                    company.Adress = await _encryptionService.EncryptAsync(company.Adress);
                    company.Adress2 = await _encryptionService.EncryptAsync(company.Adress2);
                    company.UpdatedAt = DateTime.UtcNow;

                    _logger.LogDebug($"Encrypted company {company.Id}: {company.BusinessName}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Failed to encrypt company {company.Id}");
                    throw;
                }
            }

            // Save all companies
            await _context.SaveChangesAsync();
            _logger.LogInformation($"Successfully migrated {companies.Count} companies");
        }

        /// <summary>
        /// Basic check to see if data might already be encrypted (Base64 format)
        /// </summary>
        private bool IsLikelyEncrypted(string? data)
        {
            if (string.IsNullOrEmpty(data))
                return false;

            // Check if it looks like Base64 (basic check)
            try
            {
                // Base64 strings are typically longer and contain specific characters
                if (data.Length > 20 && data.Length % 4 == 0)
                {
                    Convert.FromBase64String(data);
                    return true;
                }
            }
            catch
            {
                // Not Base64
            }

            return false;
        }

        /// <summary>
        /// Verify migration by checking if data can be decrypted
        /// </summary>
        public async Task VerifyMigrationAsync()
        {
            _logger.LogInformation("Verifying migration...");

            // Test a few users
            var testUsers = await _context.Users.Take(5).ToListAsync();
            foreach (var user in testUsers)
            {
                try
                {
                    var decryptedFirstName = await _encryptionService.DecryptAsync(user.FirstName);
                    var decryptedLastName = await _encryptionService.DecryptAsync(user.LastName);
                    _logger.LogInformation($"User {user.Id} decryption test: '{decryptedFirstName}' '{decryptedLastName}'");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Failed to decrypt user {user.Id}");
                }
            }

            // Test a few clients
            var testClients = await _context.Clients.Where(c => c.isActive).Take(5).ToListAsync();
            foreach (var client in testClients)
            {
                try
                {
                    var decryptedFirstName = await _encryptionService.DecryptAsync(client.FirstName);
                    var decryptedLastName = await _encryptionService.DecryptAsync(client.LastName);
                    _logger.LogInformation($"Client {client.Id} decryption test: '{decryptedFirstName}' '{decryptedLastName}'");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"Failed to decrypt client {client.Id}");
                }
            }

            _logger.LogInformation("Migration verification completed");
        }
    }
}
