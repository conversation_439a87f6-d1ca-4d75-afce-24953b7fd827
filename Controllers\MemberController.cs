﻿using InnoBook.Attributes;
using InnoBook.DTO.Business;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Mail;
using InnoBook.Enum;
using InnoBook.Services;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;


namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class MemberController(IBusinessService _businessService, IMemberService memberService, IMailService _mail, IConfiguration config,
        IHttpContextAccessor httpContextAccessor) : BaseController(httpContextAccessor)
    {

       
        [RequiredRoles(UserBusinessRole.All)]
        [HttpGet("CountMemberBusiness")]
        public async Task<ActionResult> CountMemberBusiness()
        {
            var total = await memberService.CountMemberBusiness(IdCompany);
            return Ok(total);
        }
        

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager)]
        [HttpPost("InviteMember")]
        public async Task<IActionResult> InviteMember(MemberBusiness member)
        {
            member.CompanyId = Guid.Parse(IdCompany);
            try
            {
                var url = config.GetSection("InviteUrlMember").Value + '/' + member.Email;

                string templateDirectory = "EmailTemplates";
                var templateService = new TemplateService(templateDirectory);

                // Load and process template
                string templateContent = templateService.LoadTemplate("InviteMember.html");
                var placeholders = new Dictionary<string, string>
                    {
                        { "Email",  member.Email },
                        { "url",url }
                    };
                string emailBody = templateService.ProcessTemplate(templateContent, placeholders);
                var mail = new MailInfoAndContent
                {
                    To = member.Email,
                    Subject = "Invite Member",
                    Body = emailBody
                };
                _mail.SendMail(mail);
                if (member.Role != UWIPConstants.Acountant)
                {
                    await _businessService.UpdateMemberByEmailAsync(member.Email, member.Role);
                }
                return Ok(member);
            }

            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


    }
}
