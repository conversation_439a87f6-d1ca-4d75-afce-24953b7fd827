import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'app-inno-empty-data',
  templateUrl: './inno-empty-data.component.html',
  styleUrls: ['./inno-empty-data.component.scss'],
  standalone: true,
  imports: [CommonModule, TranslateModule]
})
export class InnoEmptyDataComponent {

  @Input() public title?: string = ''
  @Input() public description?: string = ''
  @Input() public icon?: string = ''

  public defaultIcon = '../../../assets/img/empty_invoice.png'

  constructor() { }
}
