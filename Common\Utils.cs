﻿using InnoBook.Common;
using InnoLogiciel.Server.Model;
using Microsoft.Net.Http.Headers;
using System.IdentityModel.Tokens.Jwt;

namespace InnoLogiciel.Common
{
    public static class Utils
    {
        public static UserJwt? GetUserByHeader(IHeaderDictionary pHeader)
        {
            try
            {
                if (pHeader == null || !pHeader.TryGetValue(HeaderNames.Authorization, out var headerValue) || string.IsNullOrEmpty(headerValue))
                {
                    return null;
                }

                var bearerToken = headerValue.ToString().Replace("Bearer ", "");
                var handler = new JwtSecurityTokenHandler();
                var tokenS = handler.ReadJwtToken(bearerToken);

                var _id = tokenS.Claims.FirstOrDefault(x => x.Type == "Id")?.Value;
                if (string.IsNullOrEmpty(_id))
                {
                    return null;
                }
                var data = new UserJwt
                {
                    Id = _id
                };
                return data;
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        public static string? GetTokenFromRequest(HttpRequest request)
        {
            try
            {
                try
                {
                    Microsoft.Extensions.Primitives.StringValues headerValues;
                    request.Headers.TryGetValue("Authorization", out headerValues);
                    return headerValues.FirstOrDefault().Replace("Bearer ", string.Empty); ;
                }
                catch (Exception ex)
                {
                    return string.Empty;
                }
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        public static string GetBusinessRole(this HttpContext context)
        {
            try
            {
                return context.User.Claims.FirstOrDefault(c => c.Type == "businessRole")?.Value ?? string.Empty;
            }
            catch (Exception ex)
            {
                return string.Empty;
            }
        }


        public static string GetFullAddress(
            string address1,
            string address2,
            string city,
            string province,
            string postalCode,
            string country
        ) {
            var addressParts = new List<string>
            {
                address1,
                address2,
                city,
                province,
                postalCode,
                country
            }.Where(part => !string.IsNullOrWhiteSpace(part));

            return string.Join(", ", addressParts);
        }

        public static UtcTimeConvert GetStartAndEndOfUtcDate(DateTime localDate, string timeZone)
        {
            var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(timeZone);
            var localStart = new DateTime(localDate.Year, localDate.Month, localDate.Day, 0, 0, 0);
            var localEnd = localStart.AddDays(1);

            var localStartOffset = new DateTimeOffset(localStart, timeZoneInfo.GetUtcOffset(localStart));
            var localEndOffset = new DateTimeOffset(localEnd, timeZoneInfo.GetUtcOffset(localEnd));
            var utcStart = localStartOffset.UtcDateTime;
            var utcEnd = localEndOffset.UtcDateTime;

            return new UtcTimeConvert()
            {
                StartUtcDateTime = utcStart,
                EndUtcDateTime = utcEnd
            };
        }
    }
}
