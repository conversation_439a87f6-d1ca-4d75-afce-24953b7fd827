﻿using Amazon.S3.Model;
using InnoBook.Common;
using InnoBook.DTO;
using InnoBook.DTO.Chart;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Estimate;
using InnoBook.DTO.Invoice;
using InnoBook.DTO.Members;
using InnoBook.DTO.TimeTracking;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Extension;
using InnoBook.Request.Invoice;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.ComponentModel.Design;

namespace InnoBook.Services.Classes
{
    public class InvoiceService(InnoLogicielContext context, IConfiguration config) : IInvoiceService
    {
        public async Task<RequestInvoice?> Update(RequestInvoice model, Guid userId, Guid companyId)
        {
            var dataInvoice = await context.Invoices
                .Include(o => o.ItemInvoices)
                    .ThenInclude(x => x.Taxes)
                .FirstOrDefaultAsync(p => p.Id == model.Id);

            if (dataInvoice == null) return null;

            var existingItems = await context.ItemInvoice
                .Include(x => x.Taxes)
                .Where(x => x.InvoiceId == model.Id)
                .ToListAsync();

            if (model.Payments.Any())
            {
                model.Payments.ForEach(x => x.InvoiceId = model.Id);
            }

            if (model.ItemInvoices.Any())
            {
                var newItemIds = model.ItemInvoices.Select(p => p.Id).ToHashSet();

                // Supprimer les anciens items non présents dans la nouvelle liste
                var itemsToRemove = existingItems.Where(x => !newItemIds.Contains(x.Id)).ToList();
                foreach (var oldItem in itemsToRemove)
                {
                    var tracking = await context.TimeTrackings.FirstOrDefaultAsync(t => t.Id.ToString() == oldItem.trackingId);
                    if (tracking != null) tracking.isBilled = false;

                    if (oldItem.Taxes.Any())
                        context.Taxs.RemoveRange(oldItem.Taxes);

                    context.ItemInvoice.Remove(oldItem);
                }

                // Suppression des taxes obsolètes
                foreach (var newItem in model.ItemInvoices)
                {
                    var existingTaxes = await context.Taxs.Where(x => x.ItemInvoiceId == newItem.Id).ToListAsync();
                    var newTaxIds = newItem.Taxes.Select(t => t.Id).ToHashSet();
                    var taxesToRemove = existingTaxes.Where(t => !newTaxIds.Contains(t.Id)).ToList();
                    if (taxesToRemove.Any())
                        context.Taxs.RemoveRange(taxesToRemove);
                }

                int index = 0;
                foreach (var item in model.ItemInvoices)
                {
                    index++;
                    var existingItem = await context.ItemInvoice.FirstOrDefaultAsync(e => e.Id == item.Id);

                    if (existingItem != null)
                    {
                        // Mise à jour d’un item existant
                        existingItem.description = item.description;
                        existingItem.ProjectId = item.ProjectId;
                        existingItem.Position = index;
                        existingItem.UpdatedBy = userId.ToString();
                        existingItem.qty = item.qty;
                        existingItem.rate = item.rate;

                        foreach (var tax in item.Taxes)
                        {
                            var existingTax = await context.Taxs.FirstOrDefaultAsync(e =>
                                e.ItemInvoiceId == item.Id && e.CompanyTaxId == tax.CompanyTaxId);

                            if (existingTax != null)
                            {
                                existingTax.UpdatedBy = userId.ToString();
                            }
                            else
                            {
                                tax.CompanyTaxId = tax.CompanyTaxId != Guid.Empty ? tax.CompanyTaxId : tax.Id;
                                tax.CreatedBy = userId.ToString();
                                tax.ItemInvoiceId = item.Id;
                                tax.Id = Guid.Empty;
                                context.Taxs.Add(tax);
                            }
                        }
                    }
                    else
                    {
                        // Ajout d’un nouvel item
                        foreach (var tax in item.Taxes)
                        {
                            tax.CompanyTaxId = tax.CompanyTaxId != Guid.Empty ? tax.CompanyTaxId : tax.Id;
                            tax.Id = Guid.Empty;
                        }

                        item.UpdatedBy = userId.ToString();
                        item.InvoiceId = model.Id;
                        item.Position = index;
                        item.UserId = userId;
                        context.ItemInvoice.Add(item);
                        await context.SaveChangesAsync(); // Nécessaire pour générer l’ID si EF ne le fait pas immédiatement

                        foreach (var tax in item.Taxes)
                        {
                            tax.CreatedBy = userId.ToString();
                            tax.CompanyTaxId = tax.CompanyTaxId != Guid.Empty ? tax.CompanyTaxId : tax.Id;
                            tax.ItemInvoiceId = item.Id;
                            tax.Id = Guid.Empty;
                            context.Taxs.Add(tax);
                        }
                    }

                    // Marquer les tracking / dépenses comme facturés
                    if (item.trackingId != null)
                    {
                        var tracking = await context.TimeTrackings.FirstOrDefaultAsync(t => t.Id.ToString() == item.trackingId);
                        if (tracking != null) tracking.isBilled = true;
                    }

                    if (item.ExpensesId != null)
                    {
                        var expense = await context.Expenses.FirstOrDefaultAsync(t => t.Id.ToString() == item.ExpensesId.ToString());
                        if (expense != null) expense.Status = (int)InvoiceStatus.BILLED;
                    }
                }
            }
            else
            {
                // Si aucun item : tout désassocier + suppression
                foreach (var item in existingItems)
                {
                    var tracking = await context.TimeTrackings.FirstOrDefaultAsync(x => x.Id.ToString() == item.trackingId);
                    if (tracking != null) tracking.isBilled = false;
                }

                context.ItemInvoice.RemoveRange(existingItems);
            }

            // Upload fichier s’il y a une pièce jointe
            if (!string.IsNullOrWhiteSpace(model.base64))
            {
                var file = DigitalOcean.Base64ToIFormFile(model.base64, model.filename, model.type);
                var uploaded = await DigitalOcean.UploadFileAsync(file, config, companyId.ToString());

                if (uploaded)
                {
                   // dataInvoice.Img = model.filename;
                }
            }
            // Mise à jour des infos principales de la facture
            dataInvoice.ClientId = model.ClientId;
            dataInvoice.ProjectId = model.ProjectId;
            dataInvoice.DueDate = model.DueDate;
            dataInvoice.InvoiceDate = model.InvoiceDate;
            dataInvoice.PaidAmount = model.PaidAmount;
            dataInvoice.TaxAmount = model.TaxAmount;
            dataInvoice.TotalAmount = model.TotalAmount;
            dataInvoice.Notes = model.Notes;
            dataInvoice.Position = model.Position;
            dataInvoice.ContractorId = model.ContractorId;
            dataInvoice.CompanyId = companyId;

            await context.SaveChangesAsync();

            return model;
        }

        public async Task<RequestInvoiceSend> CreatedInvoiceSend(RequestInvoiceSend invoiceSend, string UserId, Guid CompanyId)
        {
            try
            {
                foreach (var item in invoiceSend.ListEmail)
                {
                    InvoiceSend add = new InvoiceSend();
                    add.ClientId = invoiceSend.ClientId;
                    add.CompanyId = CompanyId;
                    add.Email = item;
                    add.IsEstimate = invoiceSend.IsEstimate;
                    add.CreatedBy = UserId;
                    add.InvoiceId = invoiceSend.InvoiceId;
                    context.InvoiceSend.Add(add);
                    await context.SaveChangesAsync();
                }

                return invoiceSend;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<RequestInvoice> CreatedInvoice(RequestInvoice invoice, string UserId, Guid CompanyId)
        {
            try
            {
               

                if (invoice.Payments.Count() > 0)
                {
                    invoice.Payments.ForEach(x =>
                    {
                        x.InvoiceId = invoice.Id;
                        x.CreatedBy = UserId;
                    });
                }
                if (invoice.ItemInvoices.Count() > 0)
                {
                    int index = 1;
                    invoice.ItemInvoices.ForEach(x =>
                    {
                        x.Position = index;
                        x.CreatedBy = UserId;
                        x.InvoiceId = invoice.Id;
                        index++;
                        x.UserId = Guid.Parse(UserId);
                        if (x.Taxes.Count() > 0)
                        {
                            x.Taxes.ForEach(k =>
                            {
                                k.CompanyTaxId = k.CompanyTaxId != Guid.Empty ? k.CompanyTaxId : k.Id;
                                k.ItemInvoiceId = x.Id;
                                k.Id = Guid.Empty;
                            });
                        }
                        if (x.trackingId != null)
                        {
                            var timeTracking = context.TimeTrackings.FirstOrDefault(t => t.Id.ToString() == x.trackingId);
                            if (timeTracking != null)
                            {
                                timeTracking.isBilled = true;
                            }

                        }
                        if (x.ExpensesId != null)
                        {
                            var dataExpenses = context.Expenses.FirstOrDefault(t => t.Id.ToString() == x.ExpensesId.ToString());
                            if (dataExpenses != null)
                            {
                                dataExpenses.Status = (int)InvoiceStatus.BILLED;
                                dataExpenses.DatePaid = DateTime.UtcNow;
                            }

                        }
                    });
                }

                invoice.ContractorId = await GetRoleBussiness(CompanyId.ToString(), UserId.ToString()) == UWIPConstants.Contractor ? Guid.Parse(UserId) : null;

                var newInvoice = new Invoice
                {
                    CreatedBy = UserId,
                   Position = context.Invoices.Where(x => x.CompanyId == CompanyId).Count(),
                    CompanyId = CompanyId,
                    ClientId = invoice.ClientId,
                    ProjectId = invoice.ProjectId,
                    ItemId = invoice.ItemId,
                    ContractorId = invoice.ContractorId,
                    InvoiceNumber = invoice.InvoiceNumber,
                    IsEstimate = invoice.IsEstimate,
                    InvoiceDate = invoice.InvoiceDate,
                    DueDate = invoice.DueDate,
                    Reference = invoice.Reference,
                    Notes = invoice.Notes,
                    TotalAmount = invoice.TotalAmount,
                    Rate = invoice.Rate,
                    PaidAmount = invoice.PaidAmount,
                    TaxAmount = invoice.TaxAmount,
                    Description = invoice.Description,
                    TimeAmount = invoice.TimeAmount,
                    Status = invoice.Status,
                    Payments = invoice.Payments,
                    ItemInvoices = invoice.ItemInvoices,
                };
                
                context.Invoices.Add(newInvoice);
                await context.SaveChangesAsync();
                if (invoice.base64 != null && invoice.base64 != "")
                {
                    var file = DigitalOcean.Base64ToIFormFile(invoice.base64, invoice.filename, invoice.type);
                    var result = await DigitalOcean.UploadFileAsync(file, config, CompanyId.ToString());
                }
                return invoice;

            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<bool> UpdateArchive(Guid InvoiceId, bool isArchive)
        {
            try
            {

                var data = context.Invoices.FirstOrDefault(x => x.Id == InvoiceId);
                if (data != null)
                {
                    data.isArchive = isArchive;
                    await context.SaveChangesAsync();
                }
                else
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }
        public async Task<bool> DeleteInvoice(List<Guid?> listInvoice, bool isActive)
        {
            try
            {
                var listItemDetail = context.Invoices.Where(x => listInvoice.Contains(x.Id)).ToList();
                foreach (var item in listItemDetail)
                {
                    var dt = context.Invoices.FirstOrDefault(x => x.Id == item.Id);
                    dt.isActive = isActive;
                }
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public async Task<decimal> CalculateInvoice(int status, Guid CompanyId)
        {
            decimal total = 0;
            var data = context.Invoices.Where(x => x.CompanyId == CompanyId);
            switch (status)
            {
                case 0:
                    total = await data.Where(x => x.Status == 0).SumAsync(x => x.PaidAmount);
                    break;

                case 1:
                    total = await data.Where(x => x.Status == 1).SumAsync(x => x.PaidAmount);
                    break;

                case 2:
                    total = await data.Where(x => x.Status == 2).SumAsync(x => x.PaidAmount);
                    break;

                default:
                    throw new ArgumentException("Invalid status value. Accepted values are 0, 1, or 2.");
            }

            return total;

        }


        public async Task<InvoiceDetailDTO> GetInvoiceById(Guid InvoiceId,string CompanyId)
        {
            // Get invoice
            var invoice = await context.Invoices.Include(c => c.Company)
                                       .Include(c => c.Client)
                                       .Include(c => c.ItemInvoices)
                                       .Select(invoice => new InvoiceDetailDTO
                                       {
                                           Id = invoice.Id,
                                           ClientId = invoice.ClientId,
                                           ProjectId = invoice.ProjectId,
                                           InvoiceNumber = invoice.InvoiceNumber,
                                           InvoiceDate = invoice.InvoiceDate,
                                           DueDate = invoice.DueDate,
                                           Notes = invoice.Notes,
                                           TotalAmount = invoice.TotalAmount,
                                           Rate = invoice.Rate,
                                           PaidAmount = invoice.PaidAmount,
                                           TaxAmount = invoice.TaxAmount,
                                           Description = invoice.Description,
                                           isPaid = invoice.isPaid,
                                           Status = invoice.Status,
                                           Position = invoice.Position,
                                           CreatedAt = invoice.CreatedAt,
                                           Company = new CompanyDetailInvoiceDTO
                                           {
                                               BusinessName = invoice.Company.BusinessName,
                                               Phone = invoice.Company.Phone,
                                               CompanyImage = invoice.Company.CompanyImage,
                                               Email = invoice.Company.Email,
                                               Country = invoice.Company.Country,
                                               Adress = invoice.Company.Adress,
                                               Adress2 = invoice.Company.Adress2,
                                               City = invoice.Company.City,
                                               Province = invoice.Company.Province,
                                               PostalCode = invoice.Company.PostalCode,
                                           },

                                           Client = new ClientDetailInvoiceDTO
                                           {
                                               Id = invoice.Client.Id,
                                               ClientName = invoice.Client.ClientName,
                                               EmailAddress = invoice.Client.EmailAddress,
                                               PhoneNumber = invoice.Client.PhoneNumber,
                                               BusinessPhoneNumber = invoice.Client.BusinessPhoneNumber,
                                               MobilePhoneNumber = invoice.Client.MobilePhoneNumber,
                                               PostePhoneNumber = invoice.Client.PostePhoneNumber,
                                               PosteBusinessPhoneNumber = invoice.Client.PosteBusinessPhoneNumber,
                                               PosteMobilePhoneNumber = invoice.Client.PosteMobilePhoneNumber,
                                               Country = invoice.Client.Country,
                                               AddressLine1 = invoice.Client.AddressLine1,
                                               AddressLine2 = invoice.Client.AddressLine2,
                                               TownCity = invoice.Client.TownCity,
                                               StateProvince = invoice.Client.StateProvince,
                                               PostalCode = invoice.Client.PostalCode,

                                           },
                                           ItemInvoices = invoice.ItemInvoices.Select(c => new ItemInvoicesDetailInvoiceDTO() { Id = c.Id}).ToList()
                                       })
                                       .FirstOrDefaultAsync(x => x.Id == InvoiceId);

            // get invoice iteems
            var invoiceItemIds = invoice.ItemInvoices.Select(c => c.Id);
            var invoiceItems = await context.ItemInvoice.Include(c => c.User)
                                                         .Include(c => c.Project)
                                                         .Include(c => c.Service)
                                                         .Include(c => c.Item)
                                                         .Include(c => c.Taxes)
                                                         .ThenInclude(c => c.CompanyTax)
                                                         .Where(c => invoiceItemIds.Contains(c.Id)).OrderBy(c => c.Position)
                                                         .AsNoTracking()
                                                         .Select(ii => new ItemInvoicesDetailInvoiceDTO
                                                         {
                                                             Id = ii.Id,
                                                             InvoiceId = ii.InvoiceId,
                                                             ProjectId = ii.ProjectId,
                                                             ItemId = ii.ItemId,
                                                             ServiceId = ii.ServiceId,
                                                             UserId = ii.UserId,
                                                             rate = ii.rate,
                                                             qty = ii.qty,
                                                             description = ii.description,
                                                             Position = ii.Position,
                                                             DateSelectItem = ii.DateSelectItem,
                                                             trackingId = ii.trackingId,
                                                             //User = ii.User != null ? new DTOUserMember
                                                             //{
                                                             //    Id = ii.User.Id,
                                                             //    FirstName = ii.User.FirstName,
                                                             //    LastName = ii.User.LastName,
                                                             //    Email = ii.User.Email,
                                                             //} : null,
                                                             //Project = ii.Project != null ? new ProjectInTimeTrackingDTO
                                                             //{
                                                             //    Id = ii.Project.Id,
                                                             //    ProjectName = ii.Project.ProjectName,
                                                             //    Description = ii.Project.Description,
                                                             //} : null,
                                                             //Service = ii.Service != null ? new ServiceInTimeTrackingDTO
                                                             //{
                                                             //    Id = ii.Service.Id,
                                                             //    ServiceName = ii.Service.ServiceName
                                                             //} : null,
                                                             //Item = ii.Item != null ? new ItemDetailInvoiceDTO
                                                             //{
                                                             //    Id = ii.Item.Id,
                                                             //    Description = ii.Item.Description,
                                                             //    ItemName = ii.Item.ItemName,
                                                         
                                                             //} : null,
                                                             Taxes = ii.Taxes.Select(t => new TaxItem
                                                             {
                                                                 Id = t.Id,
                                                                 CompanyTaxId = t.CompanyTaxId,
                                                                 Name = t.CompanyTax.Name,
                                                                 Amount = t.CompanyTax.Amount,
                                                                 TaxeNumber = t.CompanyTax.TaxeNumber
                                                             }).ToList()
                                                         }).ToListAsync();
            invoice.ItemInvoices = invoiceItems;
            return invoice;
        }
        public async Task<MemoryStream> PrintInvoice(Guid InvoiceId, Guid CompanyId, Guid UserId)
        {
            var userTimeZoneId = context.Users
                                        .Where(c => c.Id == UserId)
                                        .Select(c => c.TimeZoneId).FirstOrDefault();
            var invoiceDetails = await context.Invoices.AsNoTracking().Include(c => c.Company)
                                     .Include(c => c.Project)
                                     .Include(c => c.Client)
                                     .Include(c => c.ItemInvoices.OrderBy(x => x.Position))
                                     .ThenInclude(x => x.Taxes)
                                     .ThenInclude(k => k.CompanyTax)
                                     .Include(c => c.ItemInvoices)
                                     .ThenInclude(ii => ii.User)
                                     .Include(c => c.ItemInvoices)
                                     .ThenInclude(ii => ii.Project)
                                     .Include(c => c.ItemInvoices)
                                     .ThenInclude(ii => ii.Service)
                                     .Include(c => c.ItemInvoices)
                                     .ThenInclude(ii => ii.Item).FirstOrDefaultAsync(x => x.Id == InvoiceId && x.CompanyId == CompanyId);
            //.Include(c => c.Taxes)


            var templateService = new PrintPDFService(config);

            return templateService.PrintPDFInvoice(invoiceDetails, userTimeZoneId);
        }


        public async Task<PaginatedResponse<InvoiceAllDTO>> GetAllEstimate(GetEstimateRequestParam filter, string companyId, string UserId)
        {
            var query = context.Invoices.Include(c => c.Company)
                                        .Include(c => c.Client)
                                      //  .Include(c => c.Taxes)
                                        .Where(c => c.CompanyId.ToString() == companyId && c.IsEstimate && c.isActive);

            if (filter.Role != UserBusinessRole.Admin)
            {
                query = query.Where(c => c.CreatedBy == UserId);
            }

            if (!string.IsNullOrEmpty(filter.Search))
            {
                query = query.Where(x => x.Client != null && x.Client.ClientName.ToLower().Contains(filter.Search.ToLower()));
            }

            var totalRecords = await query.CountAsync();
            var panigationQuery = query
                                 .Select(i => new InvoiceAllDTO
                                 {
                                     Id = i.Id,
                                     ContractorId = i.ContractorId,
                                     InvoiceNumber = i.InvoiceNumber,
                                     IsEstimate = i.IsEstimate,
                                     InvoiceDate = i.InvoiceDate,
                                     DueDate = i.DueDate,
                                     Reference = i.Reference,
                                     Notes = i.Notes,
                                     TotalAmount = i.TotalAmount,
                                     Rate = i.Rate,
                                     PaidAmount = i.PaidAmount,
                                     TaxAmount = i.TaxAmount,
                                     Description = i.Description,
                                     isArchive = i.isArchive,
                                     isActive = i.isActive,
                                     isPaid = i.isPaid,
                                     TimeAmount = i.TimeAmount,
                                     Status = i.Status,
                                     Position = i.Position,
                                     Client = i.Client != null ? new ClientInInvoice
                                     {
                                         Id = i.Client.Id,
                                         ClientName = i.Client.ClientName,
                                         EmailAddress = i.Client.EmailAddress,
                                     } : null

                                 })
                                 .AsQueryable();



            if (filter.ColumnName != null && filter.Direction != null)
            {
                if (filter.ColumnName == "ClientName")
                {
                    panigationQuery = filter.Direction == "Ascending" ? panigationQuery.OrderBy(c => c.Client.ClientName) : panigationQuery.OrderByDescending(c => c.Client.ClientName);
                }
                else
                {
                    panigationQuery = SortExtensions.DynamicSort(panigationQuery, filter.ColumnName, filter.Direction != "Ascending");
                }
            }
            
            if (filter.Page > 0 && filter.PageSize > 0)
            {
                panigationQuery = panigationQuery.Skip((filter.Page - 1) * filter.PageSize)
                                                 .Take(filter.PageSize);
            }

            var result = await panigationQuery.ToListAsync();

            return new PaginatedResponse<InvoiceAllDTO>
            {
                Data = result,
                Page = filter.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)filter.PageSize),
                PageSize = filter.PageSize,
                TotalRecords = totalRecords
            };
        }

        public async Task<PaginatedResponse<InvoiceAllDTO>> GetAllInvoice(GetInvoiceRequestParam filter, string companyId, string UserId)
        {
            var query = context.Invoices.Include(c => c.Company)
                                   .Include(c => c.Client)
                               //    .Include(c => c.Taxes)
                                   .Where(c => c.CompanyId.ToString() == companyId && !c.IsEstimate && c.isActive);

            if (filter.Role == UserBusinessRole.Contractor)
            {
                query = query.Where(c => c.CreatedBy == UserId || c.ContractorId == Guid.Parse(UserId));
            }

            if (!string.IsNullOrEmpty(filter.Search))
            {
                query = query.Where(x => x.Client != null && x.Client.ClientName != null && x.Client.ClientName.ToLower().Contains(filter.Search.ToLower()));

            }

            var totalRecords = await query.CountAsync();
            var panigationQuery = query
                                 .Select(i => new InvoiceAllDTO
                                 {
                                     Id = i.Id,
                                     ContractorId = i.ContractorId,
                                     InvoiceNumber = i.InvoiceNumber,
                                     IsEstimate = i.IsEstimate,
                                     InvoiceDate = i.InvoiceDate,
                                     DueDate = i.DueDate,
                                     Reference = i.Reference,
                                     Notes = i.Notes,
                                     TotalAmount = i.TotalAmount,
                                     Rate = i.Rate,
                                     PaidAmount = i.PaidAmount,
                                     TaxAmount = i.TaxAmount,
                                     Description = i.Description,
                                     isArchive = i.isArchive,
                                     isActive = i.isActive,
                                     isPaid = i.isPaid,
                                     TimeAmount = i.TimeAmount,
                                     Status = i.Status,
                                     Position = i.Position,
                                     Client = i.Client != null ? new ClientInInvoice
                                     {
                                         Id = i.Client.Id,
                                         ClientName = i.Client.ClientName,
                                         EmailAddress = i.Client.EmailAddress,
                                     } : null

                                 })
                                 .AsQueryable();



            if (filter.ColumnName != null && filter.Direction != null)
            {
                if (filter.ColumnName == "ClientName")
                {
                    panigationQuery = filter.Direction == "Ascending" ? panigationQuery.OrderBy(c => c.Client.ClientName) : panigationQuery.OrderByDescending(c => c.Client.ClientName);
                }
                else
                {
                    panigationQuery = SortExtensions.DynamicSort(panigationQuery, filter.ColumnName, filter.Direction != "Ascending");
                }
            }
            else
            {
                panigationQuery = SortExtensions.DynamicSort(panigationQuery, "InvoiceNumber", filter.Direction != "Descending");
            }

            if (filter.Page > 0 && filter.PageSize > 0)
            {
                panigationQuery = panigationQuery.Skip((filter.Page - 1) * filter.PageSize)
                                                 .Take(filter.PageSize);
            }

            var result = await panigationQuery.ToListAsync();

            return new PaginatedResponse<InvoiceAllDTO>
            {
                Data = result,
                Page = filter.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)filter.PageSize),
                PageSize = filter.PageSize,
                TotalRecords = totalRecords
            };
        }

        public async Task<PaginatedResponse<EstimateAllSendToMeDTO>> GetAllInvoiceSendToMe(GetInvoiceRequestParam filter, string companyId, string UserId)
        {
            var company = await context.Companies.FirstOrDefaultAsync(c => c.Id.ToString() == companyId);

            var query = context.InvoiceSend.Include(c => c.Invoice)
                                            .Include(c => c.Company)
                                            .Where(c => c.CompanyId.ToString() == companyId
                                                          && c.Email == company.Email && !c.IsEstimate && c.Invoice.isActive);

            if (filter.Role == UserBusinessRole.Contractor)
            {
                query = query.Where(c => c.CreatedBy == UserId || c.Invoice.ContractorId == Guid.Parse(UserId));
            }

            if (!string.IsNullOrEmpty(filter.Search))
            {
                query = query.Where(x => x.Client != null && x.Client.ClientName != null && x.Client.ClientName.ToLower().Contains(filter.Search.ToLower()));
            }

            var totalRecords = await query.CountAsync();
            var panigationQuery = query.Select(i => new EstimateAllSendToMeDTO
                                                    {
                                                        Id = i.Id,
                                                        InvoiceId = i.InvoiceId,
                                                        ClientId = i.ClientId,
                                                        Email = i.Email,
                                                        CreatedAt = i.CreatedAt,
                                                        IsEstimate = i.IsEstimate,
                                                        InvoiceNumber = i.Invoice.InvoiceNumber,
                                                        InvoiceDate = i.Invoice.InvoiceDate,
                                                        DueDate = i.Invoice.DueDate,
                                                        Reference = i.Invoice.Reference,
                                                        Notes = i.Invoice.Notes,
                                                        isPaid = i.Invoice.isPaid,
                                                        TotalAmount = i.Invoice.TotalAmount,
                                                        PaidAmount = i.Invoice.PaidAmount,
                                                        Company = i.Company != null ? new EstimateCompanyDTO
                                                        {
                                                            BusinessName = i.Company.BusinessName,
                                                            Email = i.Company.Email,
                                                        } : null,

                                                        Client = i.Client != null ? new ClientInInvoice
                                                        {
                                                            Id = i.Client.Id,
                                                            ClientName = i.Client.ClientName,
                                                            EmailAddress = i.Client.EmailAddress,
                                                        } : null

                                                    })
                                        .OrderByDescending(c => c.CreatedAt)
                                        .AsQueryable();

            if (filter.ColumnName != null && filter.Direction != null)
            {
                panigationQuery = SortExtensions.DynamicSort(panigationQuery, filter.ColumnName, filter.Direction != "Ascending");
            }

            if (filter.Page > 0 && filter.PageSize > 0)
            {
                panigationQuery = panigationQuery.Skip((filter.Page - 1) * filter.PageSize)
                                                 .Take(filter.PageSize);
            }

            var result = await panigationQuery.ToListAsync();

            return new PaginatedResponse<EstimateAllSendToMeDTO>
            {
                Data = result,
                Page = filter.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)filter.PageSize),
                PageSize = filter.PageSize,
                TotalRecords = totalRecords
            };
        }
        public async Task<int> CountInvoiceByCompany(Guid CompanyId)
        {
            var count = await context.Invoices.Where(c => c.CompanyId == CompanyId & c.IsEstimate == false).CountAsync();
            return count;
        }
        public async Task<CalculationSendToMe> CalculationInvoiceSendToMe(int status, Guid UserId, string companyId)
        {
            decimal TotalOverdue = 0;
            decimal TotalAmount = 0;
            decimal totalDraft = 0;
            var company = await context.Companies.FirstOrDefaultAsync(c => c.Id.ToString() == companyId);
            var data = await context.InvoiceSend.AsNoTracking().Where(c => c.CompanyId.ToString() == companyId && c.Email == company.Email && c.IsEstimate == false && c.Invoice.isActive == true).Include(c => c.Company)
                                    .Include(c => c.Invoice)
                                    .ThenInclude(x => x.Company)
                                     .ToListAsync();
            if (data != null)
            {

                foreach (var item_ in data)
                {

                    TotalAmount += item_.Invoice.TotalAmount;
                    if (item_.Invoice.DueDate < DateTime.UtcNow)
                    {
                        TotalOverdue += item_.Invoice.TotalAmount;
                    }
                    if (item_.Invoice.Status == status)
                        totalDraft += 1;
                }

            }
            return new CalculationSendToMe
            {
                TotalOverdue = TotalOverdue,
                TotalDraft = totalDraft,
                TotalAmount = TotalAmount,
            };
        }

        public CalculationInvoice CalculationInvoice(int status, string role, Guid UserId, string companyId)
        {
            var query = context.Invoices.AsNoTracking().Where(c => c.CompanyId.ToString() == companyId && !c.IsEstimate && c.isActive);

            if (role == UserBusinessRole.Contractor)
            {
                query = query.Where(c => c.CreatedBy == UserId.ToString() || c.ContractorId == UserId);
            }

            return new CalculationInvoice
            {
                TotalOverdue = query.Where(c => c.DueDate < DateTime.UtcNow).Sum(c => c.TotalAmount),
                TotalDraft = query.Where(c => c.Status == status).Count(),
                TotalAmount = query.Sum(c => c.TotalAmount),
            };
        }

        public async Task<int> CountEstimate(Guid CompanyId)
        {
            var count = await context.Invoices.Where(c => c.CompanyId == CompanyId && c.IsEstimate == true).CountAsync();
            return count;
        }

        public async Task<CalculationEstimate> CalculationEstimate(int status, Guid UserId, string companyId)
        {
            decimal TotalOverdue = 0;
            decimal TotalAmount = 0;
            decimal totalDraft = 0;
            var data = await context.Invoices.AsNoTracking().Where(c => c.CompanyId.ToString() == companyId && c.IsEstimate == true && c.isActive == true).Include(c => c.Company)
                                    .Include(c => c.Client)
                              //      .Include(c => c.Taxes)
                                     .ToListAsync();
            if (data != null)
            {

                foreach (var item_ in data)
                {

                    TotalAmount += item_.TotalAmount;
                    if (item_.DueDate < DateTime.UtcNow)
                    {
                        TotalOverdue += item_.TotalAmount;
                    }
                    if (item_.Status == status)
                        totalDraft += 1;
                }

            }
            return new CalculationEstimate
            {
                TotalOverdue = TotalOverdue,
                TotalDraft = totalDraft,
                TotalAmount = TotalAmount,

            };
        }

        public async Task<PaginatedResponse<EstimateAllSendToMeDTO>> GetAllEstimateSendToMe(PaginatedRequest query, string companyId, string UserId)
        {
            var company = await context.Companies.FirstOrDefaultAsync(c => c.Id.ToString() == companyId);
            var role = await context.UserBusinesses.FirstOrDefaultAsync(c => c.CompanyId.ToString() == companyId && c.UserId.ToString() == UserId);

            var data = context.InvoiceSend.AsNoTracking().Where(c => c.CompanyId.ToString() == companyId && c.Email == company.Email && c.IsEstimate == true).Include(c => c.Company)
                           .Include(c => c.Invoice).ThenInclude(c => c.Company)
                             .Select(i => new EstimateAllSendToMeDTO
                             {
                                 Id = i.Id,
                                 InvoiceId = i.InvoiceId,
                                 ClientId = i.ClientId,
                                 Email = i.Email,
                                 CreatedAt = i.CreatedAt,
                                 IsEstimate = i.IsEstimate,
                                 InvoiceNumber = i.Invoice.InvoiceNumber,
                                 InvoiceDate = i.Invoice.InvoiceDate,
                                 DueDate = i.Invoice.DueDate,
                                 Reference = i.Invoice.Reference,
                                 Notes = i.Invoice.Notes,
                                 isPaid = i.Invoice.isPaid,
                                 TotalAmount = i.Invoice.TotalAmount,
                                 PaidAmount = i.Invoice.PaidAmount,
                                 Company = i.Invoice.Company != null ? new EstimateCompanyDTO
                                 {
                                     BusinessName = i.Invoice.Company.BusinessName,
                                     Email = i.Invoice.Company.Email,
                                 } : null,

                                 Client = i.Client != null ? new ClientInInvoice
                                 {
                                     Id = i.Client.Id,
                                     ClientName = i.Client.ClientName,
                                     EmailAddress = i.Client.EmailAddress,
                                 } : null

                             })
                           .OrderByDescending(c => c.CreatedAt)
                           .AsQueryable();


            if (!string.IsNullOrEmpty(query.Search))
            {
                data = data.Where(x => x.Client != null && x.Client.ClientName.ToLower().Contains(query.Search.ToLower()));
            }
            if (query.Filter != null && query.Filter.ContainsKey("Sort") && query.Filter["Sort"] != null)
            {
                var sort = JsonConvert.DeserializeObject<SortDTO>(query.Filter["Sort"])!;

                data = SortExtensions.DynamicSort(data, sort.columnName, sort.direction == "Ascending" ? false : true);

            }
            var totalRecords = await data.CountAsync();

            var result = await data
            .Skip((query.Page - 1) * query.PageSize)
            .Take(query.PageSize)
            .ToListAsync();

            return new PaginatedResponse<EstimateAllSendToMeDTO>
            {
                Data = result,
                Page = query.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)query.PageSize),
                PageSize = query.PageSize,
                TotalRecords = totalRecords
            };
        }

        public async Task<CalculationSendToMe> CalculationEstimateSendToMe(int status, Guid UserId, string companyId)
        {
            decimal TotalOverdue = 0;
            decimal TotalAmount = 0;
            decimal totalDraft = 0;
            var company = await context.Companies.FirstOrDefaultAsync(c => c.Id.ToString() == companyId);
            var data = await context.InvoiceSend.AsNoTracking().Where(c => c.CompanyId.ToString() == companyId && c.Email == company.Email && c.IsEstimate == true && c.Invoice.isActive == true).Include(c => c.Company)
                                    .Include(c => c.Invoice)
                                    .ThenInclude(x => x.Company)
                                     .ToListAsync();
            if (data != null)
            {

                foreach (var item_ in data)
                {

                    TotalAmount += item_.Invoice.TotalAmount;
                    if (item_.Invoice.DueDate < DateTime.UtcNow)
                    {
                        TotalOverdue += item_.Invoice.TotalAmount;
                    }
                    if (item_.Invoice.Status == status)
                        totalDraft += 1;
                }

            }
            return new CalculationSendToMe
            {
                TotalOverdue = TotalOverdue,
                TotalDraft = totalDraft,
                TotalAmount = TotalAmount,

            };
        }

        public async Task<bool> ConvertToInvoice(Guid InvoiceId)
        {
            try
            {

                var data = context.Invoices.FirstOrDefault(x => x.Id == InvoiceId);
                if (data != null)
                {
                    data.IsEstimate = false;
                    await context.SaveChangesAsync();
                }
                else
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> ChangePosition(RequestChangePosition requestChangePosition)
        {
            try
            {
                var fromInvoice = await context.Invoices.FindAsync(Guid.Parse(requestChangePosition.fromInvoiceId));
                var dropInvoice = await context.Invoices.FindAsync(Guid.Parse(requestChangePosition.dropInvoiceId));
                fromInvoice.Position = requestChangePosition.dropIndex;
                dropInvoice.Position = requestChangePosition.fromIndex;
                await context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
        //public async Task<bool> ChangePositionItemInvoice(RequestChangePosition requestChangePosition)
        //{
        //    try
        //    {
        //        var fromInvoice = await context.ItemInvoice.FindAsync(Guid.Parse(requestChangePosition.fromInvoiceId));
        //        var dropInvoice = await context.ItemInvoice.FindAsync(Guid.Parse(requestChangePosition.dropInvoiceId));
        //        fromInvoice.Position = requestChangePosition.dropIndex;
        //        dropInvoice.Position = requestChangePosition.fromIndex;
        //        await context.SaveChangesAsync();
        //        return true;
        //    }
        //    catch
        //    {
        //        return false;
        //    }
        //}

        public async Task<bool> ResetPosition()
        {
            try
            {

                int index = 1;
                var data = context.Invoices.OrderBy(x => x.CreatedAt).ToList();
                foreach (var item in data)
                {
                    item.Position = index;
                    index++;
                    await context.SaveChangesAsync();
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> ResetPositionItemInvoice()
        {
            try
            {

                int index = 1;
                var data = context.ItemInvoice.OrderBy(x => x.CreatedAt).ToList();
                foreach (var item in data)
                {
                    item.Position = index;
                    index++;
                    await context.SaveChangesAsync();
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<string> GetRoleBussiness(string companyId, string UserId)
        {
            var data = await context.UserBusinesses.FirstOrDefaultAsync(x => x.CompanyId == Guid.Parse(companyId) && x.UserId == Guid.Parse(UserId));
            return data.Role ?? null;
        }

        public async Task<bool> MarkAsPaid(string Id)
        {
            try
            {
                var data = await context.Invoices.FindAsync(Guid.Parse(Id));
                data.isPaid = true;
                data.DatePaid = DateTime.UtcNow;
                data.Status = (int)InvoiceStatus.PAID;
                await context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
        public async Task<bool> MarkAsSent(string Id)
        {
            try
            {
                var data = await context.Invoices.FindAsync(Guid.Parse(Id));
                data.Status = (int)InvoiceStatus.SENT;
                await context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }
        public async Task<int> CountInvoiceByContractor(Guid CompanyId)
        {
            var count = await context.Invoices.Where(c => c.CompanyId == CompanyId & c.IsEstimate == false & c.ContractorId != null).CountAsync();
            return count;
        }

        public async Task<GetResultChartDTO> RevenueChart(string CompanyId, int Year)
        {
            var today = DateTime.UtcNow;
            var monthsData = new List<MonthData>();
            var data = await context.Invoices
                .Where(x => x.CompanyId.ToString() == CompanyId&& x.CreatedAt.Year== Year)
                .ToListAsync();  

            var groupedData = data
                .GroupBy(x => new { x.DueDate.Year, x.DueDate.Month })
                .Select(g => new
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    TotalAmount = g.Where(x=>today < x.DueDate).Sum(x => x.TotalAmount), 
                    TotalAmountPaid = g.Where(x => x.isPaid).Sum(x => x.TotalAmount)  
                })
                .ToList();

            foreach (var group in groupedData)
            {
                monthsData.Add(new MonthData
                {
                    MonthName = new DateTime(group.Year, group.Month, 1).ToString("MMMM"),
                    Month = group.Month,
                    TotalAmount = group.TotalAmount,
                    TotalAmountPaid = group.TotalAmountPaid
                });
            }

            var totalAmount = monthsData.Sum(m => m.TotalAmount);
            var totalAmountPaid = monthsData.Sum(m => m.TotalAmountPaid);

            var response = new GetResultChartDTO
            {
                Year = Year,
                TotalAmount = totalAmount,
                totalAmountPaid = totalAmountPaid,
                MonthData = monthsData
            };
          
            return response;

        }

        public async Task<GraphicsChartDTO> GraphicsChart(string CompanyId, int Year)
        {
            var today = DateTime.UtcNow;
            var monthsDataInvoice = new List<MonthDataGraphicsChart>();
                   var monthsDataExpenses = new List<MonthDataGraphicsChart>();
            var data = await context.Invoices
                .Where(x => x.CompanyId.ToString() == CompanyId)
                .ToListAsync();
            var dataExpenses = await context.Expenses
              .Where(x => x.CompanyId.ToString() == CompanyId)
              .ToListAsync();
            var groupedDataExpneses = dataExpenses
                 .Where(t => t.DatePaid != null)
               .GroupBy(x => new { x.DatePaid?.Year, x.DatePaid?.Month })
               .Select(g => new
               {
                   Year = g.Key.Year,
                   Month = g.Key.Month,
                   TotalAmountPaid = g.Where(x => x.Status == (int)ExpensesEnum.BILLED).Sum(x => x.PaidAmount)
               })
               .ToList();
            var groupedData = data
                .Where(t => t.DatePaid != null)
                .GroupBy(x => new { x.DatePaid?.Year, x.DatePaid?.Month })
                .Select(g => new
                {
                    Year = g.Key.Year,
                    Month = g.Key.Month,
                    TotalAmountPaid = g.Where(x => x.isPaid).Sum(x => x.PaidAmount)
                })
                .ToList();

            foreach (var group in groupedData)
            {
                monthsDataInvoice.Add(new MonthDataGraphicsChart
                {
                    MonthName = new DateTime((int)group.Year, (int)group.Month, 1).ToString("MMMM"),
                    Month = group.Month??0,
                    PaidAmount = group.TotalAmountPaid,
                });
            }
            // for expenses
            foreach (var group in groupedDataExpneses)
            {
                monthsDataExpenses.Add(new MonthDataGraphicsChart
                {
                    MonthName = new DateTime((int)group.Year, (int)group.Month, 1).ToString("MMMM"),
                    Month = group.Month??0,
                    PaidAmount = group.TotalAmountPaid,
                });
            }


            var PaidAmount = monthsDataInvoice.Sum(m => m.PaidAmount);

            return  new GraphicsChartDTO
            {
                MonthDataInvoice= monthsDataInvoice,
                MonthDataExpenses = monthsDataExpenses,
            };
            
        }
    }
}
