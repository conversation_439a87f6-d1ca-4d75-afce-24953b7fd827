<app-inno-modal-wrapper
  [title]="title"
  (onClose)="handleClose()">
  <form [formGroup]="invoiceItemForm">
    <div class="w-full flex flex-col gap-[16px] px-[16px] pb-[16px]">

      <app-inno-form-textarea
        label="Description"
        [isAbleResize]="true"
        [formControl]="f['description']"
        [value]="f['description'].value"
        [placeholder]="'ITEM_INVOICE.EnterInvoiceDescription'|translate" />

      <app-inno-form-input
        type="number"
        label="Rate"
        placeholder="0.00"
        [formControl]="f['rate']"
        [value]="f['rate'].value"
        [errorMessages]="{ required: 'ITEM_INVOICE.RateRequired'|translate }" />

      <app-inno-form-input
        type="number"
        label="Qty"
        [placeholder]="'ITEM_INVOICE.EnterQty'|translate"
        [formControl]="f['qty']"
        [value]="f['qty'].value"
        [errorMessages]="{ required: 'ITEM_INVOICE.QuantityRequired'|translate }" />

      @if(listTaxName?.length) {
      <div class="w-full flex flex-col relative">
        <label
          class="text-text-secondary text-text-sm-semibold mb-[2px]">Taxes</label>
        <p
          class="text-text-primary text-text-sm-bold">
          {{ listTaxName }}
        </p>
      </div>
      }

      <button class="button-link-primary" (click)="handleModifyTaxes()">
        {{'ITEM_INVOICE.AddUpdateTaxes'|translate}}
      </button>

    </div>
    <app-inno-modal-footer
      (onCancel)="handleCancel()"
      (onSubmit)="handleSubmit()" />
  </form>
</app-inno-modal-wrapper>
