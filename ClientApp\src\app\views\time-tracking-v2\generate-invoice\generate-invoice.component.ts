import { StoreService } from 'app/service/store.service';
import { Component, inject, Inject, OnInit, ViewChild } from '@angular/core';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoFormSelectSearchComponent } from 'app/component/inno-form-select-search/inno-form-select-search.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { SharedModule } from 'app/module/shared.module';
import { DropdownOptionsService } from 'app/service/dropdown-options.service';
import { AvatarModule } from 'ngx-avatars';
import { MatRadioChange, MatRadioModule } from '@angular/material/radio';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { InnoFormDatepickerComponent } from 'app/component/inno-form-datepicker/inno-form-datepicker.component';
import { InnoErrorMMessageComponent } from 'app/component/inno-error-message/inno-error-message.component';
import { convertHoursToDecimal, formatDateFilter, getStartAndEndOfMonth, isValidDateRange } from 'app/helpers/common.helper';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { ToastService } from 'app/service/toast.service';
import { InvoiceRangDateType } from 'app/enum/invoice.enum';
import { GenerateInvoiceProvider } from './generate-invoice.provider';
import { InvoiceItem } from '../../../dto/interface/invoiceItem.interface';
import { ModifyInvoiceItemDialog } from '../../../service/dialog/modify-invoice-item.dialog';
import { NewInvoiceDialog } from '../../../service/dialog/new-invoice.dialog';
import { Pager, PagerDropDown, PagerModule } from '@syncfusion/ej2-angular-grids';
import { GenrateInvoiceExpensesComponent } from './genrate-invoice-expenses/genrate-invoice-expenses.component';
import { DecimalPipe } from 'app/pipes/decimal.pipe';
import { TranslateService } from '@ngx-translate/core';
import { calculateGroupedTaxes, calculateTotalInvoiceItem } from '../../../utils/invoice.helper';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-generate-invoice',
  templateUrl: './generate-invoice.component.html',
  styleUrls: ['./generate-invoice.component.scss'],
  standalone: true,
  imports: [
    FormatNumberPipe,
    MatRadioModule,
    AvatarModule,
    SharedModule,
    PagerModule,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormSelectSearchComponent,
    InnoPopoverComponent,
    InnoSpinomponent,
    InnoFormDatepickerComponent,
    InnoErrorMMessageComponent,
    InnoEmptyDataComponent,
    InnoFormCheckboxComponent,
    InnoTableActionComponent,
    InnoFormInputComponent,
    DecimalPipe,
    GenrateInvoiceExpensesComponent
  ]
})
export class GenerateInvoiceComponent implements OnInit {
  public rangDateType = InvoiceRangDateType
  listProjectId: string[] = []
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 20
  public selectedRadio: any = 0
  public isFetchingProject: boolean = false;
  public clientOptions: IFilterDropdownOption[] = []
  public generateInvoiceForm!: UntypedFormGroup;
  public isShowCustomDateError: boolean = false;
  public listInvoiceItem: InvoiceItem[] = []
  public listIndexInvoiceSelected: number[] = []
  public calculateTotalInvoiceItem = calculateTotalInvoiceItem

  private translate = inject(TranslateService)
  public _toastService = inject(ToastService)
  public _storeService = inject(StoreService)
  private formBuilder = inject(UntypedFormBuilder)
  private dropdownOptionService = inject(DropdownOptionsService)
  private generateInvoiceProvider = inject(GenerateInvoiceProvider)

  @ViewChild('selectSearchClientElement') selectSearchClientElement!: InnoFormSelectSearchComponent;
  @ViewChild('rangeDatePopover') rangeDatePopoverElement!: InnoPopoverComponent;
  public rangDateOptions: IFilterDropdownOption[] = [
    { label: this.translate.instant('GENERATEINVOICE.RangDateOptions.AllTime'), value: this.rangDateType.all },
    { label: this.translate.instant('GENERATEINVOICE.RangDateOptions.ThisMonth'), value: this.rangDateType.this_month },
    { label: this.translate.instant('GENERATEINVOICE.RangDateOptions.LastMonth'), value: this.rangDateType.last_month },
    { label: this.translate.instant('GENERATEINVOICE.RangDateOptions.Custom'), value: this.rangDateType.custom },
  ]
  public rangeDateOptionSelected: IFilterDropdownOption = {
    label: this.translate.instant('GENERATEINVOICE.RangDateOptions.AllTime'),
    value: this.rangDateType.all,
  }
  static getComponent(): typeof GenerateInvoiceComponent {
    return GenerateInvoiceComponent;
  }
  constructor(
    public dialogRef: MatDialogRef<GenerateInvoiceComponent>,
    private modifyInvoiceItemDialog: ModifyInvoiceItemDialog,
    private newInvoiceDialog: NewInvoiceDialog,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {

    this.generateInvoiceForm = this.formBuilder.group({
      clientId: ["", Validators.compose([Validators.required])],
      tempDate: [""],
      rate: [
        "0",
        // Validators.compose([
        //   Validators.required,
        //   Validators.pattern("^[0-9]+(\\.[0-9]{1,2})?$"),
        // ]),
      ],
    });

    this.generateInvoiceForm.get('clientId')?.valueChanges
      .subscribe(() => this.resetTimeTrackingData());
    this.generateInvoiceForm.get('tempDate')?.valueChanges
      .subscribe(() => this.resetTimeTrackingData());
    this.generateInvoiceForm.get('rate')?.valueChanges
      .subscribe((newRate) => {
        let rate = Number(newRate)
        if (!rate || rate < 0) rate = 0

        this.listInvoiceItem = this.listInvoiceItem.map((item: any) => {
          return {
            ...item,
            rate,
            total: rate * item.qty,
          }
        })
      });
  }
  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
    }
    if (event?.currentPage) {
      this.currentPage = event.currentPage
      this.resetTimeTrackingData()
    }
  }
  closeDialog() {
    this.dialogRef.close();
  }
  ngOnInit(): void {
    this.dropdownOptionService.getDropdownOptionsProjectAndClient({ isOnlyClient: true })
      .then((clientOptions) => {
        this.clientOptions = clientOptions
      })


    const today = new Date()
    const lastMonthDate = new Date(new Date().setMonth(today.getMonth() - 1))
    const currentMonth = getStartAndEndOfMonth(today)
    const lastMonth = getStartAndEndOfMonth(lastMonthDate)
    this.rangDateOptions.forEach(item => {
      if (this.rangDateType.this_month === item.value) {
        const monthName = today.toLocaleString('en-US', { month: 'long' });
        item.label += ` (${monthName})`
        if (!item.metadata) item.metadata = {}
        item.metadata.startDate = new Date(currentMonth.startOfMonth)
        item.metadata.endDate = new Date(currentMonth.endOfMonth)
      }
      if (this.rangDateType.last_month === item.value) {
        const monthName = lastMonthDate.toLocaleString('en-US', { month: 'long' });
        item.label += ` (${monthName})`
        if (!item.metadata) item.metadata = {}
        item.metadata.startDate = new Date(lastMonth.startOfMonth)
        item.metadata.endDate = new Date(lastMonth.endOfMonth)
      }
    })

  }

  get getSelectedRangeDateValue() {
    if (this.rangeDateOptionSelected.value === this.rangDateType.custom) {
      const startDate = this.rangeDateOptionSelected.metadata?.startDate ?? ''
      const endDate = this.rangeDateOptionSelected.metadata?.endDate ?? ''
      return [formatDateFilter(startDate), formatDateFilter(endDate)].join(' - ')
    } else {
      const info = this.rangDateOptions.find(item => item.value === this.rangeDateOptionSelected.value)
      return info?.label
    }
  }
  radioChange($event: MatRadioChange) {
    if ($event.value == 2 && this.listIndexInvoiceSelected.length == 0) {
      setTimeout(() => {
        this.selectedRadio = null
      }, 50);
      this._toastService.showWarning("No selected project", "Please select a projet.")
      return;
    }
    this.selectedRadio = $event.value
    this._handleRadioChange($event.value)

  }
  _handleRadioChange(value: number) {
    this.listProjectId = []
    if (value == 2) {
      this.listIndexInvoiceSelected.forEach(element => {
        this.listProjectId.push(this.listInvoiceItem[element].metadata.timeTracking.projectId)
      }
      );

    }
  }

  resetTimeTrackingData = async () => {
    this.isFetchingProject = true
    const isSelectAllTime = this.rangeDateOptionSelected.value === this.rangDateType.all
    const res: any = await this.generateInvoiceProvider.fetchListProjectByClientAndDate({
      clientId: this.f['clientId']?.value,
      startDate: isSelectAllTime ? null : this.rangeDateOptionSelected.metadata?.startDate,
      endDate: isSelectAllTime ? null : this.rangeDateOptionSelected.metadata?.endDate,
      page: this.currentPage
    })
    this.totalPages = res.totalRecords
    const rate = Number(this.f['rate']?.value ?? 0)
    this.listInvoiceItem = res.data.map((item: any) => {
      const qty = convertHoursToDecimal(item?.endTime ?? '00:00:00')
      return {
        trackingId: item?.id ?? '',
        date: item?.date ?? null,
        description: item?.description ?? '',
        rate: item?.project?.hourlyRate,
        qty,
        inforUser: item.user,
        hourlyRate: item?.project?.hourlyRate,
        taxes: [],
        total: (item?.project?.hourlyRate ?? 0) * qty,
        metadata: {
          hours: item?.endTime ?? '00:00:00',
          timeTracking: item
        },
        dateSelectItem: item.date,
        projectName: item?.project.projectName,
        serviceName: item?.service?.serviceName,
        projectId: item?.project?.id,
        serviceId: item?.service?.id,
        service: item?.service,
      }
    })
    this.listIndexInvoiceSelected = []
    this.isFetchingProject = false
  }

  get f() {
    return this.generateInvoiceForm.controls as Record<string, FormControl>;;
  }

  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  totalAmount() {
    const resultTax = calculateGroupedTaxes(this.listInvoiceItem)
    return resultTax.subtotal + resultTax.grandTotalTax ?? 0
  }

  isCheckedIndex(index: number): boolean {
    return this.listIndexInvoiceSelected.includes(index)
  }

  handleToggleCheckedIndex(index: number) {
    const isChecked = this.isCheckedIndex(index)
    let newListSelected = [...this.listIndexInvoiceSelected]

    if (isChecked) {
      newListSelected = newListSelected.filter(_i => _i !== index)
    } else {
      newListSelected.push(index)
    }
    this.listIndexInvoiceSelected = newListSelected

  }

  handleCheckedAll(isChecked: any) {
    if (isChecked) {
      this.listIndexInvoiceSelected = this.listInvoiceItem.map((_item, index) => index)
    } else {
      this.listIndexInvoiceSelected = []
    }
  }

  handleSelectClient(item: IFilterDropdownOption) {
    this.generateInvoiceForm.controls["clientId"].setValue(item.value)
    this.selectSearchClientElement.handleCloseSearchResult()
  }

  handleSelectDate(item: IFilterDropdownOption) {
    this.rangeDateOptionSelected = item
    this.f['tempDate'].setValue(item.value)
    if (item.value !== this.rangDateType.custom) {
      this.rangeDatePopoverElement.handleHideContent()
    }
  }

  handleSelectCustomDate(type: "startDate" | "endDate", value: any) {
    if (this.rangeDateOptionSelected.value !== this.rangDateType.custom) return

    const metadata = this.rangeDateOptionSelected.metadata ?? {}
    metadata[type] = value

    if (metadata.startDate && metadata.endDate) {
      const isValidDate = isValidDateRange(metadata.startDate, metadata.endDate)
      if (!isValidDate) {
        metadata[type] = null
        this.isShowCustomDateError = true
      } else {
        this.isShowCustomDateError = false
      }
    }

    this.rangeDateOptionSelected.metadata = metadata
    this.f['tempDate'].setValue(this.rangDateType.custom)
  }

  handleModifyInvoiceItem(index: number, item: any) {
    const dialogRef = this.modifyInvoiceItemDialog.open(item);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {
          this.listInvoiceItem[index] = { ...item, ...res }
        }
      })
    });
  }

  handleCancel() {
    this.dialogRef.close();
  }

  handleSubmit() {
    if (this.generateInvoiceForm.invalid) {
      this.markAllControlsAsTouched();
      return
    }

    if (!this.listIndexInvoiceSelected.length) {
      return this._toastService.showWarning("No project selected", "Please select a project to continue creating the invoice.")
    }

    this.dialogRef.close();
    const listItemInvoice = this.listInvoiceItem.filter((_item, index) => this.listIndexInvoiceSelected.includes(index))
    const payload: Record<string, any> = {
      clientId: this.f['clientId'].value,
      itemInvoices: listItemInvoice,
      isGenrate: true,
    }

    if (this.rangeDateOptionSelected.value !== this.rangDateType.all) {
      const startDate = this.rangeDateOptionSelected.metadata?.startDate
      const endDate = this.rangeDateOptionSelected.metadata?.endDate
      const isValidDate = isValidDateRange(startDate, endDate)
      if (!isValidDate) return;

      payload['invoiceDate'] = new Date(startDate)
      payload['dueDate'] = new Date(endDate)
    } else {
      const listDate = this.listInvoiceItem.map((item: any) => new Date(item.date))
      const startDate = listDate.reduce((total, item) => item < total ? item : total, new Date())
      const endDate = listDate.reduce((total, item) => item > total ? item : total, new Date())
      payload['invoiceDate'] = startDate
      payload['dueDate'] = endDate
    }
    this.newInvoiceDialog.open(payload);
  }
}
