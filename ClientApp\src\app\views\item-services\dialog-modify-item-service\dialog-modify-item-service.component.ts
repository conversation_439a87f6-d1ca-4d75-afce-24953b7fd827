import { CompanyTaxService } from 'app/service/company-tax.service';
import { InnoErrorMMessageComponent } from 'app/component/inno-error-message/inno-error-message.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ItemService } from './../../../service/item.service';
import { ServiceService } from './../../../service/service.service';
import { Component, DestroyRef, Inject, inject, OnInit } from '@angular/core';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoFormTextareaComponent } from 'app/component/inno-form-textarea/inno-form-textarea.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { ICreateService } from 'app/dto/interface/service.interface';
import { ItemAndServiceViewEnum } from 'app/enum/item-service.enum';
import { SharedModule } from 'app/module/shared.module';
import { IModifyItemsAndServiceDialog } from 'app/service/dialog/modify-items-and-service.dialog';
import { ToastService } from 'app/service/toast.service';
import { InputTaxComponent } from 'app/views/invoice/dialog/new-invoice/modify-invoice-item/modify-taxes/input-tax/input-tax.component';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-dialog-modify-item-service',
  templateUrl: './dialog-modify-item-service.component.html',
  styleUrls: ['./dialog-modify-item-service.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormInputComponent,
    InnoFormTextareaComponent,
    InputTaxComponent,
    InnoErrorMMessageComponent
  ],
  providers: [LayoutUtilsService]
})
export class DialogModifyItemServiceComponent implements OnInit {
  enumService = ItemAndServiceViewEnum.Service
  public itemForm!: UntypedFormGroup;
  public titlePopup: string = ""
  public taxes = [
    { selected: false, taxeNumber: null, name: '', amount: null },
  ];

  private layoutUtilsService = inject(LayoutUtilsService)
  private toastService = inject(ToastService)
  private serviceService = inject(ServiceService)
  private itemService = inject(ItemService)
  private destroyRef = inject(DestroyRef);
  public translate = inject(TranslateService)
  private companyTaxService = inject(CompanyTaxService)
  private listIndexTaxesSelected: Number[] = []
  private formBuilder = inject(UntypedFormBuilder)
  private originData = ""

  constructor(
    private dialogRef: MatDialogRef<DialogModifyItemServiceComponent>,
    @Inject(MAT_DIALOG_DATA) public data: IModifyItemsAndServiceDialog,
  ) {

    const { mode, serviceInfo } = data

    switch (mode) {
      case ItemAndServiceViewEnum.Item:
        this.titlePopup = serviceInfo?.id ? "ITEMS_SERVICES.EditItem" : "ITEMS_SERVICES.NEW_ITEM_FORM.Title"

        this.itemForm = this.formBuilder.group({
          name: [serviceInfo?.itemName ?? "", Validators.compose([Validators.required])],
          description: [serviceInfo?.description ?? ""],
          rate: [serviceInfo?.rate ?? "", []],
          taxes: [[]],
        });
        this.taxes = serviceInfo?.taxes ?? [];
        break;

      case ItemAndServiceViewEnum.Service:
        this.titlePopup = serviceInfo?.id ? "ITEMS_SERVICES.EditService" : "ITEMS_SERVICES.CreateNewService"
        this.itemForm = this.formBuilder.group({
          name: [serviceInfo?.serviceName ?? "", Validators.compose([Validators.required])],
          description: [serviceInfo?.description ?? ""],
          rate: [serviceInfo?.rate ?? "", []],
          taxes: [[]],
        });
        this.taxes = serviceInfo?.taxes ?? [];
        break;
    }

    this.originData = JSON.stringify({
      name: this.itemForm.get('name')?.value ?? "",
      description: this.itemForm.get('description')?.value ?? "",
      rate: this.itemForm.get('rate')?.value ?? "",
      taxes: this.itemForm.get('taxes')?.value ?? "",
    })
  }
  ngOnInit(): void {
    this.GetAllCompanyTax();
  }
  GetAllCompanyTax() {
    let payload: Parameter = {
      Page: 1,
      PageSize: 50,
      Search: "",

    }
    this.companyTaxService.GetAllCompanyTax(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
      next: (res) => {
        const result = res.data.map((item: any) => {
          const existingTax = this.taxes.find((tax: any) => tax.companyTaxId === item.id);
          return {
            ...item,
            companyTaxId: item.id,
            selected: !!existingTax
          };
        });

        this.taxes = result;
        this.listIndexTaxesSelected = this.taxes
          .map((item, index) => item.selected ? index : -1)
          .filter(index => index !== -1);
      }
    });
  }
  handleClose() {
    this.dialogRef.close();
  }
  static getComponent(): typeof DialogModifyItemServiceComponent {
    return DialogModifyItemServiceComponent;
  }

  get f() {
    return this.itemForm.controls as Record<string, FormControl>;;
  }

  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  Delete(index: number) {
    this.taxes.splice(index, 1)
    const indexExist = this.listIndexTaxesSelected.indexOf(index)
    if (indexExist !== -1) {
      this.listIndexTaxesSelected.splice(indexExist, 1)
    }
  }

  addTax() {
    this.taxes.push({ selected: false, taxeNumber: null, name: '', amount: null });
  }

  handleSelectedTax(value: boolean, index: number) {
    const updateTaxesForm = () => {
      const taxes = this.listIndexTaxesSelected.map((_i: number) => this.taxes[_i])
      this.itemForm.get('taxes')?.setValue(taxes)
    }

    const indexExist = this.listIndexTaxesSelected.indexOf(index)
    if (indexExist !== -1) {
      this.taxes[indexExist].selected = false
      this.listIndexTaxesSelected.splice(indexExist, 1)
      updateTaxesForm()
      return;
    }

    if (this.listIndexTaxesSelected.length === 2) {
      const indexUnselected = this.listIndexTaxesSelected.shift() as number
      this.taxes[indexUnselected].selected = false
    }

    this.listIndexTaxesSelected.push(index)
    this.taxes.forEach((item: any, index: number) => {
      item.selected = this.listIndexTaxesSelected.includes(index);
    });
    updateTaxesForm();
  }

  handleCancel() {
    const currentData = JSON.stringify({
      name: this.itemForm.get('name')?.value ?? "",
      description: this.itemForm.get('description')?.value ?? "",
      rate: this.itemForm.get('rate')?.value ?? "",
      taxes: this.itemForm.get('taxes')?.value ?? "",
    })

    const isChanged = currentData !== this.originData
    if (!isChanged) {
      this.dialogRef.close()
      return;
    }

    this.layoutUtilsService.alertConfirm({
      title: this.translate.instant('TOAST.DiscardChanges'),
      description: this.translate.instant('TOAST.DescriptionChanges'),
    }).then((result) => {
      if (result) {
        this.dialogRef.close()
      }
    })
  }

  handleSubmit() {
    if (this.itemForm.invalid) {
      this.markAllControlsAsTouched();
      return;
    }

    // Check for duplicate tax names across all taxes
    const taxNames = this.taxes
      .map(tax => tax.name?.toLowerCase().trim())
      .filter(name => name); // Filter out empty/null names

    const duplicateTaxes = taxNames.filter((name, index) =>
      taxNames.indexOf(name) !== index
    );

    if (duplicateTaxes.length > 0) {
      this.toastService.showWarning(
        this.translate.instant("TOAST.Warning"),
        `${this.translate.instant("TOAST.DuplicateTax")} ${duplicateTaxes.join(', ')}. ${this.translate.instant("TOAST.TaxName")}`
      );
      return;
    }

    // Get selected taxes for payload
    const selectedTaxes = this.taxes.filter(x => x.selected === true);

    switch (this.data.mode) {
      case ItemAndServiceViewEnum.Item:
        const payloadItem = {
          itemName: this.f['name'].value,
          description: this.f['description'].value,
          rate: this.f['rate'].value,
          taxes: selectedTaxes,
          id: this.data?.serviceInfo?.id
        }
        if (this.data?.serviceInfo?.id) {
          this.itemService.Update(payloadItem).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
            if (res) {
              this.toastService.showSuccess(this.translate.instant('TOAST.Update'), this.translate.instant('TOAST.UpdateItem'));
              this.dialogRef.close({ viewRefresh: ItemAndServiceViewEnum.Item })

            }
          });
        } else {
          this.itemService.CreateItem(payloadItem).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
            next: (res) => {
              if (res) {
                this.toastService.showSuccess(this.translate.instant('TOAST.Save'), this.translate.instant('TOAST.Success'));
                this.dialogRef.close({ viewRefresh: ItemAndServiceViewEnum.Item })
              }
            }
          });
        }
        break;

      case ItemAndServiceViewEnum.Service:
        const payload: ICreateService = {
          serviceName: this.itemForm.get('name')?.value ?? "",
          description: this.itemForm.get('description')?.value ?? "",
          rate: this.itemForm.get('rate')?.value ?? "",
          taxes: selectedTaxes,
          id: this.data?.serviceInfo?.id
        }

        if (this.data?.serviceInfo?.id) {
          this.serviceService.Update(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
            if (!res) return
            this.toastService.showSuccess(this.translate.instant('TOAST.Success'), this.translate.instant('TOAST.UpdateService'))
            this.dialogRef.close({ viewRefresh: ItemAndServiceViewEnum.Service })
          })
        } else {
          this.serviceService.CreateService(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
            if (!res) return
            this.toastService.showSuccess(this.translate.instant('TOAST.Success'), this.translate.instant('TOAST.CreateService'))
            this.dialogRef.close({ viewRefresh: ItemAndServiceViewEnum.Service })
          })
          if (this.data.isShowProject == false) {
            this.dialogRef.close(payload)
          }
        }
        break;
    }
  }
}
