﻿using InnoBook.Entities;

namespace InnoBook.DTO.Invoice
{
    public class InvoiceDetailDTO
    {
        public Guid Id { get; set; }
        public Guid? ContractorId { get; set; }
        public string? InvoiceNumber { get; set; }
        public Guid ClientId { get; set; }
        public Guid? ProjectId { get; set; }
        public bool IsEstimate { get; set; } = false;
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public DateTime CreatedAt { get; set; }
        public string? Reference { get; set; }
        public string? Notes { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal Rate { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public string? Description { get; set; }
        public bool isPaid { get; set; } = false;
        public int Status { get; set; }
        public int Position { get; set; }
        public ClientDetailInvoiceDTO? Client { get; set; }
        public CompanyDetailInvoiceDTO? Company { get; set; }
        public List<ItemInvoicesDetailInvoiceDTO>? ItemInvoices { get; set; }
    }
}
