import { SortGird } from 'app/dto/interface/SortGird.interface';
import { DecimalPipe } from './../../../../../pipes/decimal.pipe';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, inject, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { convertHoursToDecimal } from 'app/helpers/common.helper';
import { SharedModule } from 'app/module/shared.module';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { InvoiceItem } from '../../../../../dto/interface/invoiceItem.interface';
import { MatTooltip } from '@angular/material/tooltip';
import { AvatarModule } from 'ngx-avatars';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TimeTrackingQueryParam } from 'app/dto/interface/queryParameter.interface';
import { TimetrackingService } from 'app/service/timetracking.service';
import { Pager, PagerDropDown, PagerModule } from '@syncfusion/ej2-angular-grids';
import { calculateGroupedTaxes, calculateTimeTrackingAmount } from 'app/utils/invoice.helper';
import { TranslateService } from '@ngx-translate/core';
Pager.Inject(PagerDropDown);
interface IAddTrackingByClient {
  client: Record<string, any>, // id, name
  project: Record<string, any>, // id, name
  listIdTimeTrackingSelected: InvoiceItem[]
}

@Component({
  selector: 'app-select-time-tracking',
  templateUrl: './select-time-tracking.component.html',
  styleUrls: ['./select-time-tracking.component.scss'],
  standalone: true,
  imports: [
    MatTooltip,
    AvatarModule,
    SharedModule,
    PagerModule,
    FormatNumberPipe,
    DecimalPipe,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormCheckboxComponent,
    InnoSpinomponent,
    InnoEmptyDataComponent,
  ]
})
export class SelectTimeTrackingComponent implements OnInit {
  public sort: SortGird
  today = new Date();
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 20
  public title: string = ''
  public clientName: string = ''
  public projectName: string = ''
  public listInvoiceItem: InvoiceItem[] = []
  public listIndexInvoiceSelected: number[] = []
  public isFetchingProject: boolean = false
  public calculateTotalInvoiceItem = calculateTimeTrackingAmount;
  sortDirection: 'Ascending' | 'Descending' = 'Ascending';
  sortColumn: string = ''

  private destroyRef = inject(DestroyRef);
  private timetrackingService = inject(TimetrackingService)
  public _storeService = inject(StoreService)
  public translate = inject(TranslateService)
  static getComponent(): typeof SelectTimeTrackingComponent {
    return SelectTimeTrackingComponent;
  }

  constructor(
    public dialogRef: MatDialogRef<SelectTimeTrackingComponent>,
    @Inject(MAT_DIALOG_DATA) public data?: IAddTrackingByClient
  ) {
    this.clientName = data?.client?.['clientName'] ?? ''
    this.projectName = data?.project?.['projectName'] ?? ''
    this.title = `Select time tracking by ${this.clientName}${this.projectName != '' ? ' / ' + this.projectName : ''}`;
  }
  handleClose() {
    this.dialogRef.close();
  }
  handleSort() {
    this.currentPage = 1; // Reset to the first page when sorting
    const page = this.currentPage
    const textSearch = ""
    const isGetAll = false
    this.GetAllTimTrackingUnBillTime({ page, textSearch, isGetAll });
  }

  sortDates(column: string) {
    if (this.sortColumn === column) {
      this.sortDirection = this.sortDirection === 'Ascending' ? 'Descending' : 'Ascending';
      this.sort = {
        columnName: this.sortColumn,
        direction: this.sortDirection
      }
    } else {
      this.sortColumn = column;
      this.sortDirection = 'Ascending';
      this.sort = {
        columnName: this.sortColumn,
        direction: this.sortDirection
      }
    }
    this.handleSort()
  }
  GetAllTimTrackingUnBillTime(args?: { page?: number, textSearch?: string, isGetAll?: boolean }) {
    const clientId = this.data?.client?.['id']
    const project: any = this.data?.project
    if (!clientId) return;

    const query: TimeTrackingQueryParam = {
      Page: args?.isGetAll ? 0 : (args?.page ?? 1),
      Search: args?.textSearch ?? '',
      PageSize: this.pageSizesDefault,
      clientId: clientId,
      loggedBy: 'all',
      columnName: this?.sort?.columnName,
      direction: this?.sort?.direction,
      projectId: project?.id,
      isUnBill: true,
    };
    this.isFetchingProject = true
    this.timetrackingService.GetAllTimeTracking(query)
      .pipe(takeUntilDestroyed(this.destroyRef)).subscribe((listResponse: any) => {
        if (listResponse) {
          this.totalPages = listResponse.totalRecords
          const listIdTimeTrackingExist = this.data?.listIdTimeTrackingSelected ?? []
          listResponse = listResponse?.data ? listResponse?.data?.filter((item: any) => !listIdTimeTrackingExist.includes(item.id)) ?? [] : listResponse?.filter((item: any) => !listIdTimeTrackingExist.includes(item.id)) ?? []

          this.listInvoiceItem = listResponse.map((item: any) => {
            const qty = convertHoursToDecimal(item?.endTime ?? '00:00:00')
            return {
              trackingId: item?.id ?? '',
              date: item?.date ?? this.today,
              description: item?.description ?? '',
              rate: item?.project?.hourlyRate,
              qty,
              taxes: [],
              total: calculateTimeTrackingAmount(item?.endTime, item?.project?.hourlyRate),
              metadata: {
                hours: item?.endTime ?? '00:00:00',
                timeTracking: item
              },
              dateSelectItem: item.date,
              projectName: item?.project.projectName,
              serviceName: item?.service?.serviceName,
              projectId: item?.project?.id,
              serviceId: item?.service?.id,
              inforUser: item?.user,
              service: item?.service,
            }
          })
          this.isFetchingProject = false
          if (args.isGetAll) {
            this.pageSizesDefault = this.totalPages
            this.listIndexInvoiceSelected = this.listInvoiceItem.map((_item, index) => index)
          }
        }
      })

  }
  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      this.GetAllTimTrackingUnBillTime({ page: this.currentPage, textSearch: "", isGetAll: false });
    }
    if (event?.currentPage) {
      this.currentPage = event.currentPage
      this.GetAllTimTrackingUnBillTime({ page: this.currentPage, textSearch: "", isGetAll: false });
    }
  }

  ngOnInit(): void {
    this.sortDirection = "Ascending"
    this.sortColumn = 'date'
    this.sort = {
      columnName: this.sortColumn,
      direction: this.sortDirection
    }
    this.handleSort()

  }

  handleCheckedAll(isChecked: any) {
    if (isChecked) {
      const page = this.currentPage
      const textSearch = ""
      const isGetAll = true
      this.GetAllTimTrackingUnBillTime({ page, textSearch, isGetAll });
    } else {
      this.listIndexInvoiceSelected = []
    }
  }

  isCheckedIndex(index: number): boolean {
    return this.listIndexInvoiceSelected.includes(index)
  }

  handleToggleCheckedIndex(index: number) {
    const isChecked = this.isCheckedIndex(index)
    let newListSelected = [...this.listIndexInvoiceSelected]

    if (isChecked) {
      newListSelected = newListSelected.filter(_i => _i !== index)
    } else {
      newListSelected.push(index)
    }
    this.listIndexInvoiceSelected = newListSelected
  }

  totalAmount() {
    const resultTax = calculateGroupedTaxes(this.listInvoiceItem)
    return resultTax.subtotal + resultTax.grandTotalTax ?? 0
  }

  handleCancel() {
    this.dialogRef.close();
  }

  handleSubmit() {
    //const listInvoiceItemSelected = this.listInvoiceItem.filter((_item, index) => this.listIndexInvoiceSelected.includes(index))
    const listInvoiceItemSelected = this.listIndexInvoiceSelected.map(index => this.listInvoiceItem[index]);
    this.dialogRef.close(listInvoiceItemSelected.map(({ service, ...rest }) => rest))
  }
}
