import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';

@Component({
  selector: 'app-add-item-invoice',
  standalone: true,
  imports: [SharedModule, MatMenuModule],
  templateUrl: './add-item-invoice.component.html',
  styleUrl: './add-item-invoice.component.scss'
})
export class AddItemInvoiceComponent implements OnInit {
  @Input() totalHours!: number
  @Input() _id!: number
  @Input() dataEdit!: any
  @Input() aplyAll!: boolean
  @Output() calculate = new EventEmitter<any>();
  nameTax: string = ''
  listTax: any[] = []
  priceTax: number = 0
  sumtax: number = 0;
  ApplyAll: boolean = false;
  qty!: number;
  note!: string;
  total!: number;
  rate!: number;
  @ViewChild('actiontMenuTrigger') actiontMenuTrigger!: MatMenuTrigger;
  _formatTotal(total: number) {
    return Math.floor(total * 1000) / 1000
  }
  ngOnChanges(changes: SimpleChanges) {
    if (changes['totalHours'] && changes['totalHours'].currentValue) {
      this.qty = changes['totalHours'].currentValue
    }
    if (changes['aplyAll'] && changes['aplyAll'].currentValue) {
      this.ApplyAll = changes['aplyAll'].currentValue

    }
    if (changes['dataEdit'] && changes['dataEdit'].currentValue) {
      this.dataEdit = changes['dataEdit'].currentValue
      this.qty = this.dataEdit.qty
      this.note = this.dataEdit.description
      this.rate = this.dataEdit.rate
      this.total = this.dataEdit.total
      if (this.dataEdit.taxes.length > 0) {
        this.handleEmitTax(this.dataEdit.taxes)
      }
    }


  }
  ngOnInit(): void {
  }

  CloseTax($event: boolean) {
    this.actiontMenuTrigger.closeMenu();
  }
  handleEmitTax(data: any) {
    this.listTax = data;
    this.nameTax = ""
    data.forEach((element: any) => {

      if (element.selected) {
        this.nameTax = this.nameTax + "," + element.name
      }
    });
    this.nameTax = this.nameTax.trimStart().replace(/^,/, '+');
    this.total = this._formatTotal(this.rate * this.qty + (this.rate * this.qty ))
    if (this.total) {
      this.priceTax = Math.floor((this.rate * this.qty) * (this.sumtax / 100) * 1000) / 1000
      this.calculate.emit({ id: this._id, total: this.total, description: this.note, qty: this.qty, rate: this.rate, taxes: this.listTax })
    }

  }
  stopPropagation(event: any) {
    event.stopPropagation();
  }
  NoteChanged(value: string) {
    this.note = value;
    if (!isNaN(this.total)) {
      this.calculate.emit({ id: this._id, total: this.total, description: this.note, qty: this.qty, rate: this.rate, taxes: this.listTax })
    }
  }

  rateChanged(value: number) {
    this.rate = value
    if (this.qty) {
      this.total = this._formatTotal(value * this.qty + (value * this.qty) * (this.sumtax / 100));
    }
    if (!isNaN(this.total)) {
      this.calculate.emit({ id: this._id, total: this.total, description: this.note, qty: this.qty, rate: this.rate, taxes: this.listTax })
    }
  }

  rateChangedQty(value: number) {
    this.qty = value
    if (this.rate) {
      this.total = this._formatTotal(value * this.rate + (value * this.rate) * (this.sumtax / 100));
    }
    if (!isNaN(this.total)) {
      this.calculate.emit({ id: this._id, total: this.total, description: this.note, qty: this.qty, rate: this.rate, taxes: this.listTax })
    }
  }
}
