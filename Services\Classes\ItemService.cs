﻿using InnoBook.DTO;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Item;
using InnoBook.Entities;
using InnoBook.Extension;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Linq;

namespace InnoBook.Services.Classes
{
    public class ItemService(InnoLogicielContext context) : IItemService
    {
        private ItemDTO MapToItemDTO(Item item, List<TaxItem> taxes)
        {
            return new ItemDTO
            {
                Id = item.Id,
                ItemName = item.ItemName,
                Description = item.Description,
                Rate = item.Rate,
                CreatedBy = item.CreatedBy,
                CreatedAt = item.CreatedAt,
                isActive = item.isActive,
                Taxes = taxes
            };
        }


        public async Task<ItemDTO> CreatedItem(ItemDTO dto, Guid companyId, string userId)
        {
            using (var transaction = context.Database.BeginTransaction())
            {
                try
                {
                    // Handle add or update company taxes
                    var taxes = await HandleAddOrUpdateTaxes(dto.Taxes, companyId, userId);

                    var newItem = new Item
                    {
                        Id = Guid.NewGuid(),
                        ItemName = dto.ItemName,
                        Description = dto.Description,
                        Rate = dto.Rate,
                        CreatedBy = userId,
                        CreatedAt = DateTime.UtcNow,
                        CompanyId = companyId,
                        isActive = true,
                        Taxes = taxes
                    };

                    context.Items.Add(newItem);
                    await context.SaveChangesAsync();
                    await context.Database.CommitTransactionAsync();
                    // On remplit manuellement le DTO de retour
                    var result = MapToItemDTO(newItem, dto.Taxes);
                    return result;
                }
                catch (Exception)
                {
                    await context.Database.RollbackTransactionAsync();
                    throw;
                }
            }
        }

        public async Task<bool> DeleteItem(List<Guid?> listItem, string userId)
        {
            try
            {
                var listItemDetail = await context.Items.Where(x => listItem.Contains(x.Id)).ToListAsync();
                listItemDetail.ForEach(item =>
                {
                    item.isActive = false;
                    item.UpdatedAt = DateTime.UtcNow;
                    item.UpdatedBy = userId;
                });
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<PaginatedResponse<ItemDTO>> GetAllItem(PaginatedRequest query, string CompanyId)
        {
            var data = context.Items.Where(c => c.CompanyId.ToString() == CompanyId && c.isActive == true).Include(o => o.Taxes).ThenInclude(x => x.CompanyTax)
                                    .OrderByDescending(x => x.CreatedAt)
                                     .Select(x => new ItemDTO
                                     {
                                         Id = x.Id,
                                         ItemName = x.ItemName,
                                         Description = x.Description,
                                         Rate = x.Rate,
                                         CreatedAt = x.CreatedAt,
                                         CreatedBy = x.CreatedBy,
                                         isArchive = x.isArchive,
                                         isActive = x.isActive,
                                         Taxes = x.Taxes.Select(t => new TaxItem
                                         {
                                             Id = t.Id,
                                             Amount = t.CompanyTax.Amount.Value,
                                             Name = t.CompanyTax.Name,
                                             TaxeNumber = t.CompanyTax.Name,
                                             CompanyTaxId = t.CompanyTaxId

                                         }).ToList()
                                     })
                                   .AsQueryable();
            if (query.Search != null && !string.IsNullOrEmpty(query.Search))
            {
                data = data.Where(c => c.ItemName.ToLower().Contains(query.Search.ToLower()));
            }
            if (query.Filter != null && query.Filter.ContainsKey("Sort") && query.Filter["Sort"] != null)
            {
                var sort = JsonConvert.DeserializeObject<SortDTO>(query.Filter["Sort"])!;

                data = SortExtensions.DynamicSort(data, sort.columnName, sort.direction == "Ascending" ? false : true);

            }
            var totalRecords = await data.CountAsync();

            var result = await data
            .Skip((query.Page - 1) * query.PageSize)
            .Take(query.PageSize)
            .ToListAsync();

            var userIds = result.Where(c => !string.IsNullOrEmpty(c.CreatedBy) && Guid.TryParse(c.CreatedBy, out Guid id))
                                .Select(c => Guid.Parse(c.CreatedBy)).ToHashSet();
            // Just connect to database 1 time
            var users = await context.Users.Where(c => userIds.Contains(c.Id))
                                           .Select(x => new InforUserDTO
                                           {
                                               Id = x.Id,
                                               FirstName = x.FirstName,
                                               LastName = x.LastName,
                                               Email = x.Email
                                           }).ToListAsync();
            result.ForEach(item =>
            {
                item.inforUser = users.FirstOrDefault(c => c.Id.ToString() == item.CreatedBy);
            });
            return new PaginatedResponse<ItemDTO>
            {
                Data = result,
                Page = query.Page,
                TotalPage = (totalRecords + query.PageSize - 1) / query.PageSize,
                PageSize = query.PageSize,
                TotalRecords = totalRecords
            };
        }

        public async Task<ItemDTO> GetItemById(string id)
        {
            var data = await context.Items
                   .Include(o => o.Taxes)
                   .ThenInclude(x => x.CompanyTax)
                   .FirstOrDefaultAsync(c => c.Id.ToString() == id);

            if (data == null)
                return null;

            // On mappe les taxes
            var taxDtos = data.Taxes.Select(t => new TaxItem
            {
                Id = t.Id,
                Amount = t.CompanyTax.Amount.Value,
                Name = t.CompanyTax.Name,
                TaxeNumber = t.CompanyTax.Name,
                CompanyTaxId = t.CompanyTaxId
            }).ToList();

            return MapToItemDTO(data, taxDtos);
        }

        public async Task<bool> Update(ItemDTO model, Guid CompanyId, string UserId)
        {
            using (var transaction = context.Database.BeginTransaction())
            {
                try
                {
                    var data = await context.Items.FirstOrDefaultAsync(x => x.Id == model.Id);
                    if (data == null) return false;

                    // Handle add or update company taxes
                    var addedTaxes = await HandleAddOrUpdateTaxes(model.Taxes, CompanyId, UserId);

                    // Obtenir toutes les taxes liées à l'item
                    var existingTaxes = await context.Taxs.Where(x => x.ItemId == model.Id).ToListAsync();

                    // Supprimer les taxes qui ne sont plus dans le modèle
                    var modelTaxIds = model.Taxes.Select(t => t.CompanyTaxId).ToHashSet();
                    var taxesToRemove = existingTaxes.Where(t => !modelTaxIds.Contains(t.CompanyTaxId)).ToList();
                    if (taxesToRemove.Any())
                    {
                        context.Taxs.RemoveRange(taxesToRemove);
                    }

                    // Ajouter ou mettre à jour les taxes
                    addedTaxes.Where(c => !existingTaxes.Select(c => c.CompanyTaxId).Contains(c.CompanyTaxId))
                              .ToList().ForEach(c =>
                              {
                                  c.ItemId = data.Id;
                              });
                    context.AddRange(addedTaxes);


                    existingTaxes.Where(t => modelTaxIds.Contains(t.CompanyTaxId)).ToList().ForEach(t =>
                    {
                        t.UpdatedBy = UserId;
                        t.UpdatedAt = DateTime.UtcNow;
                    });

                    // Mise à jour de l'item
                    data.CompanyId = CompanyId;
                    data.ItemName = model.ItemName;
                    data.Description = model.Description;
                    data.Rate = model.Rate;
                    data.UpdatedBy = UserId;
                    data.UpdatedAt = DateTime.UtcNow;

                    await context.SaveChangesAsync();
                    await context.Database.CommitTransactionAsync();
                    return true;
                }
                catch( Exception ex)
                {
                    await context.Database.RollbackTransactionAsync();
                    return false;
                }
            }
        }


        public async Task<bool> UpdateArchive(List<Guid?> listItem)
        {
            try
            {

                var listItemDetail = context.Items.FirstOrDefault(x => listItem.Contains(x.Id));
                listItemDetail.isArchive = true;
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        private async Task<List<Tax>> HandleAddOrUpdateTaxes(List<TaxItem>? taxes, Guid companyId, string userId)
        {
            // Handle for case user add new companyTax
            var addedCompanyTaxes = taxes.Where(c => c.CompanyTaxId == Guid.Empty)
                                      .Select(t => new CompanyTax()
                                      {
                                          Amount = t.Amount,
                                          TaxeNumber = t.TaxeNumber,
                                          CompanyId = companyId,
                                          CreatedBy = userId,
                                          Name = t.Name,
                                      }).ToList();
            context.CompanyTax.AddRange(addedCompanyTaxes);
            // Handle for update taxes
            var updatedCompanyTaxes = taxes.Where(c => c.CompanyTaxId != Guid.Empty)
                                               .Select(t => new CompanyTax()
                                               {
                                                   Id = t.CompanyTaxId,
                                                   Amount = t.Amount,
                                                   TaxeNumber = t.TaxeNumber,
                                                   CompanyId = companyId,
                                                   CreatedBy = userId,
                                                   Name = t.Name,
                                               }).ToList();
            context.CompanyTax.UpdateRange(updatedCompanyTaxes);
            await context.SaveChangesAsync();

            addedCompanyTaxes.AddRange(updatedCompanyTaxes);
            // Convert added CompanyTax to Tax, to add taxes into item
            return addedCompanyTaxes.Select(tax => new Tax
            {
                Id = Guid.NewGuid(),
                CompanyTaxId = tax.Id,
                CreatedBy = userId
            }).ToList();
        }
    }
}
