﻿using InnoBook.Attributes;
using InnoBook.Common;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Estimate;
using InnoBook.DTO.Invoice;
using InnoBook.DTO.Mail;
using InnoBook.Entities;
using InnoBook.Request.Invoice;
using InnoBook.Services;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class InvoicesController(IInvoiceService invoiceService, IMailService _mail, IConfiguration config,
        IHttpContextAccessor httpContextAccessor) : BaseController(httpContextAccessor)
    {
        [RequiredRoles(UserBusinessRole.All)]
        [HttpGet()]
        [Route("GetRevenueChart")]
        public async Task<IActionResult> RevenueChart(int year)
        {
            var data = await invoiceService.RevenueChart(IdCompany, year);
            return Ok(data);
        }
        [RequiredRoles(UserBusinessRole.All)]
        [HttpGet()]
        [Route("GraphicsChart")]
        public async Task<IActionResult> GraphicsChart(int year)
        {
            var data = await invoiceService.GraphicsChart(IdCompany, year);
            return Ok(data);
        }

        
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("UploadFile")]
        public async Task<IActionResult> UploadFile(IFormFile formFile)
        {
            if (formFile == null || formFile.Length == 0)
            {
                return BadRequest("File is missing or empty.");
            }
            string companyId = Request.Form["companyId"];
            var dt = await DigitalOcean.UploadFileAsync(formFile, config, companyId);
            return Ok(dt);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("DeleteInvoice")]
        public async Task<IActionResult> DeleteInvoice(List<Guid?> listInvoice, bool isActive)
        {
            var result = await invoiceService.DeleteInvoice(listInvoice, isActive);
            return Ok(result);

        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("MarkAsPaid")]
        public async Task<IActionResult> MarkAsPaid(string Id)
        {
            var result = await invoiceService.MarkAsPaid(Id);
            return Ok(result);

        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("MarkAsSent")]
        public async Task<IActionResult> MarkAsSent(string Id)
        {
            var result = await invoiceService.MarkAsSent(Id);
            return Ok(result);

        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("ConvertToInvoice")]
        public async Task<ActionResult> ConvertToInvoice(Guid invoiceId)
        {
            try
            {
                var result = await invoiceService.ConvertToInvoice(invoiceId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("UpdateArchive")]
        public async Task<ActionResult> UpdateArchive(Guid invoiceId, bool isArchive)
        {
            try
            {
                var result = await invoiceService.UpdateArchive(invoiceId, isArchive);
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }

        [AllowAnonymous]
        [HttpGet("GetInvoiceByIdLink")]
        public async Task<IActionResult> GetInvoiceByIdLink(Guid InvoiceId)
        {
            var result = await invoiceService.GetInvoiceById(InvoiceId,IdCompany);
            return Ok(result);

        }
       
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("GetFileURL")]
        public ActionResult GetFileURL(string nameFile)
        {
            DigitalOcean digitalOcean = new DigitalOcean();
            var url = digitalOcean.GetFileURL(config, IdCompany, nameFile);
            return Ok(url);
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("GetInvoiceById")]
        public async Task<IActionResult> GetInvoiceById(Guid InvoiceId)
        {
            var result = await invoiceService.GetInvoiceById(InvoiceId,IdCompany);
            return Ok(result);

        }

        [RequiredRoles(UserBusinessRole.All)]
        [HttpGet("PrintInvoice")]
        public async Task<IActionResult> PrintInvoice(Guid InvoiceId)
        {

            // MemoryStream pdfStream = _invoiceService.GetInvoicePDF();
            var pdfStream = await invoiceService.PrintInvoice(InvoiceId, Guid.Parse(IdCompany), Guid.Parse(IdUser));
            return File(pdfStream, "application/pdf", "Invoice.pdf");

        }

        
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("CalculationInvoice")]
        public ActionResult CalculationInvoice(int status)
        {
            string role = HttpContext.GetBusinessRole();
            var cliens = invoiceService.CalculationInvoice(status, role, Guid.Parse(IdUser), IdCompany);
            return Ok(cliens);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("CalculationInvoiceSendToMe")]
        public async Task<ActionResult> CalculationInvoiceSendToMe(int status)
        {
            var cliens = await invoiceService.CalculationInvoiceSendToMe(status, Guid.Parse(IdUser), IdCompany);
            return Ok(cliens);
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant)]
        [HttpGet("CalculationEstimateSendToMe")]
        public async Task<ActionResult> CalculationEstimateSendToMe(int status)
        {
            var cliens = await invoiceService.CalculationEstimateSendToMe(status, Guid.Parse(IdUser), IdCompany);
            return Ok(cliens);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant)]
        [HttpGet("CalculationEstimate")]
        public async Task<ActionResult> CalculationEstimate(int status)
        {
            var cliens = await invoiceService.CalculationEstimate(status, Guid.Parse(IdUser), IdCompany);
            return Ok(cliens);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("SendMailInvoice")]
        public IActionResult SendMailInvoice(RequestInvoiceSendMail invoiceEmail)
        {
            try
            {
                var url = config.GetSection("ActivateAccountUrl").Value;
                var urlInvoice = config.GetSection("Invoice").Value;

                string templateDirectory = "EmailTemplates";
                var templateService = new TemplateService(templateDirectory);

                // Load and process template
                string templateContent = templateService.LoadTemplate("InvoiceEmail.html");
                var placeholders = new Dictionary<string, string>
                {

                   { "url",url },
                      { "BusinessName",invoiceEmail.BusinessName },
                       { "InvoiceNumber",invoiceEmail.InvoiceNumber },
                          { "PayAmount",invoiceEmail.PayAmount },
                           { "Date",invoiceEmail.Date },
                      { "urlinvoice",urlInvoice+"/invoices" }


                };
                string emailBody = templateService.ProcessTemplate(templateContent, placeholders);
                foreach (var item in invoiceEmail.ListEmail)
                {
                    var mail = new MailInfoAndContent
                    {
                        To = item,
                        Subject = invoiceEmail.BusinessName + " sent you an invoice " + invoiceEmail.InvoiceNumber,
                        Body = emailBody
                    };
                    _mail.SendMail(mail);
                }

                return Ok(invoiceEmail);
            }
            catch (Exception ex)
            {
                return new OkObjectResult(ex);
            }
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager)]
        [HttpGet("GetAllEstimate")]
        public async Task<ActionResult<PaginatedResponse<IActionResult>>> GetAllEstimate([FromQuery] GetEstimateRequestParam query)
        {
            query.UserId = IdUser;
            query.Role = HttpContext.GetBusinessRole();
            var invoice = await invoiceService.GetAllEstimate(query, IdCompany, IdUser);
            return Ok(invoice);
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("GetAllInvoice")]
        public async Task<ActionResult<PaginatedResponse<IActionResult>>> GetAllInvoice([FromQuery] GetInvoiceRequestParam query)
        {
            query.UserId = IdUser;
            query.Role = HttpContext.GetBusinessRole();
            var invoice = await invoiceService.GetAllInvoice(query, IdCompany, IdUser);
            return Ok(invoice);
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("GetAllInvoiceSendToMe")]
        public async Task<ActionResult<PaginatedResponse<IActionResult>>> GetAllInvoiceSendToMe([FromQuery] GetInvoiceRequestParam query)
        {
            query.Role = HttpContext.GetBusinessRole();
            var invoice = await invoiceService.GetAllInvoiceSendToMe(query, IdCompany, IdUser);
            return Ok(invoice);
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant)]
        [HttpPost("GetAllEstimateSendToMe")]
        public async Task<ActionResult<PaginatedResponse<IActionResult>>> GetAllEstimateSendToMe([FromBody] PaginatedRequest query)
        {
            var invoice = await invoiceService.GetAllEstimateSendToMe(query, IdCompany, IdUser);
            return Ok(invoice);
        }


        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("CountInvoiceByContractor")]
        public async Task<ActionResult> CountInvoiceByContractor()
        {
            var data = await invoiceService.CountInvoiceByContractor(Guid.Parse(IdCompany));
            return Ok(data);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("CountInvoiceByCompany")]
        public async Task<ActionResult> CountInvoiceByCompany()
        {
            var data = await invoiceService.CountInvoiceByCompany(Guid.Parse(IdCompany));
            return Ok(data);
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Accountant)]
        [HttpGet("CountEstimate")]
        public async Task<ActionResult> CountEstimate()
        {
            var data = await invoiceService.CountEstimate(Guid.Parse(IdCompany));
            return Ok(data);
        }


        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("UpdateInvoice")]
        public async Task<ActionResult> UpdateInvoice(RequestInvoice invoice)
        {
            invoice.CompanyId = Guid.Parse(IdCompany);
            try
            {
                var result = await invoiceService.Update(invoice, Guid.Parse(IdUser), Guid.Parse(IdCompany));
                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(ex);
            }

        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("CreatedInvoiceSend")]
        public async Task<ActionResult> CreatedInvoiceSend(RequestInvoiceSend invoiceSend)
        {
            var data = await invoiceService.CreatedInvoiceSend(invoiceSend, IdUser, Guid.Parse(IdCompany));
            return Ok(data);
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpPost("ChangePosition")]
        public async Task<ActionResult> ChangePosition(RequestChangePosition requestChangePosition)
        {
            var data = await invoiceService.ChangePosition(requestChangePosition);
            return Ok(data);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("ResetPosition")]
        public async Task<ActionResult> ResetPosition()
        {
            var data = await invoiceService.ResetPosition();
            return Ok(data);
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Accountant)]
        [HttpGet("ResetPositionItemInvoice")]
        public async Task<ActionResult> ResetPositionItemInvoice()
        {
            var data = await invoiceService.ResetPositionItemInvoice();
            return Ok(data);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Contractor, UserBusinessRole.Contractor)]
        [HttpPost("CreatedInvoice")]
        public async Task<ActionResult> CreatedInvoice(RequestInvoice invoice)
        {
            invoice.CompanyId = Guid.Parse(IdCompany);
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }
            var data = await invoiceService.CreatedInvoice(invoice, IdUser, Guid.Parse(IdCompany));

            return Ok(data);
        }
    }
}
