import { CdnService } from './../../../service/cdn.service';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { SortGird } from 'app/dto/interface/SortGird.interface';
import { ExpensesService } from 'app/service/expenses.service';
import { StoreService } from 'app/service/store.service';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { SharedModule } from 'app/module/shared.module';
import { Component, DestroyRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { concatMap, debounceTime, Subject, Subscription } from 'rxjs';
import { ActionEventArgs, GridAllModule, GridComponent, PagerModule, SelectionSettingsModel, SortSettingsModel } from '@syncfusion/ej2-angular-grids';
import { CommonModule } from '@angular/common';
import { MatMenuModule } from '@angular/material/menu';
import { InnoInputSearchComponent } from 'app/component/inno-input-search/inno-input-search.component';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { GetExpenseQueryParam, GetUploadedExpensesQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Expenses } from 'app/dto/interface/expenses.interface';
import { InvoiceService } from 'app/service/invoice.service';
import { SizePipe } from 'app/pipes/size.pipe';
@Component({
  selector: 'app-expenses-upload',
  standalone: true,
  imports: [CommonModule,
    PagerModule,
    MatMenuModule,
    GridAllModule,
    SharedModule,
    InnoInputSearchComponent,
    InnoSpinomponent,
    InnoTableActionComponent,
    SizePipe],
  providers: [LayoutUtilsService],
  templateUrl: './expenses-upload.component.html',
  styleUrl: './expenses-upload.component.scss'
})
export class ExpensesUploadComponent implements OnDestroy, OnInit {
  public isLoading = false;
  private _subscriptions: Subscription[] = [];
  public search: string = ''
  private searchSubject = new Subject<string>();
  public sort: SortGird
  public sortOptions: SortSettingsModel = { columns: [] };
  public selectionOptions?: SelectionSettingsModel;
  public expensesId: string | undefined = '';
  public listExpenses: Expenses[] = []
  public dataSource: any;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  public columnName: string
  public direction: any
  @ViewChild('grid') grid?: GridComponent;
  constructor(
    private translate: TranslateService,
    private layoutUtilsService: LayoutUtilsService,
    private router: Router,
    private destroyRef: DestroyRef,
    private activatedRoute: ActivatedRoute,
    public storeService: StoreService,
    private expensesService: ExpensesService,
    private cdnService: CdnService
  ) { }

  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
      this.LoadUpload();
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }

  }
  LoadUpload() {
    this.isLoading = true
    let params: GetUploadedExpensesQueryParam = {
      Page: this.currentPage,
      PageSize: this.pageSizesDefault,
      Search: this.search,
      ExpensesId: this.expensesId,
      ...this.sort
    };

    if (this.expensesId) {
      this.expensesService.GetAllUploadExpenses(params).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
        next: (res) => {
          console.log("res", res.data);

          if (res) {
            this.isLoading = false
            this.totalPages = res.totalRecords
            this.dataSource = res.data
            if (this.columnName) {
              this.sortOptions = {
                columns: [{ field: this.columnName, direction: this.direction }]
              };
            }

          }
        }
      });
    }
  }
  GetData() {
    let params: GetExpenseQueryParam = {
      Page: 0,
      PageSize: 100,
      Search: "",
    }
    this.expensesService.GetAllExpenses(params).pipe(
      takeUntilDestroyed(this.destroyRef),
      concatMap((res) => {
        if (res) {
          this.listExpenses = res.data;
          if (this.listExpenses.length > 0) {
            this.expensesId = this.listExpenses[0].id
          }
          let payloadproject: Parameter = {
            Page: this.currentPage,
            PageSize: 10,
            Search: "",
            ExpensesId: this.expensesId
          };

          if (this.expensesId) {
            return this.expensesService.GetAllUploadExpenses(payloadproject).pipe(takeUntilDestroyed(this.destroyRef));
          }
        }
        return [];
      })
    ).subscribe(
      (secondRes: any) => {
        if (secondRes) {
          this.totalPages = secondRes.totalPage
          this.dataSource = secondRes.data
        }
      },
      (error: any) => {
        console.error('Error:', error);
      }
    );
  }

  handleSelectExpenses($event: any) {
    this.expensesId = ($event.target as HTMLSelectElement).value;
    this.LoadUpload();
  }




  handleSearch(search: string) {
    this.searchSubject.next(search);
  }
  ngOnInit(): void {
    const sb = this.searchSubject.pipe(debounceTime(550)).subscribe((search) => {
      this.search = search ?? "";
      this.LoadUpload();
    });

    this._subscriptions.push(sb)

    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.page) {
        this.currentPage = queryParams.page;
        this.LoadUpload();
      }
      else {
        this.GetData()
      }

    });

  }
  creaFormDelete() {

  }

  handleDelete(item: any) {
    const _title = this.translate.instant('Delete File !');
    const _description = this.translate.instant('Do you want to delete?');
    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return

      // this.expensesService.DeleteServices([item.id]).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      //   if (res) {
      //     this.LoadAllServiceByProjectAsync();
      //     this.toastService.showSuccess("Delete", "Success");
      //   }
      //   else {
      //     this.toastService.showError("Fail", "Fail");
      //   }
      // })
    })
  }

  handleDowload(item: any) {
    this.cdnService.GetFile(item.filename).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        const blob = new Blob([res], { type: item.type });
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = item.filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    }
    )
  }

  handleArchive(item: any) {
    // this.serviceService.UpdateArchive([item.id]).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
    //   if (res) {
    //     this.LoadAllServiceByProjectAsync();
    //     this.toastService.showSuccess("Save", "Success");
    //   }
    //   else {
    //     this.toastService.showError("Fail", "Fail");
    //   }
    // })
  }

  onActionBegin(args: ActionEventArgs) {
    if (args.requestType === 'sorting') {
      this.columnName = args.columnName
      this.direction = args.direction

      this.sort = {
        columnName: args.columnName,
        direction: args.direction
      }
      if (this.columnName) {
        this.LoadUpload();
        return;
      }
      this.sortOptions = {
        columns: []
      };
      this.sort = null
      this.LoadUpload();
    }

  }
  ngOnDestroy(): void {

    if (this._subscriptions) {
      this._subscriptions.forEach((sb) => sb.unsubscribe());
    }
  }
}
