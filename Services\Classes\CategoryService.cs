﻿using InnoBook.DTO;
using InnoBook.DTO.Category;
using InnoBook.DTO.CoreModel;
using InnoBook.Entities;
using InnoBook.Extension;
using InnoBook.Request.Category;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using InnoBook.Entities;
namespace InnoBook.Services.Classes
{
    public class CategoryService(InnoLogicielContext context) : ICategoryService
    {

        public InnoLogicielContext _context { get; set; } = context;

        public async Task<bool> CreateCategoryAsync(List<RequestCategory> category)
        {
            int index = 1;
            foreach (var item in category)
            {
                item.Number = _context.Categorys.Count() + index;
                index++;
            }
            var newCategory = category.Select(item => new Category
            {
                CategoryName = item.CategoryName,
                CompanyId = item.CompanyId,
                CanEdit = item.CanEdit,
                Number = item.Number
            }).ToList();
            _context.Categorys.AddRange(newCategory);
            await _context.SaveChangesAsync();
            return true;
        }
        public async Task<bool> CreateCategoryItemAsync(List<RequestCategoryItem> category)
        {
            int index = 1;
            foreach (var item in category)
            {
                item.Number = _context.CategoryItem.Count()+ index;
                index++;
            }
            var categoryItems = category.Select(item => new CategoryItem
            {
                ItemName = item.ItemName,
                CompanyId=item.CompanyId,
                CanEdit = item.CanEdit,
                Number = item.Number
            }).ToList();
            _context.CategoryItem.AddRange(categoryItems);
            await _context.SaveChangesAsync();
            return true;
        }
        public async Task<bool> DeleteCategory(List<Guid?> listCategory)
        {
            try
            {

                var listItemDetailItem = _context.CategoryItem.Where(x => listCategory.Contains(x.Id)).ToList();
                _context.CategoryItem.RemoveRange(listItemDetailItem);

                var listItemDetail = _context.Categorys.Where(x => listCategory.Contains(x.Id)).ToList();
                _context.Categorys.RemoveRange(listItemDetail);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public async Task<bool> UpdateCategory(RequestCategory category)
        {
            try
            {
                var data = await _context.Categorys.FirstOrDefaultAsync(x => x.Id ==category.Id);
                if(data == null)
                {
                    return false;
                }    
                data.Number = category.Number;
                data.CategoryName = category.CategoryName;
                data.CanEdit = category.CanEdit;
                data.CategoryItems = category.CategoryItems;
                await _context.SaveChangesAsync();
            return true;
            }
             catch
            {
                return false;
            }    
        }

        public async Task<bool> DeleteCategoryItem(List<Guid?> listCategoryIteam)
        {
            try
            {

                var listItemDetail = _context.CategoryItem.Where(x => listCategoryIteam.Contains(x.Id)).ToList();
                _context.CategoryItem.RemoveRange(listItemDetail);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public async Task<PaginatedResponse<GetAllCategoryDTO>> GetAllCategoryAsync(PaginatedRequest query)
        {
            var data =  _context.Categorys
                .Select(c=>new GetAllCategoryDTO
                {
                    Id=c.Id,
                    Number=c.Number,
                    CategoryName = c.CategoryName,
                    CreatedAt = c.CreatedAt,
                })
                .OrderBy(x=>x.Number)
                                .AsQueryable();
            if (query.Search != null && !string.IsNullOrEmpty(query.Search))
            {
                data = data.Where(c => c.CategoryName.ToLower().Contains(query.Search.ToLower()));
            }
            if (query.Filter != null && query.Filter.ContainsKey("Sort") && query.Filter["Sort"] != null)
            {
                var sort = JsonConvert.DeserializeObject<SortDTO>(query.Filter["Sort"])!;

                data = SortExtensions.DynamicSort(data, sort.columnName, sort.direction == "Ascending" ? false : true);

            }
            var totalRecords = await data.CountAsync();

            var result = await data
            .Skip((query.Page - 1) * query.PageSize)
            .Take(query.PageSize)
            .ToListAsync();
            return new PaginatedResponse<GetAllCategoryDTO>
            {
                Data = result,
                Page = query.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)query.PageSize),
                PageSize = query.PageSize,
                TotalRecords = totalRecords
            };
        }

        public  async Task<List<GetCategoryDTO>> GetCategory()
        {
            var data = await _context.Categorys.Select(c => new GetCategoryDTO
            {
                Id =c.Id,
                Number = c.Number,
                CategoryName = c.CategoryName,
                CanEdit = c.CanEdit,
                CategoryItems=c.CategoryItems.Select(i=> new CategoryItemDTO
                {
                    Id=i.Id,
                    CategoryId=i.CategoryId,
                    ItemName=i.ItemName,
                    Number=i.Number,

                } ).ToList(),

            })
                               .ToListAsync();
            return data;
        }

        public async Task<GetCategoryDTO> GetCategoryById(string Id)
        {
            var data = await _context.Categorys
             .Where(c => c.Id.ToString() == Id)
             .Select(c => new GetCategoryDTO
             {
                 Id = c.Id,
                 CategoryName = c.CategoryName,
                 CategoryItems = c.CategoryItems.Select(ci => new CategoryItemDTO
                 {
                     Id = ci.Id,
                     CategoryId = ci.CategoryId,
                     ItemName = ci.ItemName,
                      Number = ci.Number
                 }).ToList()
             })
             .FirstOrDefaultAsync();
            return data;
        }
    }
}
