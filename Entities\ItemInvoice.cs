﻿using InnoLogiciel.Server.Entities;
using System.ComponentModel.DataAnnotations.Schema;

namespace InnoBook.Entities
{
    public class ItemInvoice : BaseGuidEntity
    {
        public Guid InvoiceId { get; set; }
        public Guid? ProjectId { get; set; }
        public Guid? ItemId { get; set; }
        public Guid? ServiceId { get; set; }
        public Guid? ExpensesId { get; set; }
        public Guid? UserId { get; set; }
        public decimal? rate { get; set; }
        public decimal? qty { get; set; }
        public string? description { get; set; }
        public int Position { get; set; }
        public DateTime? DateSelectItem { get; set; } = DateTime.UtcNow;
        public string? trackingId { get; set; }
        // Navigation properties
        public Item? Item { get; set; }
        public Project? Project { get; set; }
        public Service? Service { get; set; }
        public Invoice? Invoice { get; set; }
        public Expenses? Expenses { get; set; }
        public User? User { get; set; }
        public List<Tax>? Taxes { get; set; }
    }
}
