using InnoBook.Attributes;
using InnoBook.Controllers;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Mail;
using InnoBook.DTO.User;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Model;
using InnoBook.Services;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using InnoLogiciel.Server.Enum.User;
using InnoLogiciel.Server.Model;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TimeZoneConverter;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;
using static Org.BouncyCastle.Crypto.Engines.SM2Engine;

namespace InnoLogiciel.Server.Controllers
{
    [ApiController]
    [Authorize]
    [Route("api/[controller]")]
    public class UsersController(IUserIdentity userIdentity, IMailService _mail, IHttpContextAccessor httpContextAccessor,
        IBusinessService businessService, IEncryptionService encryptionService) : BaseController(httpContextAccessor)
    {

        [RequiredRoles("Admin")]
        [HttpGet("GetAllUser")]
        public async Task<ActionResult<PaginatedResponse<IActionResult>>> GetAllUser([FromQuery] PaginatedRequest query)
        {
            var data = await userIdentity.GetAllUser(query);
 
            return Ok(data);
        }

        [RequiredRoles("All")]
        [HttpGet("CheckTimer")]
        public async Task<ActionResult> CheckTimer()
        {
            var data = await userIdentity.CheckTimer(Guid.Parse(IdUser));
            return Ok(data);
        }

        // Delete account by user
        [HttpDelete("DeleteUser")]
        [Ownership(typeof(User))]
        public async Task<IActionResult> DeleteUser(string idUser)
        {
           var result= await userIdentity.DeleteUser(idUser);
            return Ok(result);
        }

        [RequiredRoles("Admin")]
        [HttpPost("SendMail")]
        public object SendMail(MailModel mailModel)
        {
            string templateDirectory = "EmailTemplates";
            var templateService = new TemplateService(templateDirectory);

            // Load and process template
            string templateContent = templateService.LoadTemplate("SendCodeLogin.html");
            var placeholders = new Dictionary<string, string>
            {
                { "Code", Constant.GenerateRandomFiveDigitNumber() }
            };
            string emailBody = templateService.ProcessTemplate(templateContent, placeholders);
            try
            {
                var mail = new MailInfoAndContent
                {
                    Body = emailBody,
                    To = mailModel.EmailReciver,
                    Subject = mailModel.Title
                };
                _mail.SendMail(mail);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }


        [RequiredRoles("All")]
        [HttpPost("UpdateTimer")]
        public async Task<IActionResult> UpdateTimer(UpdateTimerDTO user)
        {
            await userIdentity.UpdateTimer(user, Guid.Parse(IdUser));
            return Ok();
        }


        [AllowAnonymous]
        [HttpPost("Login")]
        public async Task<IActionResult> Login(User user)
        {
            try
            {
                var data = await userIdentity.Login(user.Email, user.Password);
                return Ok(new { IsSuccess = true, Token = data });
            }
            catch (Exception ex)
            {
                return Ok(new { IsSuccess = false, Message = ex.Message });
            }
        }

        [AllowAnonymous]
        [HttpPost("RefreshToken")]
        public async Task<IActionResult> RefreshToken([FromBody] InnoBook.DTO.CoreModel.RefreshRequest refreshRequest)
        {
            var principal = userIdentity.GetPrincipalRefreshToken(refreshRequest.RefreshToken);
            if (principal == null)
            {
                return Unauthorized();
            }

            var companyId = userIdentity.GetIdCompanyFromRefreshToken(refreshRequest.RefreshToken);
            var newAccessToken = await userIdentity.RefreshToken(refreshRequest.RefreshToken, companyId.ToString());
            return Ok(newAccessToken);
        }

        [RequiredRoles("All")]
        [HttpPost("UpdateAccessToken")]
        public IActionResult UpdateAccessToken(RequestUpdateToken body)
        {
            string Token = Utils.GetTokenFromRequest(Request);
            if (Token == null)
            {
                return BadRequest("No Token");
            }
            var companyId = businessService.GetCompanyId(body.BusinessId);
            var data = userIdentity.UpdateAccessToken(Token, "companyId", companyId);
            if (data==null)
            {
                return BadRequest("No Token.");
            }
            return Ok(data.Result);
        }


        [AllowAnonymous]
        [HttpGet("SignIn")]
        public async Task<IActionResult> SignIn(string email, string code)
        {
            var user = await userIdentity.FindUserEmailAndCode(email, code);
            if (user == null)
            {
                return BadRequest("The code has expired or is invalid.");
            }
            if (user.ExpireTime < DateTime.UtcNow)
            {
                return BadRequest("The code has expired or is invalid.");
            }
            var data = await userIdentity.SignIn(email);

            if (data == null)
            {
                return BadRequest("Null");
            }
            return Ok(data);
        }

        [AllowAnonymous]
        [HttpGet("ResendCode")]
        public async Task<IActionResult> ResendCode(string email)
        {
            var code = Constant.GenerateRandomFiveDigitNumber();

            string templateDirectory = "EmailTemplates";
            var templateService = new TemplateService(templateDirectory);

            // Load and process template
            string templateContent = templateService.LoadTemplate("SendCodeLogin.html");
            var placeholders = new Dictionary<string, string>
            {
                { "Code", code }
            };
            string emailBody = templateService.ProcessTemplate(templateContent, placeholders);
            var check_user = await userIdentity.FindUserEmail(email);
            if (check_user == null)
            {
                return BadRequest("Not found user");
            }

            await userIdentity.UpdateTwoFactorAsync(check_user.Id, code, DateTime.UtcNow.AddMinutes(5));
            try
            {

                var mail = new MailInfoAndContent
                {
                    To = email,
                    Subject = "Login Innboook",
                    Body = emailBody
                };
                _mail.SendMail(mail);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
            return Ok(true);

        }

        [AllowAnonymous]
        [Route("RecoverPassword")]
        public IActionResult RecoverPassword(string email)
        {
            var data = userIdentity.GenerateUrlRecoverPass(email);
            string templateDirectory = "EmailTemplates";
            var templateService = new TemplateService(templateDirectory);

            // Load and process template
            string templateContent = templateService.LoadTemplate("RecoverPassword.html");
            var placeholders = new Dictionary<string, string>
            {
                { "Result", data.Result }
            };
            string emailBody = templateService.ProcessTemplate(templateContent, placeholders);
            if (data == null)
            {
                return BadRequest("Null");
            }
            try
            {
                var mail = new MailInfoAndContent
                {
                    To = email,
                    Subject = "Recover Password",
                    Body = emailBody
                };
                _mail.SendMail(mail);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [AllowAnonymous]
        [HttpPut("ChangePassWithToken")]
        public IActionResult ChangePassWithToken(TokenPass dt)
        {
            bool  result = userIdentity.ChangePassWithToken(dt);
             return Ok(result);
        }


        [AllowAnonymous]
        [HttpPost("Register")]
        public async Task<IActionResult> Register([FromBody] RegisterUserDTO userDto)
        {
            try
            {
                var token = await userIdentity.Register(userDto);
                return Ok(token);
            }
            catch(Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [AllowAnonymous]
        [HttpPost("ValidateInvitation")]
        public async Task<IActionResult> ValidateInvitation([FromBody] ValidateInvitationDto invitation)
        {
            try
            {
                var token = await userIdentity.ValidateInvitation(invitation);
                return Ok(token);
            }
            catch (Exception ex)
            {

                return BadRequest(ex.Message);
            }
        }

        [RequiredRoles("Admin")]
        [HttpGet("GetUsers")]
        public async Task<ActionResult<List<GetUserDTO>>> GetUsers()
        {
            var list = await userIdentity.ListUser();
          
            return list;
        }

        [RequiredRoles("All")]
        [HttpGet("{id}")]
        public async Task<IActionResult> GetUser(Guid id)
        {
            var user = await userIdentity.FindId(id);

            if (user == null)
            {
                return BadRequest("No data");
            }
            return Ok(user); ;
        }

        [RequiredRoles("All")]
        [HttpGet("GetUserProfile")]
        public async Task<IActionResult> GetUserProfile()
        {
            var user = await userIdentity.GetUserProfile(Guid.Parse(IdUser));
            bool isValid = TZConvert.KnownIanaTimeZoneNames.Contains(user.TimeZoneId);
            if(isValid)
            {
                user.TimeZoneId = TZConvert.IanaToWindows(user.TimeZoneId);

            }

            if (user == null)
            {
                return BadRequest("No data");
            }
            return Ok(user);
        }

        [RequiredRoles("All")]
        [HttpPut("UpdateUserProfile")]
        public async Task<IActionResult> UpdateUserProfile(UserUpdateDTO user)
        {
            user.Id = Guid.Parse(IdUser);
            await userIdentity.Update(user);
            return CreatedAtAction(nameof(GetUser), new { id = user.Id }, user);
        }

        [RequiredRoles("All")]
        [HttpPut("UpdateUserProfileByEmail")]
        public async Task<IActionResult> UpdateUserProfileByEmail(UserUpdateDTO user)
        {
          
            var result = await userIdentity.Update(user.Email, user);
         
            return Ok(result);
        }

        [AllowAnonymous]
        [HttpPost("SignUp")]
        public async Task<ActionResult> SignUp(CreateUserDTO user)
        {

            string templateDirectory = "EmailTemplates";
            var templateService = new TemplateService(templateDirectory);
            var code = Constant.GenerateRandomFiveDigitNumber();
            // Load and process template
            string templateContent = templateService.LoadTemplate("SendCodeLogin.html");
            var placeholders = new Dictionary<string, string>
            {
             { "Code", code}
            };
            string emailBody = templateService.ProcessTemplate(templateContent, placeholders);

            var check_user = await userIdentity.FindUserEmail(user.Email);
            if (check_user == null)
            {

                var CreateUser = new User()
                {
                    FirstName= user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email,
                    Is2FAEnabled = user.Is2FAEnabled,
                    CreatedAt = DateTime.UtcNow,
                    ExpireTime = DateTime.UtcNow.AddMinutes(5),
                    TwoFactorCode = code,
                    Status = UserStatus.Active,
                };

                await userIdentity.Insert(user);
                user.Id = CreateUser.Id;

            }
            else
            {
                var updateUser = new UserUpdateDTO()
                {
                    Id= check_user.Id,
                    Email = check_user.Email,
                    Status= UserStatus.Active,
                    TwoFactorCode = code,
                    ExpireTime = DateTime.UtcNow.AddMinutes(5)

                };
                await userIdentity.UpdateTwoFactorAsync(check_user.Id, code, DateTime.UtcNow.AddMinutes(5));

                user.Id = check_user.Id;
            }
            try
            {
                var mail = new MailInfoAndContent
                {
                    To = user.Email,
                    Subject = "Login Innboook",
                    Body = emailBody
                };
                _mail.SendMail(mail);
            }
            catch (Exception ex)
            {
                throw;
            }
            return CreatedAtAction(nameof(GetUser), new { id = user.Id }, user);
        }

        [RequiredRoles("Admin")]
        [HttpPost("PostUser")]
        public async Task<ActionResult<User>> PostUser(CreateUserDTO user)
        {
            var check_user = await userIdentity.FindUserEmail(user.Email);
            if (check_user == null)
            {
                await userIdentity.Insert(user);
            }
            else
            {
                return BadRequest("Email already exists");
            }
            return CreatedAtAction(nameof(GetUser), new { id = user.Id }, user);
        }
    }
}
