using Amazon.S3.Model;
using InnoBook.DTO;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Members;
using InnoBook.DTO.Project;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Extension;
using InnoBook.Request.Project;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;

namespace InnoBook.Services.Classes
{
    public class ProjectService(InnoLogicielContext context) : IProjectService
    {
        public async Task<PaginatedResponse<ProjectDTO>> GetAllProjects(GetProjectRequestParam filter, Guid UserId,string companyId)
        {
            var query = context.Projects.Include(c => c.Client)
                                        .Include(c => c.Members)
                                        .Include(c => c.TimeTrackings)
                                        .Include(c => c.Invoices)
                                        .Where(c => c.Client.CompanyId == Guid.Parse(companyId));


            // Search
            if (filter.Search != null && !string.IsNullOrEmpty(filter.Search))
            {
                query = query.Where(c => c.ProjectName.ToLower().Contains(filter.Search.ToLower())
                                         || c.Client.ClientName.ToLower().Contains(filter.Search.ToLower()));
            }

            // Filter Date
            if (!string.IsNullOrEmpty(filter.TimeZone) && !string.IsNullOrEmpty(filter.FilterDate) && DateTime.TryParse(filter.FilterDate, out var date))
            {
                var utcDate = Utils.GetStartAndEndOfUtcDate(date, filter.TimeZone);
                query = query.Where(x => x.EndDate >= utcDate.StartUtcDateTime && x.EndDate < utcDate.EndUtcDateTime);
            }

            // Filter IsActive project
            if(filter.IsActive != null)
            {
                query = query.Where(c => c.isActive == filter.IsActive.Value && c.isArchive == false);
            }

            // Filter IsArchive project
            if (filter.IsArchive != null)
            {
                query = query.Where(c => c.isArchive == filter.IsArchive.Value);
            }

            // Filter assigned project 
            if (filter.Role != UserBusinessRole.Admin)
            {
                // Restrict normal users only get the assigned projects
                query = query.Where(p => p.Members.Select(m => m.UserId).Contains(UserId));
            }

            int totalRecords = await query.CountAsync();

            var panigationQuery = query.AsNoTracking().OrderByDescending(x => x.CreatedAt)
                                  .Select(p => new ProjectDTO
                                  {
                                      Id = p.Id,
                                      ProjectName = p.ProjectName,
                                      ClientName = p.Client != null ? p.Client.ClientName : null,
                                      StartDate = p.StartDate,
                                      EndDate = p.EndDate,
                                      Description = p.Description,
                                      Option = p.Option,
                                      Type = p.Type,
                                      FlatRate = p.FlatRate,
                                      HourlyRate = p.HourlyRate,
                                      Billable = p.Billable,
                                      TotalHours = p.TotalHours,
                                      ActualHours = p.ActualHours,
                                      Budget = p.Budget,
                                      isArchive = p.isArchive,
                                      isActive = p.isActive,
                                      Status = p.Status,
                                      Members = p.Members
                                        .Where(m => m.Status == 0)
                                        .Select(m => new MemberInProjectDTO
                                        {
                                            User = new DTOUserMember
                                            {
                                                Id = m.User.Id,
                                                LastName = m.User.LastName,
                                                FirstName = m.User.FirstName,
                                                Email = m.User.Email
                                                
                                            }
                                        })
                                        .ToList()

                                  })
                                .AsQueryable();

            // Sort
            if (filter.ColumnName != null && filter.Direction != null)
            {
                panigationQuery = SortExtensions.DynamicSort(panigationQuery, filter.ColumnName, filter.Direction != "Ascending");
            }

            // Panigation query
            if (filter.Page > 0 && filter.PageSize > 0)
            {
                panigationQuery = panigationQuery.Skip((filter.Page - 1) * filter.PageSize)
                                                 .Take(filter.PageSize);
            }

            var data = await panigationQuery.ToListAsync();

            var projectIds = data.Select(p => p.Id);

            var timeTrackings = await context.TimeTrackings.Where(t => projectIds.Contains((Guid)t.ProjectId) && t.Member.Status == 0)
                                                           .Select(t => new GetTimeTrackingInProjectDTO
                                                           {
                                                               EndTime = t.EndTime,
                                                               Billable = t.Billable,
                                                               ProjectId = (Guid)t.ProjectId
                                                           }).ToListAsync();

            var invoices = await context.Invoices.Where(t => projectIds.Contains((Guid)t.ProjectId))
                                                 .Select(i => new InvoiceInProjectDTO
                                                 {
                                                     TotalAmount = i.TotalAmount,
                                                     ProjectId = (Guid)i.ProjectId
                                                 }).ToListAsync();
            data = data.Select(p =>
            {
                p.TimeTrackings = timeTrackings.Where(t => t.ProjectId == p.Id).ToList();
                p.Invoices = invoices.Where(t => t.ProjectId == p.Id).ToList();
                return p;
            }).ToList();

            return new PaginatedResponse<ProjectDTO>()
            {
                PageSize = filter.PageSize,
                Page = filter.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)filter.PageSize),
                Data = data,
                TotalRecords = totalRecords
            };
        }

       
        public async Task<RequestProject> CreateProject(RequestProject project,Guid auId, string companyId)
        {
           
            try
            {
                var serviceIds = project.Services.Select(s => s.Id).ToHashSet();
                var addedServices = await context.Services
                    .Where(s => serviceIds.Contains(s.Id))
                    .ToListAsync();

                project.Members.ForEach(c => {
                    c.CreatedBy = auId.ToString();
                    c.CompanyId = Guid.Parse(companyId);
                });
                var newProject = new Project
                {
                    ProjectName= project.ProjectName,
                    CompanyId = Guid.Parse(companyId),
                    StartDate = DateTime.UtcNow,
                    Type = 1,
                    Description=project.Description,
                    EndDate=project.EndDate,
                    ClientId=project.ClientId,
                    FlatRate = project.FlatRate,
                    TotalHours = project.TotalHours,
                    ActualHours = project.ActualHours,
                    Budget = project.Budget ,
                    Status = project.Status,
                    HourlyRate = project.HourlyRate == null ? 0 : project.HourlyRate,
                    CreatedBy = auId.ToString(),
                    Services = addedServices,
                    isActive = true,
                    isArchive = false,
                    Members= project.Members

                };
                await context.Projects.AddAsync(newProject);
                await context.SaveChangesAsync();
                return project;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<bool> DeleteProject(List<Guid?> listProject, bool isActive)
        {
            try
            {

                var listItemDetail = context.Projects.Where(x => listProject.Contains(x.Id)).ToList();
                foreach (var item in listItemDetail)
                {
                    item.isActive = isActive;
                }
                await context.SaveChangesAsync();
                return true;
            }catch (Exception ex)
            {
                throw;
            }

        }

        public async Task<bool> Update(RequestProject model, string companyId, string userId)
        {
            try
            {
                // Suppression des membres désactivés
                var membersToRemove = model.Members
                    .Where(m => m.Status == MemberStatus.Deactivated)
                    .ToList();

                if (membersToRemove.Count != 0)
                {
                    context.Members.RemoveRange(membersToRemove);
                }

                // Nettoyer les nouveaux membres (à insérer)
                model.Members = model.Members
                    .Where(x => x.Id == Guid.Empty)
                    .ToList();


                // Gestion des services liés au projet
                var modelServiceIds = model.Services.Select(s => s.Id).ToHashSet();

                var existingServices = await context.Services
                    .Where(s => modelServiceIds.Contains(s.Id))
                    .ToListAsync();

                // Charger le projet avec ses services liés
                var project = await context.Projects
                    .Include(p => p.Services)
                    .Include(p => p.Members)
                    .FirstOrDefaultAsync(p => p.Id == model.Id && p.isActive == true);

                if (project != null)
                {
                    // Supprimer les services non sélectionnés
                    var servicesToRemove = project.Services
                        .Where(s => !modelServiceIds.Contains(s.Id))
                        .ToList();

                    foreach (var s in servicesToRemove)
                        project.Services.Remove(s);

                    // Ajouter les services manquants (déjà trackés)
                    var addedService = existingServices.Where(c => modelServiceIds.Contains(c.Id)).ToList();
                    project.Services.AddRange(addedService);

                    // Mise à jour des membres ajoutés
                    foreach (var newMember in model.Members)
                    {
                        project.Members.Add(newMember);
                    }
                    project.ProjectName = model.ProjectName;
                    project.StartDate = model.StartDate;
                    project.EndDate = model.EndDate;
                    project.Description = model.Description;
                    project.FlatRate = model.FlatRate;
                    project.HourlyRate = model.HourlyRate;
                    project.Billable = model.Billable;
                    project.Option = model.Option;
                    project.TotalHours = model.TotalHours;
                    project.ActualHours = model.ActualHours;
                    project.ClientId = model.ClientId;

                    context.Projects.Update(project);
                }

                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        public async Task<bool> UpdateArchive(Guid ProjectId ,bool isArchive)
        {
            try
            {

                var data = context.Projects.FirstOrDefault(x => x.Id == ProjectId);
                if(data!=null)
                {
                    data.isArchive = isArchive;
                    await context.SaveChangesAsync();
                }
                else
                {
                    return false;
                }
                 
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public async Task<CalculationProject> CalculationProject(Guid UserId, string companyId, string role)
        {
            var query = context.Projects.Include(c => c.Members)
                                        .Include(c => c.TimeTrackings)
                                        .Include(c => c.Invoices)
                                        .Where(c => c.Client.CompanyId == Guid.Parse(companyId) && c.isActive && !c.isArchive);

            if (role != UserBusinessRole.Admin)
            {
                // Restrict normal users only get the assigned projects
                query = query.Where(p => p.Members.Select(m => m.UserId).Contains(UserId));
            }

            var res = await query.AsNoTracking().Select(p => new
            {
                InvoiceAmount = p.Invoices.Sum(c => c.TotalAmount),
                TimeTrackings = p.TimeTrackings.Select(c => new { EndTime = c.EndTime, Billable = c.Billable })
            }).ToListAsync();

            TimeSpan totalLoggedTime = TimeSpan.FromSeconds(0);
            TimeSpan totalLoggedTimeUnbilled = TimeSpan.FromSeconds(0);
            var timeTrackings = res.SelectMany(c => c.TimeTrackings).ToList();
            timeTrackings.ForEach(t =>
            {
                if (TimeSpan.TryParse(t.EndTime, out TimeSpan time))
                {
                    totalLoggedTime = totalLoggedTime.Add(time);
                    if (t.Billable)
                    {
                        totalLoggedTimeUnbilled = totalLoggedTimeUnbilled.Add(time);
                    }
                }
            });

            string totalLoggedFormatted = $"{(int)totalLoggedTime.TotalHours}:{totalLoggedTime.Minutes:D2}:{totalLoggedTime.Seconds:D2}";
            string totalLoggedFormattedUnbilled = $"{(int)totalLoggedTimeUnbilled.TotalHours}:{totalLoggedTimeUnbilled.Minutes:D2}:{totalLoggedTimeUnbilled.Seconds:D2}";

            return new CalculationProject
            {
                TotalLogged= (decimal)totalLoggedTime.TotalSeconds,
                TotalAmount = res.Sum(c => c.InvoiceAmount),
                TotalLoggedFormatted = totalLoggedFormatted,   
                TotalUnbilled= totalLoggedFormattedUnbilled,
                TotalActive = res.Count,
            };
           
        }

        public async Task<ProjectDetailDTO> GetProjectById(Guid auId)
        {
            try
            {
                var data = await context.Projects.AsNoTracking().Include(c => c.Client)
                                              .Include(c => c.Services).ThenInclude(x => x.Taxes).ThenInclude(t => t.CompanyTax)
                                               .Include(c => c.Members.Where(m => m.Status == 0)).ThenInclude(u => u.User)
                                                .Select(p => new ProjectDetailDTO
                                                {
                                                    Id = p.Id,
                                                    ProjectName = p.ProjectName,
                                                    ClientId= p.Client.Id,
                                                    ClientName = p.Client != null ? p.Client.ClientName : null,
                                                    StartDate = p.StartDate,
                                                    EndDate = p.EndDate,
                                                    Description = p.Description,
                                                    Option = p.Option,
                                                    Type = p.Type,
                                                    FlatRate = p.FlatRate,
                                                    HourlyRate = p.HourlyRate,
                                                    Billable = p.Billable,
                                                    TotalHours = p.TotalHours,
                                                    ActualHours = p.ActualHours,
                                                    Budget = p.Budget,
                                                    isArchive = p.isArchive,
                                                    isActive = p.isActive,
                                                    Status = p.Status,
                                                    Services=p.Services,
                                                    Members = p.Members.Select(m=> new ProjectMemberDTO
                                                    {
                                                        Id=m.Id,
                                                        Status=m.Status,
                                                        User = m.User != null ? new DTOUserMember
                                                        {
                                                            Id = m.User.Id,
                                                            FirstName = m.User.FirstName,
                                                            LastName = m.User.LastName,
                                                            Email = m.User.Email,
                                                        } : null,

                                                    }).Where(m => m.Status == MemberStatus.Active).ToList()

                                                })
                                             .FirstOrDefaultAsync(c => c.Id == auId);



                return data;
            }catch(Exception ex)
            {
                throw ex;
            }
            
        }
    }
}
