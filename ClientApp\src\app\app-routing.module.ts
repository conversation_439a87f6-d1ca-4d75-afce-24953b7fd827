import { Routes } from "@angular/router";
import { Role } from "./enum/role.enum";
import { RoleGuard } from "./helpers/roleRouter.guard";

export const routes: Routes = [
  {
    path: "",
    loadComponent: () =>
      import("./layout/authenticatedLayout/authenticatedLayout.component").then(
        (mod) => mod.AuthenticatedLayoutComponent
      ),
    canActivate: [RoleGuard],
    data: { roles: [Role.All] },
    children: [
      {
        path: "",
        redirectTo: "/dashboard",
        pathMatch: "full",
      },

      {
        path: "dashboard",
        canActivate: [RoleGuard],
        data: { roles: [Role.All] },
        loadComponent: () =>
          import("./views/dashboard/dashboard.component").then(
            (mod) => mod.DashboardComponent
          ),
      },
      {
        path: "pos",
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin, Role.Manager] },
        loadComponent: () =>
          import("./views/pos/pos.component").then(
            (mod) => mod.PosComponent
          ),
      },
      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin, Role.Manager] },
        path: "clients",
        loadComponent: () =>
          import("./views/clients/clients.component").then(
            (mod) => mod.ClientsComponent
          ),
      },

      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin, Role.Manager] },
        path: "estimates",
        children: [
          {
            path: "",
            pathMatch: "full",
            loadComponent: () =>
              import("./views/estimate/estimate.component").then((mod) => mod.EstimateComponent),
          },
          {
            path: ":id",
            pathMatch: "full",
            loadComponent: () =>
              import("./views/estimate/estimate-details/estimate-details.component").then(
                (mod) => mod.EstimateDetailsComponent
              ),
          },
        ]
      },
      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin, Role.Manager, Role.Contractor, Role.Accountant] },
        path: "invoices",
        children: [
          {
            path: "",
            pathMatch: "full",
            loadComponent: () =>
              import("./views/invoice/invoice/invoice.component").then((mod) => mod.InvoiceComponent),
          },
          {
            path: ":id",
            pathMatch: "full",
            loadComponent: () =>
              import("./views/invoice/detail-invoice/detail-invoice.component").then(
                (mod) => mod.DetailInvoiceComponent
              ),
          },
        ]
      },
      {
        path: "recurring-templates",
        loadComponent: () =>
          import("./views/invoice/recurring-templates/recurring-templates.component").then((mod) => mod.RecurringTemplatesComponent),
      },
      {
        path: "retainers",
        loadComponent: () =>
          import("./home/<USER>").then((mod) => mod.HomeComponent),
      },
      {
        path: "payments",
        loadComponent: () =>
          import("./views/payment/payments/payments.component").then((mod) => mod.PaymentsComponent),
      },
      {
        path: "checkout-links",
        loadComponent: () =>
          import("./home/<USER>").then((mod) => mod.HomeComponent),
      },
      {
        path: "billing",
        data: { roles: [Role.Admin] },
        loadComponent: () =>
          import("./views/billing/billing.component").then(
            (mod) => mod.BillingComponent
          ),
      },
      {
        path: "billing/plan-selection",
        data: { roles: [Role.Admin] },
        loadComponent: () =>
          import("./views/billing/plan-selection/plan-selection.component").then(
            (mod) => mod.PlanSelectionComponent
          ),
      },
      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin] },
        path: "stripe-test",
        loadComponent: () =>
          import("./views/stripe-test/stripe-test.component").then((mod) => mod.StripeTestComponent),
      },
      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin, Role.Manager, Role.Employee, Role.Accountant] },
        path: "expenses",
        children: [
          {
            path: "",
            pathMatch: "full",
            loadComponent: () =>
              import("./views/expenses/expenses.component").then((mod) => mod.ExpensesComponent),
          },
          {
            path: "upload",
            pathMatch: "full",
            loadComponent: () =>
              import("./views/expenses/expenses-upload/expenses-upload.component").then(
                (mod) => mod.ExpensesUploadComponent
              ),
          },
        ]
      },

      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin, Role.Manager, Role.Employee, Role.Contractor] },
        path: "projects",
        loadComponent: () =>
          import("./views/projects/projects.component").then(
            (mod) => mod.ProjectsComponent
          ),
      },
      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin, Role.Manager, Role.Employee, Role.Contractor] },
        path: "time-tracking",
        loadComponent: () =>
          import("./views/time-tracking-v2/time-tracking.component").then(
            (mod) => mod.TimeTrackingComponent
          ),
      },

      {
        path: "reports",
        loadComponent: () =>
          import("./views/report/report.component").then((mod) => mod.ReportComponent),
      },

      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin, Role.Manager] },
        path: "items-and-services",
        loadComponent: () =>
          import("./views/item-services/item-services.component").then((mod) => mod.ItemServicesComponent),
      },
      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin] },
        path: "settings",
        children: [
          {
            path: "",
            pathMatch: "full",
            loadComponent: () =>
              import("./views/settings/settings.component").then(
                (mod) => mod.SettingsComponent
              ),
          },

          {
            path: "category",
            loadComponent: () =>
              import("./views/settings/category/category.component").then(
                (mod) => mod.CategoryComponent
              ),
          },

          {
            path: "financial",
            loadComponent: () =>
              import("./views/settings/financial/financial.component").then(
                (mod) => mod.FinancialComponent
              ),
          },
          {
            path: "business",
            loadComponent: () =>
              import("./views/settings/business/business.component").then(
                (mod) => mod.BusinessComponent
              ),
          },
          {
            path: "permission",
            loadComponent: () =>
              import("./views/settings/role-permission-project/role-permission-project.component").then(
                (mod) => mod.RolePermissionProjectComponent
              ),
          },
          {
            path: "account",
            loadComponent: () =>
              import("./views/settings/account/account.component").then(
                (mod) => mod.AccountComponent
              ),
          },
        ],
      },
      {
        canActivate: [RoleGuard],
        data: { roles: [Role.All] },
        path: "profile",
        loadComponent: () =>
          import("./views/profile/profile.component").then(
            (mod) => mod.ProfileComponent
          ),
      },
      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin, Role.Manager] },
        path: "invite-members",
        children: [
          {
            path: "",
            pathMatch: "full",
            loadComponent: () =>
              import("./views/members/invite-member/invite-member.component").then(
                (mod) => mod.InviteMemberComponent
              ),
          },


        ],
      },

      {
        canActivate: [RoleGuard],
        data: { roles: [Role.Admin] },
        path: "members",
        children: [
          {
            path: "",
            pathMatch: "full",
            loadComponent: () =>
              import("./views/members/member/member.component").then(
                (mod) => mod.MemberComponent
              ),
          },
          {
            path: "detail/:id",
            loadComponent: () =>
              import("./views/members/detail-member/detail-member.component").then(
                (mod) => mod.DetailMemberComponent
              ),
          },

        ],
      },
    ],
  },
  {
    path: "link/:id",
    loadComponent: () =>
      import("./views/invoice/load-link-invoice/load-link-invoice.component").then(
        (mod) => mod.LoadLinkInvoiceComponent
      ),
  },
  {
    path: "login",
    loadComponent: () =>
      import("./auth/login/login.component").then((mod) => mod.LoginComponent),
  },
  {
    path: "do-login",
    loadComponent: () =>
      import("./auth/document-cannot-login/document-cannot-login.component").then((mod) => mod.DocumentCannotLoginComponent),
  },
  {
    path: 'activate-invite',
    loadComponent: () =>
      import('./views/members/activate-member/activate-member.component').then((mod) => mod.ActivateMemberComponent),
  },
  {
    path: 'sign-up',
    loadComponent: () =>
      import('./auth/register/register.component').then((mod) => mod.RegisterComponent),
  },

  {
    path: 'reset-password',
    loadComponent: () =>
      import('./auth/reset-password/reset-password.component').then(
        (mod) => mod.ResetPasswordComponent
      ),
  },
  {
    path: 'forgot-password',
    loadComponent: () =>
      import('./auth/forgot-password/forgot-password.component').then(
        (mod) => mod.ForgotPasswordComponent
      ),
  },
  {
    path: '**',
    redirectTo: '',
    canActivate: [RoleGuard],
    data: { roles: [Role.All] },
  }

];
