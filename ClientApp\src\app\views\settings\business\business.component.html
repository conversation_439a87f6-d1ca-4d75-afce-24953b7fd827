<div class="w-full py-[24px] border-b border-border-primary bg-bg-primary">
  <div class="container-full">
    <div class="flex items-center gap-[8px]">
      <button class="button-icon button-size-md" (click)="handleBack()">
        <img src="../../../../assets/img/icon/ic_arrow_left.svg" alt="Icon">
      </button>
      <p class="text-text-primary text-headline-lg-bold">
        {{'SETTINGS.BasicInformation.Title'| translate}}
      </p>
    </div>
  </div>
</div>

<div class="container-full py-[20px] bg-white">
  <div class="w-[160px] shrink-0 mb-2 md:mx-[unset]">
    <app-inno-upload
      [canEdit]="true"
      [isBussiness]="true"
      [imageUrl]="imageUrl"
      onerror="this.src='../../../../assets/img/image_default.svg'"
      (onChange)="handleChangePicture($event)" />
  </div>
  <form [formGroup]="businessForm"
    class="flex flex-col w-full mx-auto gap-[16px]" (ngSubmit)="onSubmit()">
    <app-inno-form-input
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.BusinessName'| translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.BusinessNamePlaceholder' | translate"
      [formControl]="f['business']"
      [value]="f['business'].value"
      [errorMessages]="{required: 'SETTINGS.BasicInformation.BasicInformationForm.BusinessNameRequired' | translate}" />

    <app-inno-form-input
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.EmailBusiness' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.EmailPlaceholder' | translate"
      type="email"
      [formControl]="f['email']"
      [value]="f['email'].value"
      [errorMessages]="{
          required: 'SETTINGS.BasicInformation.BasicInformationForm.EmailRequired' | translate,
          email: 'SETTINGS.BasicInformation.BasicInformationForm.InvalidEmail' | translate
        }" />

    <app-inno-form-input
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.PhoneNumber' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.PhonePlaceholder' | translate"
      [formControl]="f['phone']"
      [value]="f['phone'].value"
      mask="(*************"
      autocomplete="phone"
      pattern="[0-9]*"
      [errorMessages]="{
          required: ('SETTINGS.BasicInformation.BasicInformationForm.PhoneRequired' | translate),
          minlength: ('SETTINGS.BasicInformation.BasicInformationForm.PhoneLength' | translate),
          maxlength: ('SETTINGS.BasicInformation.BasicInformationForm.PhoneLength' | translate),
          pattern: ('SETTINGS.BasicInformation.BasicInformationForm.PhonePattern' | translate)
        }" />

    <app-inno-form-select-search
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.Country' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.Country' | translate"
      [options]="countriesOption"
      [formControl]="f['country']"
      [value]="f['country'].value"
      [errorMessages]="{ required: 'SETTINGS.BasicInformation.BasicInformationForm.CountryRequired' | translate }" />

    <app-inno-form-input
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.AddressLine1' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.AddressLine1Placeholder' | translate"
      [formControl]="f['address1']"
      [value]="f['address1'].value" />

    <app-inno-form-input
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.AddressLine2' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.AddressLine2Placeholder' | translate"
      [formControl]="f['address2']"
      [value]="f['address2'].value" />

    <app-inno-form-input
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.TownCity' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.TownCityPlaceholder' | translate"
      [formControl]="f['tow_city']"
      [value]="f['tow_city'].value" />

    <app-inno-form-input
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.StateProvince' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.StateProvincePlaceholder' | translate"
      [formControl]="f['state_povince']"
      [formControl]="f['state_povince']"
      [value]="f['state_povince'].value" />

    <app-inno-form-input
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.PostalCode' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.PostalCodePlaceholder' | translate"
      [formControl]="f['postal_code']"
      [value]="f['postal_code'].value" />

    <app-inno-form-select-search
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.StartWeekOn' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.StartWeekOnPlaceholder' | translate"
      [options]="startWeekOption"
      [isDisableSearch]="true"
      [formControl]="f['start_week_on']"
      [value]="f['start_week_on'].value" />

    <app-inno-form-select-search
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.Timezone' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.PlaceholderTimezone' | translate"
      [options]="timezoneOption"
      [formControl]="f['timezone']"
      [value]="f['timezone'].value" />

    <app-inno-form-select-search
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.DateFormat' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.PlaceholderDateFormat' | translate"
      [options]="dateFormatOption"
      [isDisableSearch]="true"
      [formControl]="f['dateFormat']"
      [value]="f['dateFormat'].value" />

    <app-inno-form-textarea
      [label]="'SETTINGS.BasicInformation.BasicInformationForm.Note' | translate"
      [placeholder]="'SETTINGS.BasicInformation.BasicInformationForm.NotePlaceholder' | translate"
      [formControl]="f['note']"
      [value]="f['note'].value" />

    <div class="w-full flex justify-center mt-[20px]">
      <button
        type="submit"
        class="button-primary button-size-md px-[20px]">
        {{'BUTTON.Save'| translate}}
      </button>
    </div>
  </form>
</div>
