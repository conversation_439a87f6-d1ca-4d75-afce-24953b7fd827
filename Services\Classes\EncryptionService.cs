using InnoBook.Common;
using InnoBook.Entities;
using InnoBook.Funtion;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using Npgsql;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory.Database;

namespace InnoBook.Services.Classes
{
    public class EncryptionService : IEncryptionService
    {
        private readonly InnoLogicielContext _context;
        private readonly IConfiguration _configuration;
        private readonly string _encryptionKey;

        public class EncryptedResult
        {
            public string EncryptedData { get; set; }
        }

        public EncryptionService(InnoLogicielContext context, IConfiguration configuration)
        {
            _context = context;
            _configuration = configuration;
            _encryptionKey = _configuration.GetSection("ENCRYPTION_KEY").Value 
                ?? throw new InvalidOperationException("ENCRYPTION_KEY not found in configuration");
        }

        public async Task<string?> EncryptAsync(string? plainText)
        {
            if (string.IsNullOrEmpty(plainText))
                return plainText;

            try
            {
                var connection = _context.Database.GetDbConnection() as NpgsqlConnection;
                if (connection == null)
                    throw new InvalidOperationException("Database connection is not NpgsqlConnection");

                if (connection.State != System.Data.ConnectionState.Open)
                    await connection.OpenAsync();

                await using var command = new NpgsqlCommand("SELECT pgp_sym_encrypt($1, $2)", connection);
                command.Parameters.AddWithValue(plainText);
                command.Parameters.AddWithValue(_encryptionKey);

                // Execute the query and retrieve the result
                var result = await command.ExecuteScalarAsync();

                return Convert.ToBase64String(result as byte[]);
            }
            catch (Exception ex)
            {
                // Check for specific pgcrypto extension error
                if (ex.Message.Contains("function pgp_sym_encrypt") && ex.Message.Contains("does not exist"))
                {
                    throw new InvalidOperationException(
                        "PostgreSQL pgcrypto extension is not enabled. " +
                        "Please run the database setup script: Scripts\\setup-database.bat", ex);
                }

                // Log the exception (you might want to use a proper logging framework)
                Console.WriteLine($"Encryption failed: {ex.Message}");
                throw new InvalidOperationException("Failed to encrypt data", ex);
            }
        }

        public async Task<string?> DecryptAsync(string? encryptedText)
        {
            if (string.IsNullOrEmpty(encryptedText))
                return encryptedText;

            // Check if the string is valid Base64 before attempting to decrypt
            if (!EncryptionHelper.IsValidBase64String(encryptedText))
            {
                // If it's not Base64, assume it's already plain text (not encrypted)
                return encryptedText;
            }

            try
            {
                // Convert Base64 back to bytes
                var encryptedBytes = Convert.FromBase64String(encryptedText);

                // Use PostgreSQL pgcrypto to decrypt the data
                var sql = "SELECT pgp_sym_decrypt({0}, {1})";
                var result = await _context.Database
                    .SqlQueryRaw<string>(sql, encryptedBytes, _encryptionKey)
                    .FirstOrDefaultAsync();

                return result;
            }
            catch (Exception ex)
            {
                // Check for specific pgcrypto extension error
                if (ex.Message.Contains("function pgp_sym_decrypt") && ex.Message.Contains("does not exist"))
                {
                    throw new InvalidOperationException(
                        "PostgreSQL pgcrypto extension is not enabled. " +
                        "Please run the database setup script: Scripts\\setup-database.bat", ex);
                }

                // Log the exception
                Console.WriteLine($"Decryption failed: {ex.Message}");
                // If decryption fails, return the original text (might be plain text)
                return encryptedText;
            }
        }

        public async Task<User> EncryptUserAsync(User user)
        {
            if (user == null) return user;

            user.FirstName = await EncryptAsync(user.FirstName);
            user.LastName = await EncryptAsync(user.LastName);

            return user;
        }

        public async Task<User> DecryptUserAsync(User user)
        {
            if (user == null) return user;

            user.FirstName = await DecryptAsync(user.FirstName);
            user.LastName = await DecryptAsync(user.LastName);

            return user;
        }

        public async Task<Client> EncryptClientAsync(Client client)
        {
            if (client == null) return client;

            client.FirstName = await EncryptAsync(client.FirstName);
            client.LastName = await EncryptAsync(client.LastName);
            client.PhoneNumber = await EncryptAsync(client.PhoneNumber);
            client.BusinessPhoneNumber = await EncryptAsync(client.BusinessPhoneNumber);
            client.MobilePhoneNumber = await EncryptAsync(client.MobilePhoneNumber);
            client.AddressLine1 = await EncryptAsync(client.AddressLine1);
            client.AddressLine2 = await EncryptAsync(client.AddressLine2);

            return client;
        }

        public async Task<Client> DecryptClientAsync(Client client)
        {
            if (client == null) return client;

            client.FirstName = await DecryptAsync(client.FirstName);
            client.LastName = await DecryptAsync(client.LastName);
            client.PhoneNumber = await DecryptAsync(client.PhoneNumber);
            client.BusinessPhoneNumber = await DecryptAsync(client.BusinessPhoneNumber);
            client.MobilePhoneNumber = await DecryptAsync(client.MobilePhoneNumber);
            client.AddressLine1 = await DecryptAsync(client.AddressLine1);
            client.AddressLine2 = await DecryptAsync(client.AddressLine2);

            return client;
        }

        public async Task<Company> EncryptCompanyAsync(Company company)
        {
            if (company == null) return company;

            company.Phone = await EncryptAsync(company.Phone);
            company.Adress = await EncryptAsync(company.Adress);
            company.Adress2 = await EncryptAsync(company.Adress2);

            return company;
        }

        public async Task<Company> DecryptCompanyAsync(Company company)
        {
            if (company == null) return company;

            company.Phone = await DecryptAsync(company.Phone);
            company.Adress = await DecryptAsync(company.Adress);
            company.Adress2 = await DecryptAsync(company.Adress2);

            return company;
        }


    }
}
