import { CdnService } from './../../../service/cdn.service';
import { DecimalPipe } from './../../../pipes/decimal.pipe';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { ToastService } from './../../../service/toast.service';
import { MerchantService } from './../../../service/merchant.service';
import { StoreService } from 'app/service/store.service';
import { SharedModule } from 'app/module/shared.module';
import { CurrencyPipe } from '@angular/common';
import { Component, DestroyRef, Inject, inject, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { AvatarModule } from 'ngx-avatars';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Subscription } from 'rxjs';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoFormSelectSearchComponent } from 'app/component/inno-form-select-search/inno-form-select-search.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { InnoFormTextareaComponent } from 'app/component/inno-form-textarea/inno-form-textarea.component';
import { InnoSelectSearchProjectComponent } from 'app/component/inno-select-search-project/inno-select-search-project.component';
import { DropdownOptionsService } from 'app/service/dropdown-options.service';
import { InnoFormDatepickerComponent } from 'app/component/inno-form-datepicker/inno-form-datepicker.component';
import { ExpensesService } from 'app/service/expenses.service';
import { Expenses } from '../../../dto/interface/expenses.interface';
import { AddCategoryExpensesDialog } from '../../../service/dialog/add-category-expenses.dialog';
import { ModifyTaxesDialog } from '../../../service/dialog/modify-taxes.dialog';
import { SpinnerService } from 'app/service/spinner.service';
import { ModifyExpenseItemDialog } from 'app/service/dialog/modify-expense-item.dialog';
import { calculateGroupedTaxes, calculateTotalInvoiceItem, getNameTaxes } from 'app/utils/invoice.helper';
import { SizePipe } from 'app/pipes/size.pipe';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-new-expense-form',
  standalone: true,
  imports: [
    AvatarModule,
    SharedModule,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormSelectSearchComponent,
    InnoFormTextareaComponent,
    InnoFormDatepickerComponent,
    InnoSelectSearchProjectComponent,
    InnoFormInputComponent,
    InnoTableActionComponent,
    FormatNumberPipe,
    DecimalPipe,
    SizePipe
  ],
  providers: [CurrencyPipe],
  templateUrl: './new-expense-form.component.html',
  styleUrl: './new-expense-form.component.scss'
})
export class NewExpenseFormComponent implements OnInit, OnDestroy {
  public title: string = "EXPENSES.NewExpenseButton"
  public merchantOptions: IFilterDropdownOption[] = []
  public projectAndClientOptions: IFilterDropdownOption[] = []
  public categoryOptions: IFilterDropdownOption[] = []
  public newpExpensesForm!: UntypedFormGroup;
  public subtotal: number = 0
  public Attachment: any[] = [];
  public totalAmount: number = 0
  public sumtax: number = 0;
  public calculateTotalInvoiceItem = calculateTotalInvoiceItem
  public getNameSelectedTaxes = getNameTaxes

  private _subscriptions: Subscription[] = [];
  private formBuilder = inject(UntypedFormBuilder)
  private destroyRef = inject(DestroyRef);
  private _spinnerService = inject(SpinnerService)
  private _toastService = inject(ToastService)
  public _storeService = inject(StoreService)
  public _authenticationService = inject(AuthenticationService)
  private _merchantService = inject(MerchantService)
  private dropdownOptionService = inject(DropdownOptionsService)
  public _expeneseService = inject(ExpensesService)
  private cdnService = inject(CdnService)


  @ViewChild('selectSearchClientElement') selectSearchClientElement!: InnoFormSelectSearchComponent;
  @ViewChild('selectSearchCategoryElement') selectSearchCategoryElement!: InnoFormSelectSearchComponent;

  static getComponent(): typeof NewExpenseFormComponent {
    return NewExpenseFormComponent;
  }


  constructor(
    private translate: TranslateService,
    public dialogRef: MatDialogRef<NewExpenseFormComponent>,
    private addCategoryExpensesDialog: AddCategoryExpensesDialog,
    private modifyExpensesItemDialog: ModifyExpenseItemDialog,
    private modifyTaxesDialog: ModifyTaxesDialog,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    if (this.data) {
      this.title = "EXPENSES.UpdateExpense"
    }
    this.newpExpensesForm = this.formBuilder.group({
      expensesName: ['', Validators.compose([Validators.required])],
      projectId: [''],
      clientId: [''],
      categoryId: [''],
      categoryItemId: ['', Validators.compose([Validators.required])],
      merchantId: ['', Validators.compose([Validators.required])],
      date: [null, Validators.compose([Validators.required])],
      paidAmount: [''],
      note: [''],
      filename: [""],
      attachments: [[]],
      itemExpenses: [[]],
      taxes: [[]],
      base64: [''],
    });
    this.newpExpensesForm.get('itemExpenses')?.valueChanges.subscribe(listInvoice => {
      this.subtotal = 0
      listInvoice?.forEach((invoiceItem: any) => {
        // Exclude taxes
        const totalInvoiceItem = calculateTotalInvoiceItem(invoiceItem?.rate, invoiceItem?.qty);
        this.subtotal += totalInvoiceItem
      })

      // Calculate with discount if exist
      this.calculateAllTax();

    });
  }
  handleClose() {
    this.dialogRef.close();
  }

  calculateAllTax() {
    this.sumtax = 0;
    const resultTax = calculateGroupedTaxes(this.f['itemExpenses'].value)
    this.sumtax = resultTax.grandTotalTax;
    this.totalAmount = this.subtotal + this.CheckIsNaN(this.sumtax)
  }

  CheckIsNaN(value: any) {
    if (isNaN(value)) {
      return 0
    }
    return value;

  }
  reloadListMerchantOptions() {
    this.dropdownOptionService.getDropdownOptionsMerchant()
      .then((merchantOptions) => {
        this.merchantOptions = merchantOptions
      })
  }
  reloadListCategoryOptions() {
    this.dropdownOptionService.getDropdownOptionsCategories()
      .then((categoryOptions) => {
        this.categoryOptions = categoryOptions
      })
  }

  handleDataEdit(data: any) {
    // Set existing taxes is selected
    data.itemExpenses.forEach(item => {
      item.taxes.forEach((tax: any) => {
        tax.selected = true;
      });
    });
    this.f['expensesName'].setValue(data?.expensesName);
    this.f['projectId'].setValue(data?.projectId);
    this.f['clientId'].setValue(data?.clientId);
    this.f['categoryId'].setValue(data?.categoryId);
    this.f['categoryItemId'].setValue(data?.categoryItemId);
    this.f['merchantId'].setValue(data?.merchantId);
    this.f['date'].setValue(data?.date);
    this.f['paidAmount'].setValue(data?.paidAmount || 0);
    this.f['note'].setValue(data?.note);
    this.f['attachments'].setValue(data?.attachments);
    this.f['itemExpenses'].setValue(data?.itemExpenses);
    this.totalAmount = data?.paidAmount ?? 0;
    this.Attachment = data?.attachments ?? [];
  }

  GetExpensesById(id) {
    this._expeneseService.GetExpensesById(id)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((res: any) => {
        if (res) {
          this.handleDataEdit(res)
        }
      })
  }
  ngOnInit(): void {
    this.reloadListMerchantOptions()
    Promise.all([
      this.dropdownOptionService.getDropdownOptionsCategories(),
      this.dropdownOptionService.getDropdownOptionsProjectAndClient()
    ]).then(([categoryOptions, projectAndClientOptions]) => {
      this.categoryOptions = categoryOptions
      this.projectAndClientOptions = projectAndClientOptions
    })
    this.GetExpensesById(this.data)
  }

  get f() {
    return this.newpExpensesForm.controls as Record<string, FormControl>;;
  }

  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  handleSubmit() {
    if (this.newpExpensesForm.invalid) {
      this.markAllControlsAsTouched()
      return
    }
    this._spinnerService.show();
    const payload: Expenses = {
      expensesName: this.f['expensesName'].value,
      clientId: this.f['clientId'].value,
      projectId: this.f['projectId'].value,
      categoryId: this.f['categoryId'].value,
      categoryItemId: this.f['categoryItemId'].value,
      date: this.f['date'].value,
      merchantId: this.f['merchantId'].value,
      note: this.f["note"].value,
      paidAmount: this.totalAmount,
      itemExpense: this.f['itemExpenses'].value.map(item => ({
        ...item,
        taxes: item.taxes.some(tax => tax.companyTax)
          ? item.taxes.map(({ companyTax, ...rest }) => rest)
          : item.taxes.filter(tax => tax.selected)
      })),
      base64: undefined,
      attachments: this.Attachment,
      filename: undefined
    }

    if (this.data) {
      payload['id'] = this.data
      this._expeneseService.UpdateExpenses(payload)
        .pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
          if (res) {
            this._spinnerService.hide();
            this.dialogRef.close(res);
          }
        })
    } else {

      this._expeneseService.CreateExpenses(payload)
        .pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
          next: (res) => {
            if (res) {
              this._spinnerService.hide();
              this.dialogRef.close(res);
            }
          }
        })
    }
  }

  handleCancel() {
    this.dialogRef.close();
  }

  handleSelectProject(item: IFilterDropdownOption) {
    if (item.metadata?.type != 'project') {
      this.f['clientId'].setValue(item.metadata?.client?.id);
      this.f['projectId'].setValue(null)
      this.selectSearchClientElement.handleCloseSearchResult()
      return
    }

    this.f['projectId'].setValue(item.value)
    this.f['clientId'].setValue(item.metadata?.objectClient?.id)
    this.selectSearchClientElement.handleCloseSearchResult()
  }

  handleSelectCategory(item: IFilterDropdownOption) {
    if (item.metadata?.type != 'childCategory') return

    this.f['categoryItemId'].setValue(item.value)
    this.f['categoryId'].setValue(item.metadata?.parentCategory?.id)
    this.selectSearchCategoryElement.handleCloseSearchResult()
  }

  handleCreateNewMerchant(newMerchantName?: string) {
    if (!newMerchantName?.length) return

    const payload = {
      merchantName: newMerchantName
    }
    this._merchantService.CreateMerchant(payload)
      .pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
        if (res) {
          this.f['merchantId'].setValue(res.id);
          this.reloadListMerchantOptions();
          this._toastService.showSuccess(this.translate.instant("TOAST.Create"), this.translate.instant("TOAST.Success"));
        }
      }
      )
  }
  handleCreateCategory($event: any) {
    this.OpenDialogCategory($event);
  }

  OpenDialogCategory(item: any) {
    const dialogRef = this.addCategoryExpensesDialog.open({ item });
    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (res) {
          this.reloadListCategoryOptions();
        }
      })
    });
  }

  handleDeleteInvoiceItem(index: number) {
    const currentPaymentInvoice = [...(this.f['itemExpenses']?.value ?? [])]
    if (!currentPaymentInvoice?.length) return

    currentPaymentInvoice.splice(index, 1)
    this.f['itemExpenses'].setValue(currentPaymentInvoice)
  }

  handleModifyExpenseItem(index?: number, item?: any) {

    const dialogRef = this.modifyExpensesItemDialog.open(item);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) return
        const currentPaymentInvoice = this.f['itemExpenses'].value ?? []
        if (index === undefined) {
          currentPaymentInvoice.push(res)
          this.f['itemExpenses'].setValue(currentPaymentInvoice)

        }
        else {
          currentPaymentInvoice[index] = res
          this.f['itemExpenses'].setValue(currentPaymentInvoice)
        }
        if (this._storeService.get_ApplyTaxAll()) {
          this._storeService.set_ApplyTaxAll(false)
          let temp = this.f['itemExpenses'].value
          temp.forEach((element: any) => {
            if (element.taxes.length > 0) {
              res.taxes.filter(x => x.selected == true).forEach((item: any) => {
                const exists = element.taxes.some((existingTax: any) => {
                  if (existingTax.companyTax) {
                    return existingTax?.companyTax.name === item?.name;
                  }
                  return existingTax.name === item.name;
                });
                if (!exists) {
                  element.taxes.push(item);
                }
              });
            } else {
              res.taxes.forEach((item: any) => {
                element.taxes.push(item)
              });
            }


          });

        }
      })
    });
  }
  handleModifyTaxes(item: any, index: number) {
    const dialogRef = this.modifyTaxesDialog.open(item);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) {

          return;
        }
        const currentPaymentInvoice = this.f['itemExpenses'].value ?? []
        currentPaymentInvoice[index].taxes = res["taxes"].filter(c => c.selected);

        this.f['itemExpenses'].setValue(currentPaymentInvoice)
        if (this._storeService.get_ApplyTaxAll()) {
          this._storeService.set_ApplyTaxAll(false)
          let temp = this.f['itemExpenses'].value
          temp.forEach((element: any) => {
            const selectedTaxes = res.taxes.filter((tax: any) => tax.selected);

            if (element.taxes.length > 0) {
              const existingTaxNames = new Set(element.taxes.map((t: any) => t.name));
              selectedTaxes.forEach((tax: any) => {
                if (!existingTaxNames.has(tax.name)) {
                  element.taxes.push(tax);
                }
              });
            } else {
              element.taxes.push(...res.taxes);
            }
          });
        }
      })
    });
  }

  RemoveChoseFile(index: number) {
    this.Attachment.splice(index, 1);
  }
  onSelectFile(event: any) {

    if (event.target.files && event.target.files[0]) {
      var file_name = event.target.files;
      var filesAmount = event.target.files.length;

      for (let i = 0; i < filesAmount; i++) {
        var file = event.target.files[i];

        // Check if file size exceeds 5MB (5 * 1024 * 1024 bytes)
        if (file.size > 5 * 1024 * 1024) {
          let description = this.translate.getLangs()[0] == 'en' ? `File ${file.name} is larger than 5MB and will not be uploaded` : `Le fichier ${file.name} dépasse 5 Mo et ne sera pas téléversé`
          this._toastService.showWarning(this.translate.instant("TOAST.SizeFile"), description)
          continue; // Skip this file
        }
        var reader = new FileReader();
        reader.onload = (event) => {
          this.Attachment.push({ base64: event.target.result, filename: file_name[i].name, type: file_name[i].type, size: file_name[i].size });
        }
        reader.readAsDataURL(event.target.files[i]);
      }
    }

  }
  handleDowload(item: any) {
    this._spinnerService.show();
    this.cdnService.GetFile(item.filename).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        const blob = new Blob([res], { type: item.type });
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = item.filename;
        document.body.appendChild(link);
        this._spinnerService.hide();
        link.click();
        document.body.removeChild(link);
      }
    }
    )
  }
  ngOnDestroy(): void {
    this._subscriptions.forEach((sb) => sb.unsubscribe());
  }
}
;
