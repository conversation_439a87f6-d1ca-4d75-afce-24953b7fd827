import { DataService } from 'app/service/data.service';
import { InnoFormTextareaComponent } from './../../../../../component/inno-form-textarea/inno-form-textarea.component';
import { DecimalPipe } from './../../../../../pipes/decimal.pipe';
import { Component, effect, EffectRef, inject, Inject, OnDestroy } from '@angular/core';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { SharedModule } from 'app/module/shared.module';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { ModifyTaxesDialog } from '../../../../../service/dialog/modify-taxes.dialog';
import { DatePipe } from '@angular/common';
import { User } from 'app/dto/interface/user.interface';
import { StoreService } from 'app/service/store.service';
import { calculateTotalInvoiceItem, getNameTaxes } from 'app/utils/invoice.helper';

@Component({
  selector: 'app-modify-invoice-item',
  templateUrl: './modify-invoice-item.component.html',
  styleUrls: ['./modify-invoice-item.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormInputComponent,
    InnoFormTextareaComponent,
    FormatNumberPipe,
    DecimalPipe
  ]
})
export class ModifyInvoiceItemComponent implements OnDestroy {
  public invoiceItemForm!: UntypedFormGroup;
  today = new Date();
  public listTaxName?: string = ''
  inforUser: User
  title: string;
  private cleanupEffect: EffectRef;
  private formBuilder = inject(UntypedFormBuilder)
  private dataService = inject(DataService)
  private storeService = inject(StoreService)

  static getComponent(): typeof ModifyInvoiceItemComponent {
    return ModifyInvoiceItemComponent;
  }
  constructor(
    public dialogRef: MatDialogRef<ModifyInvoiceItemComponent>,
    private modifyTaxesDialog: ModifyTaxesDialog,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.cleanupEffect = effect(() => {
      const hasData = this.data;
      const isEstimate = this.dataService.isEstimate();
      if (hasData && isEstimate) {
        this.title = "ITEM_INVOICE.EditEstimateItem"
      } else if (hasData && !isEstimate) {
        this.title = "ITEM_INVOICE.EditInvoiceItem"
      } else if (!hasData && isEstimate) {
        this.title = "ITEM_INVOICE.NewEstimateItem"
      } else {
        this.title = "ITEM_INVOICE.NewInvoiceItem"
      }
    });
    this.inforUser = this.data?.inforUser ?? this.storeService.get_InforUser();
    const description = this.data ? this.createDescription(this.data) : "";

    const { rate, qty, taxes } = data || {}

    // Set default selected
    const newTaxes = taxes?.map(c => ({ ...c, selected: true })) ?? [];

    this.invoiceItemForm = this.formBuilder.group({
      description: [description, []],
      rate: [rate, Validators.compose([Validators.required])],
      qty: [qty, Validators.compose([Validators.required])],
      taxes: [newTaxes],
    });
    if (newTaxes) {
      this.listTaxName = getNameTaxes(newTaxes)
    }

    this.invoiceItemForm.valueChanges.subscribe((values) => {
      this.listTaxName = getNameTaxes(values.taxes)

    });
  }
  ngOnDestroy(): void {
    if (this.cleanupEffect) {
      this.cleanupEffect.destroy();
    }
  }
  getFullName() {
    if (this.inforUser?.firstName && this.inforUser?.lastName) {
      return this.inforUser.firstName + " " + this.inforUser.lastName
    }
    else {
      return this.inforUser?.email ?? ""
    }

  }
  createDescription(data: any): string {
    if (data?.position == 0 || data?.id) {
      const description = data?.description;
      return description;
    }
    else {
      const datePipe = new DatePipe('en-US');
      const projectName = data?.projectName ?? "";
      const itemName = data?.itemName ?? "";
      const description = data?.description;
      const serviceName = data?.serviceName ?? "";
      const expensesName = data?.expensesName;
      const formattedDate = datePipe.transform(data?.dateSelectItem, 'MMM, d yyyy');
      const user = this.getFullName()
      const descriptionParts = [projectName, itemName, serviceName, expensesName, `${user}- ${formattedDate}`, description];
      const descriptionnew = descriptionParts.filter(part => part != null && part !== "").join('\n');

      return descriptionnew;
    }
  }
  handleClose() {
    this.dialogRef.close();
  }

  get f() {
    return this.invoiceItemForm.controls as Record<string, FormControl>;;
  }

  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  handleCancel() {
    this.dialogRef.close();
  }

  handleSubmit() {
    if (this.invoiceItemForm.invalid) {
      this.markAllControlsAsTouched();
      return;
    }

    const payload: Record<string, any> = {
      description: this.f['description'].value,
      rate: this.f['rate'].value,
      qty: this.f['qty'].value,
      taxes: this.f['taxes'].value,
      date: this.today,
      position: this.data?.position ?? 0,
      dateSelectItem: this.data?.date ?? this.today,
      trackingId: this.data?.trackingId,
      serviceId: this.data?.serviceId,
      projectId: this.data?.projectId,
      projectName: this.data?.projectName,
      expensesName: this.data?.expensesName,
      expensesId: this.data?.expensesId,
    }
    if (this.data?.id) {
      payload["id"] = this.data.id
    }

    this.dialogRef.close(payload)
  }

  handleModifyTaxes() {
    const Taxes = this.f["taxes"]?.value
      ?.filter(tax => tax.companyTax)
      .map(tax => ({
        ...tax.companyTax,
        selected: true
      }));
    const updatedTaxes = [...Taxes, ...this.f["taxes"]?.value];
    const dialogRef = this.modifyTaxesDialog.open(updatedTaxes.filter(x => x.selected) ?? []);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res?.taxes) return
        this.f["taxes"].setValue(res.taxes)
      })
    });
  }
}

