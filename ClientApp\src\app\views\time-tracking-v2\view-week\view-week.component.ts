import { EditTimeTrackingWeeekDialog } from './../../../service/dialog/edit-time-tracking-weeek.dialog';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { ToastService } from 'app/service/toast.service';
import { Component, DestroyRef, inject, OnDestroy, OnInit } from '@angular/core';
import { GridAllModule, Pager, PagerDropDown, PagerModule } from '@syncfusion/ej2-angular-grids';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { TimeTrackingViewEnum } from 'app/enum/time-tracking.enum';
import { getStartAndEndOfWeek, getWeekDays, isSameDate, sumHours } from 'app/helpers/common.helper';
import { SharedModule } from 'app/module/shared.module';
import { DataService } from 'app/service/data.service';
import { concatMap, from, Subscription, switchMap } from 'rxjs';
import { TimeTrackingProvider } from '../time-tracking.provider';
import { DialogAddTimeComponent } from '../dialog-add-time/dialog-add-time.component';
import { MatDialog } from '@angular/material/dialog';
import { DialogModule } from '@angular/cdk/dialog';
import { TranslateService } from '@ngx-translate/core';
import { TimetrackingService } from 'app/service/timetracking.service';
import { Parameter, TimeTrackingQueryParam } from 'app/dto/interface/queryParameter.interface';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-view-week',
  templateUrl: './view-week.component.html',
  styleUrls: ['./view-week.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    PagerModule,
    DialogModule,
    GridAllModule,
    InnoSpinomponent,
    InnoPopoverComponent
  ]
})
export class ViewWeekComponent implements OnInit, OnDestroy {
  public isRuning: boolean = false;
  public dataSource: any[] | null = null
  public isLoading: boolean = false
  public dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"];
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  public pageSizesDefault: number = 10
  private startOfWeek?: string;
  private endOfWeek?: string;
  private dataService = inject(DataService)
  private timeTrackingProvider = inject(TimeTrackingProvider)
  private timeTrackingServices = inject(TimetrackingService)
  private toastService = inject(ToastService)
  private editTimeTrackingWeeekDialog = inject(EditTimeTrackingWeeekDialog)
  private destroyRef = inject(DestroyRef);
  private translate = inject(TranslateService);
  private layoutUtilsService = inject(LayoutUtilsService)
  private unsubscribe: Subscription[] = [];
  customStyles: any = { 'padding-bottom': '100px' };
  constructor() { }

  ngOnInit(): void {
    // Refresh list
    if (localStorage.getItem('isRunning') && localStorage.getItem('isRunning') == 'true') {
      this.isRuning = true
    }
    else {
      this.isRuning = false
    }
    this.unsubscribe.push(
      this.dataService.GetTimeTrackingFilter()
        .subscribe((filter) => {
          if (filter.typeView !== TimeTrackingViewEnum.Week) return

          const week = getStartAndEndOfWeek(filter.dateSelected)
          const isSameWeek = this.startOfWeek === week.startOfWeek && this.endOfWeek === week.endOfWeek

          this.startOfWeek = week.startOfWeek
          this.endOfWeek = week.endOfWeek

          if (!isSameWeek) this.isLoading = true
          this.timeTrackingProvider.reloadTimeTrackingData().subscribe({
            next: (res) => {
              const data = res?.data ?? []
              if (!this.startOfWeek || !this.endOfWeek) return

              const weekDays = getWeekDays(this.startOfWeek, this.endOfWeek)
              const clientMap = new Map<string, any>()
              data.forEach((item: any) => {
                const clientItem = item.client
                if (!clientItem) return

                if (clientMap.has(clientItem.id)) return
                clientMap.set(clientItem.id, { client: clientItem })
              })

              const rowsBody = Array.from(clientMap.values());
              rowsBody.forEach((item: any) => {
                const clientId = item.client.id
                const allTrackingOfClient = data.filter((e: any) => e.client?.id === clientId)

                const groupHoursByDate: Record<string, any> = {}
                Object.entries(weekDays).forEach(([dayName, date]) => {
                  const listTrackingOfDate = allTrackingOfClient.filter((e: any) => isSameDate(new Date(date), new Date(e.date)))
                  const listHoursFromTracking = listTrackingOfDate.map((e: any) => (e.endTime ?? '00:00:00'))
                  const hours = sumHours(listHoursFromTracking)
                  groupHoursByDate[dayName] = { hours }
                })

                const totalHoursWeek = sumHours(Object.values(groupHoursByDate).map(e => e.hours))

                item.listHoursDay = groupHoursByDate
                item.totalHoursWeek = totalHoursWeek
              })

              const rowHeader = this.dayNames.reduce((acc, dayName) => {
                const _date = weekDays[dayName]

                const listHourOfDay = rowsBody.reduce((arrHour, item) => {
                  const _hoursByDayName = item.listHoursDay?.[dayName]?.hours ?? '00:00:00'
                  arrHour.push(_hoursByDayName)
                  return arrHour
                }, [])

                return {
                  ...acc,
                  [dayName]: {
                    shortName: dayName.slice(0, 3),
                    shortDate: _date.split('/')[1],
                    totalHoursDay: sumHours(listHourOfDay)
                  }
                }
              }, {})

              this.dataSource = [rowHeader, ...rowsBody]
            },
            complete: () => {
              this.isLoading = false
            }
          })
        })
    )
  }

  onPageChange(event: any): void { }

  getData(item: any) {
    return JSON.stringify(item)
  }

  public showHours(hours?: string) {
    if (!hours) return ''

    if (hours === '00:00') return '-'
    return hours
  }
  handleEdit() {
    const item = {
      startDate: this.startOfWeek,
      endDate: this.endOfWeek,
    }
    const dialogRef = this.editTimeTrackingWeeekDialog.open(item);
    dialogRef.then((c) => {
      c.afterClosed().subscribe((result) => {
        if (result) {
          this.ngOnInit();
        }
      });
    });

  }

  handleDelete() {
    const queryParams: TimeTrackingQueryParam = {
      Page: 0,
      PageSize: 100,
      Search: "",
      startDate: this.startOfWeek,
      endDate: this.endOfWeek,
    }
    this.timeTrackingServices.GetAllTimeTracking(queryParams)
      .pipe(
        concatMap((response1: any) => {
          const _title = this.translate.instant('Delete Time Tracking Week !');
          const _description = this.translate.instant('Do you want to delete?');
          return from(
            this.layoutUtilsService.alertDelete({
              title: _title,
              description: _description
            })
          ).pipe(
            switchMap((isConfirm) => {
              if (!isConfirm) {
                return [];
              }
              return this.timeTrackingServices.DeleteTimeTracking(response1.data.map(x => x?.id));
            })
          );
        }),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((response2) => {
        if (response2) {
          this.toastService.showSuccess(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
          this.dataService.triggerRefreshListTimeTracking()
          return;
        }
        this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe.forEach((s) => s.unsubscribe());
  }
}
