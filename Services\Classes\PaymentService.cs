﻿using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Payment;
using InnoBook.Entities;
using InnoBook.Request.RequestPayment;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;

namespace InnoBook.Services.Classes
{
    public class PaymentService(InnoLogicielContext context) : IPaymentService
    {
        public async Task<RequestPayment> CreatedPayment(RequestPayment payment, string UserId)
        {
            try
            {
                var newPayment = new Payment
                {
                    CreatedBy = UserId,
                    InvoiceId = payment.InvoiceId,
                    CompanyId = payment.CompanyId,
                    Note = payment.Note,
                    DatePayment = payment.DatePayment,
                    PaidAmount = payment.PaidAmount,
                    IdPaymentMethod = payment.IdPaymentMethod,
                    NotificationSent = payment.NotificationSent

                };
                context.Payment.Add(newPayment);
                await context.SaveChangesAsync();
                return payment;
            }
            catch (Exception ex)
            {
                throw;
            }
        }

        public Task<bool> DeletePaymente(List<Guid?> listInvoice)
        {
            throw new NotImplementedException();
        }

        public async Task<PaginatedResponse<GetPaymentDTO>> GetAllPayment(PaginatedRequest query, string companyId)
        {
            var data = await context.Payment
    .AsNoTracking()
    .Where(c => c.InvoiceId == query.InvoiceId && c.CompanyId.ToString() == companyId)
    .Select(c => new GetPaymentDTO
    {
        Id = c.Id,
        InvoiceId = c.InvoiceId,
        CompanyId = c.CompanyId,
        Note = c.Note,
        DatePayment = c.DatePayment,
        PaidAmount = c.PaidAmount,
        IdPaymentMethod = c.IdPaymentMethod,
        NotificationSent = c.NotificationSent
    })
    .Skip((query.Page - 1) * query.PageSize)
    .Take(query.PageSize)
    .ToListAsync();
            return new PaginatedResponse<GetPaymentDTO>
            {
                Data = data,
                Page = query.Page,
                TotalPage = (data.Count() + query.PageSize - 1) / query.PageSize,
                PageSize = query.PageSize,
                TotalRecords = data.Count()
            };
        }
        public async Task<PaginatedResponse<GetPaymentDTO>> GetAllPaymentCompany(PaginatedRequest query,string companyId)
        {
            var total = await context.Payment.Where(c => c.CompanyId.ToString() == companyId).CountAsync();
            var data = await context.Payment.AsNoTracking().Where(c => c.CompanyId.ToString() == companyId)
                .Select(c => new GetPaymentDTO
                {
                    Id = c.Id,
                    InvoiceId = c.InvoiceId,
                    CompanyId = c.CompanyId,
                    Note = c.Note,
                    DatePayment = c.DatePayment,
                    PaidAmount = c.PaidAmount,
                    IdPaymentMethod = c.IdPaymentMethod,
                    NotificationSent = c.NotificationSent
                })
                                .Skip((query.Page - 1) * query.PageSize)
                                .Take(query.PageSize)
                                .ToListAsync();
            return new PaginatedResponse<GetPaymentDTO>
            {
                Data = data,
                Page = query.Page,
                TotalPage = (total + query.PageSize - 1) / query.PageSize,
                PageSize = query.PageSize,
                TotalRecords = total
            };
        }


    }
}
