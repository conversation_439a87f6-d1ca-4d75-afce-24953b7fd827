

<app-breadcrum title="Payments" [actionTemplate]="actionTemplate">
    <ng-template #actionTemplate>

        <div
            class="flex gap-2 align-content-end hover:border border-slate-400 rounded-md">
            <button [matMenuTriggerFor]="menu"
                class="pl-2 pr-2 flex  items-center text-lg font-bold">More
                Action
                <span class="material-icons">
                    expand_more
                </span>
            </button>
            <mat-menu #menu="matMenu">
                <button mat-menu-item>View Payment</button>
                <button mat-menu-item>Payment Setting</button>
            </mat-menu>

        </div>

    </ng-template>
</app-breadcrum>
<hr>

@if(listChoosePayment.length>0)
{
<div class="flex mb-3 items-center">
    <div><span>Select :</span> <span class="font-bold">
            {{listChoosePayment.length}}</span></div>
    <div class="ml-3">
        <button type="button" [matMenuTriggerFor]="menu"
            class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm p-2">Action</button>

        <mat-menu #menu="matMenu">
            <button (click)="creaFormDelete()" mat-menu-item> <span
                    class="material-icons">
                    delete_forever
                </span>Delete</button>
        </mat-menu>
    </div>
</div>
}

<div>
    <hr>

    <div class="p-3 flex items-center">
        <span class="text-xl font-bold text-gray-700 mr-2">All Invoice
            Payments</span>
        <button
            (click)="AddPayment()"
            class="bg-green-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-lg font-bold">+</button>
    </div>
    @if(isLoading) {
    <div class="container-full h-[60dvh] flex justify-center items-center">
        <app-inno-spin size="lg" />
    </div>
    }@else {
    <ejs-grid #grid [dataSource]="dataSource"
        (rowSelecting)="onRowSelecting($event)"
        [allowSelection]="true"
        [allowSorting]="true"
        [sortSettings]='sortOptions'
        (actionBegin)="onActionBegin($event)"
        (rowDeselecting)="onRowDeselecting($event)"
        [selectionSettings]="selectionOptions">
        <e-columns>
            <e-column type="checkbox" width="30"></e-column>

            <e-column headerText="Payment Method /
       " width="120" field="idPaymentMethod">
                <ng-template #template let-data>
                    <span
                        class="text-gray-600">{{data.idPaymentMethod}}</span>
                </ng-template>
            </e-column>
            <e-column field="paidAmount" headerText="Paid Amount
        "
                width="70">
            </e-column>
            <e-column
                headerText="
             Date Payment"
                width="100" field="datePayment">
                <ng-template #template let-data>
                    <span>{{data.datePayment | date:
                        _storeService.getdateFormat() }}</span>
                </ng-template>
            </e-column>
            <e-column field="note" headerText="Description
        "
                width="70">
            </e-column>

        </e-columns>

    </ejs-grid>
    <ejs-pager [pageSize]='pageSizesDefault'
        [totalRecordsCount]='totalPages'
        [currentPage]="currentPage"
        [pageSizes]="pageSizes" (click)="onPageChange($event)">
    </ejs-pager>
    }
</div>
