import { Component, Input } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';

@Component({
  selector: 'app-inno-status',
  templateUrl: './inno-status.component.html',
  styleUrls: ['./inno-status.component.scss'],
  standalone: true,
  imports: [SharedModule]
})
export class InnoStatusComponent {

  public STATUS = {
    DRAFT: 0,
    UN_BILLED: 1,
    NON_BILLABLE: 2,
    PAID: 3,
    BILLED: 4,
    SENT: 5
  }
  @Input() public status?: number;
  constructor() { }
  getStatusText(): string {
    switch (this.status) {
      case this.STATUS.DRAFT:
        return 'STATUS.Draft';
      case this.STATUS.UN_BILLED:
        return 'STATUS.Unbilled';
      case this.STATUS.NON_BILLABLE:
        return 'STATUS.NonBillable';
      case this.STATUS.PAID:
        return 'STATUS.Paid';
      case this.STATUS.BILLED:
        return 'STATUS.Billed';
      case this.STATUS.SENT:
        return 'STATUS.Sent';
      default:
        return 'Unknown';
    }
  }
}
