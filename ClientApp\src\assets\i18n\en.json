{"TOAST": {"Sent": "<PERSON><PERSON>", "SentCode": "Your code is sent", "DeleteImg": "Do you want to remove the picture ?", "MissingRequiredFields": "Missing required fields", "Tax": "There are two or more taxes with the same name.", "TaxName": "Please ensure all tax names are unique.", "DuplicateTax": "Duplicate tax names found:", "Success": "Success", "Warning": "Warning", "Save": "Save", "TheFeature": "The feature is in development.", "SizeFile": "Size File", "Create": "Create", "Delete": "Delete", "NoSelected": "No client selected", "Instruction": "Please select a client to continue creating the invoice.", "Paid": "Paid", "Active": "Active", "Fail": "Fail", "Update": "Update", "MarkAsPaid": "<PERSON>", "MarkAsSent": "<PERSON>", "UpdateBusiness": "Your information has been successfully updated!", "UpdateService": "Update service successfully", "UpdateItem": "Update item successfully", "CreateService": "Create a new service successfully", "DiscardChanges": "Discard Changes", "DescriptionWarning": "You already have an active timer, log it or discard it before", "DescriptionChanges": "You have unsaved changes. Do you want to leave and discard your changes?"}, "ITEM_INVOICE": {"NewInvoiceItem": "New invoice item", "EditInvoiceItem": "Edit a invoice item", "NewEstimateItem": "New estimate item", "EditEstimateItem": "Edit a estimate item", "Description": "Description", "EnterInvoiceDescription": "Enter the invoice description", "Rate": "Rate", "RateRequired": "Rate is required", "Qty": "Qty", "EnterQty": "Enter the qty", "QuantityRequired": "Quantity is required", "Total": "Total", "AddUpdateTaxes": "Add/update Taxes"}, "EMPTY": {"EmptyProject": "Empty project", "NoResult": "No result"}, "COMMON": {"DifferentKeywords": "Please try different keywords", "NoResult": "No result", "EmptyData": "Empty data", "CreateNew": "Create a new", "Search": "Search", "SearchProjectsClients": "Search projects or clients", "NewEntry": "New Entry", "ChooseBusiness": "Choose a Business", "Owner": "Owner", "UserExist": "User already exist", "DatePlaceholder": "Select date", "ConfirmDelete": "Do you want to delete?", "Action": "Action", "Loading": " Loading...", "Delete": "Delete", "MarkBill": "<PERSON> as bill", "Archive": "Archive", "Activate": "Activate", "UploadPicture": "Upload picture", "DescriptionEmptyProject": "Please select a project to get list project"}, "CLIENT": {"ADD_CLIENT_FORM": {"Title": "Add New a Client", "TitleEdit": "Edit a Client", "FirstName": "First name", "FirstNamePlaceholder": "Enter first name", "LastName": "Last name", "LastNamePlaceholder": "Enter last name", "ClientName": "Client name", "ClientNamePlaceholder": "Enter client name", "ClientNameRequired": "Client name is required", "Email": "Email", "EmailPlaceholder": "Enter email", "EmailRequired": "Email is required", "EmailInvalid": "Invalid email", "PhoneNumber": "Phone number", "PhoneNumberPlaceholder": "Enter phone number", "PhoneLength": "Phone number must be 10 digits", "PhonePattern": "OnlyNumber", "Post": "Post", "PostPlaceholder": "Enter Post", "BusinessPhone": "Business Phone", "BusinessPhonePlaceholder": "Enter business phone number", "MobilePhone": "Mobile Phone Number", "MobilePhonePlaceholder": "Enter mobile phone number", "Country": "Country", "CountryPlaceholder": "Select country", "CountryRequired": "Country is required", "Address1": "Address Line 1", "Address1Placeholder": "Enter address", "Address2": "Address Line 2", "Address2Placeholder": "Enter address", "TownCity": "Town/City", "TownCityPlaceholder": "Enter town/City", "StateProvince": "State/Province", "StateProvincePlaceholder": "Enter state/province", "PostalCode": "Postal Code", "PostalCodePlaceholder": "Enter postal code"}, "GIRD": {"Company": "Company", "Credit": "Credit"}, "NewClient": "New client", "DeleteClient": "Delete Client !", "ActiveClient": "Active clients", "TotalOverdue": "Total overdue", "TotalDraft": "Total in draft", "TotalOutstanding": "Total outstanding", "AllClient": "All clients", "MailsHistory": "Mails history"}, "PROJECT": {"ADD_PROJECT_FORM": {"Title": "Add New a Project", "TitleEdit": "Edit a Project", "ProjectType": "Project Type", "ProjectTypePlaceholder": "Select Project Type", "ProjectNameRequired": "Project Name is required", "ClientRequired": "Client is required", "HourlyProject": "Hourly project", "ProjectName": "Project Name", "ProjectNamePlaceholder": "Enter project name", "Description": "Description", "DescriptionPlaceholder": "Add a description", "AssignClient": "Assign Client", "AssignClientPlaceholder": "Select project/client", "EndDate": "End Date", "EndDatePlaceholder": "Select end date", "HourlyRate": "Hourly Rate", "HourlyRatePlaceholder": "Enter Hourly Rate", "FlatRate": "Flat Rate", "FlatRatePlaceholder": "Enter flat Rate", "TotalHours": "Total Hours", "TotalHoursPlaceholder": "Enter total hour", "Member": "Member", "Billable": "Billable", "AddService": "Add a Service"}, "GIRD": {"ProjectName": "Project Name", "Client": "Clients", "EndDate": "End Date", "Members": "Members", "Logged": "Logged", "Unbilled": "Unbilled", "Amount": "Amount"}, "NewAProject": "Create a new project", "NewProject": "Create New Project", "DeleteProject": "Delete Project !", "DescriptionActive": "Do you want to active?", "ActiveProject": "Active Project", "UnActiveProject": "UnActive Project", "ArchiveProjects": "Archive Project", "TotalLogged": "Total Logged", "TotalUnbilled": "Total Unbilledt", "TotalAmount": "Total Amount"}, "BUTTON": {"Cancel": "Cancel", "Update": "Update", "Save": "Save", "Agree": "Agree", "Delete": "Delete", "Apply": "Apply", "SendInvoice": "Send Invoice"}, "LOGIN": {"TitleSign": "Sign in to InnoBooks", "Or": "Or", "TitleNewAccount": "create a new account", "EmailAddress": "Email address", "EmailRequired": "Email is required", "InvalidRequired": "Invalid email", "Password": "Password", "PasswordRequired": "Password required", "PlaceholderPassword": "Enter your password", "PlaceholderEmail": "Enter your email", "ForgotPassword": "Forgot your password?", "OrContinue": "Or continue with", "Signin": " Sign in", "TimeTrackingMade": "Time Tracking Made Simple", "Describe": "Analyze, manage, and optimize your workflow with our powerful time tracking platform."}, "REGISTER": {"TitleCreateAccount": "Create your account", "Describe": "Join our platform and start managing your time effectively", "REGISTER_FORM": {"FirstNameRequired": "First name is required", "LastNameRequired": "Last  name is required", "FirstName": "First name", "LastName": "Last name", "EnterFirstName": "Enter your first name", "EnterLastName": "Enter your last name", "Email": "Email address", "EnterEmail": "Enter your email", "EmailRequired": "Email is required", "Password": "Password", "InvalidEmail": "Invalid email", "PasswordRequired": "Password required", "EnterPassword": "Enter your password", "PasswordMinLength": "Password minlength 6", "ConfirmPassword": "Confirm Password", "ConfirmPasswordRequired": "Confirm password required", "ConfirmPasswordValidator": "Password and Confirm Password did not match.", "EnterPasswordAgain": "Enter your password again", "AgreeTo": "I confirm that I have read and agree to InnoBooks Terms of Service and Privacy Policy.", "CreateButton": "Create Account", "AlreadyAccount": "Already have an account?", "SignIn": " Sign in", "Company": " Company", "OrContinue": "Or continue with"}}, "TIMETRACKING": {"Timerdiscarded": "Timer discarded !", "Title": "Time Tracking", "Write": "Write your thoughts here...", "ProjectRequired": "Project is required", "EditEntry": "Edit Time Entry", "TimeRequired": "Time is required", "DeleteTimeTracking": "Delete TimeTracking !", "TitleGenerate": "Generate Invoice", "TotalTime": "Total Time", "Day": "Day", "Week": "Week", "Month": "Month", "All": "All", "Search": "Search", "GenerateInvoice": "Generate Invoice", "DatePickerPlaceholder": "Select a date", "EntryPlaceholder": "What are you working on?", "StartTimer": "Start Timer", "Billable": "Billable", "Entry": "Entry", "LogTime": "Log time", "NoClient": "No client", "Discard": "Discard", "AddEntry": " Add Entry", "Timer": "Timer", "NoTime": "No time entries at the moment", "DescribeNoTime": "Start adding projects/clients and log your time now!", "ChooseClient": "Choose a client", "ChooseService": "Choose Service", "NoEntries": "No time entries at the moment", "NoEntriesSubtext": "Start adding projects/clients and log your time now!", "DIALOG": {"TitleConfirm": "Confirm", "Discard": "Are you sure you want to discard this timer?"}}, "SETTINGS": {"Title": "Settings", "BasicInformation": {"Title": "Basic Information", "Description": "Add or edit basic Information about your business", "BasicInformationForm": {"BusinessName": "Business Name", "BusinessNamePlaceholder": "Business Name", "BusinessNameRequired": "Business name is required", "EmailBusiness": "Email Business", "EmailPlaceholder": "Email", "EmailRequired": "Email is required", "PhoneNumber": "Phone Number", "PhonePlaceholder": "Phone", "PhoneRequired": "Phone is required", "Country": "Country", "AddressLine1": "Address Line 1", "AddressLine1Placeholder": "Address Line 1", "AddressLine2": "Address Line 2", "AddressLine2Placeholder": "Address Line 2", "TownCity": "Town/City", "TownCityPlaceholder": "Town/City", "StateProvince": "State/Province", "StateProvincePlaceholder": "State/Province", "PostalCode": "Postal Code", "PostalCodePlaceholder": "Postal Code", "StartWeekOn": "Start Week On", "StartWeekOnPlaceholder": "Select a day", "Timezone": "Timezone", "PlaceholderTimezone": "Select a timezone", "DateFormat": "Date Format", "PlaceholderDateFormat": "Select a format", "Note": "Note", "NotePlaceholder": "Note here...", "PhoneLength": "Phone number must be 10 digits", "PhonePattern": "OnlyNumber"}}, "PermissionInformation": {"Title": "Permission Information", "Description": "Add or edit role permission about your project"}, "AccountInformation": {"Title": "Account Information", "Description": "Account Management innoBook"}, "CategoryInformation": {"Title": "Category Information", "Description": "Edit or add type for Management innoBook"}, "TaxAndFinancialInformation": {"Title": "Tax and Financial Information", "Description": "Add or edit financial preference for about your business", "TaxAndFinancialInformationForm": {"BaseCurrency": "Base currency", "BaseCurrencyPlaceholder": "Select a currency", "FiscalYearStartMonth": "Fiscal year start month", "FiscalYearStartMonthPlaceholder": "Select...", "FiscalYearEndMonth": "Fiscal year end month", "FiscalYearEndMonthPlaceholder": "Select...", "StandardRate": "Standard Rate", "StandardRatePlaceholder": "$0.00", "StandardRateUnit": "$ / Hour", "SaveChanges": "Save Changes"}}, "Billing": {"Title": "Billing & Plans", "Description": "Manage your billing information", "BillingForm": {"CardNumber": "Card Number", "CardNumberPlaceholder": "Enter your card number", "CardNumberRequired": "Card number is required", "ExpirationDate": "Expiration Date", "ExpirationDatePlaceholder": "Select a date", "ExpirationDateRequired": "Expiration date is required", "CVC": "CVC", "CVCPlaceholder": "Enter your CVC", "CVCRequired": "CVC is required", "SaveChanges": "Save Changes"}}}, "CATEGORY": {"Title": "Category", "TitleAdd": "Add Category", "ListCategory": "List Category Item", "CategoryItem": "Category Item", "AddCateGory": "Add a new Category", "CreateCategoryItem": "Created Category Item", "CreateCategory": "Created Category", "AddNew": "Add a new item Category", "RemoveAll": "Remove All", "DeleteCategory": "Delete Category", "GIRD": {"CategoryName": "Category Name", "CreateDate": "Create Date"}}, "TEAMMEMBERS": {"Title": "Team Members", "AddButton": "Add Team Members", "DeleteMember": "Delete members !", "GIRD": {"Name": "Name", "Role": "Role"}, "AddMemberForm": {"ProfileMember": "Profile Member", "Title": "Add Team Members", "FirstName": "First name", "LastName": "Last name", "Email": "Email", "FirstNameRequired": "Firstname is required", "LastNameRequired": "Lastname is required", "EmailRequired": "Email is required", "InvalidEmail": "<PERSON><PERSON><PERSON>"}, "Detail": {"BasicInfo": "Basic Information", "RolePermissions": "Role and Permissions", "EmailLabel": "Email", "RoleLabel": "Role", "SettingsTitle": "Team Member Settings", "SetBillableRate": "Set Billable Rate", "BillableRateDescription": "Set hourly rates for tracked time", "SetCostRate": "Set Cost Rate", "CostRateDescription": "How much you pay your team members for the work they do", "SetCapacity": "Set Capacity", "CapacityUnit": "hr/week"}}, "EXPENSES": {"Title": "Expenses", "DeleteExpeneses": "Delete Expeneses !", "DatePlaceholder": "Select date", "UpdateExpense": "Update Expense", "DescriptionEditExpensesItem": "Edit a expenses item", "NewExpensesItem": "New expenses item", "ExportButton": "Export", "NewExpenseButton": "New Expense", "GIRD": {"ExpenseName": "Expense Name", "ProjectClient": "Project/Client", "Date": "Date", "User": "User", "Amount": "Amount"}, "NEW_ADD_FORM": {"MerchantName": "Merchant Name", "MerchantPlaceholder": "Search or create new expense", "ExpenseName": "Expense Name", "ExpensePlaceholder": "Enter Expense Name", "Description": "Description", "DescriptionPlaceholder": "A brief description of the project.", "ClientProject": "Client/Project", "ClientProjectPlaceholder": "Select client/project", "Category": "Category", "CategoryPlaceholder": "Select category", "Date": "Date", "DatePlaceholder": "Select date", "ValidationMerchantRequired": "Expense name is required", "ValidationExpenseRequired": "Expense Name is required", "ValidationCategoryRequired": "Category item is required", "ValidationDateRequired": "End date is required", "ValidationRateRequired": "Rate is required", "ValidationQuantityRequired": "Quantity is required", "AddTaxes": "+ Add Taxes", "AddNewLine": "Add new line", "ExpensesItem": "Expenses Item", "Rate": "Rate", "Quantity": "Quantity", "Tax": "Tax", "LineTotal": "Line Total", "Subtotal": "Subtotal", "Total": "Total", "AmountDue": "Amount Due", "Discount": "Discount", "AddUpdateTaxes": "Add/update Taxes", "AddDiscount": "Add discount", "Attachment": "Add Attachment"}}, "MENUACTION": {"MarkAsPaid": "<PERSON> as <PERSON><PERSON>", "MarkAsSent": "<PERSON> as <PERSON><PERSON>", "Duplicate": "Duplicate", "DownloadPDF": "Download PDF", "Print": "Print", "Archive": "Archive", "Delete": "Delete"}, "ESTIMATE": {"Title": "Estimate", "TitleEdit": "Update Estimate", "DeleteEstimate": "Delete Estimate !", "Tabs": {"Created": "Created", "SentToMe": "Sent to Me"}, "Summary": {"TotalOverdue": "Total estimate overdue", "TotalInDraft": "Total estimate in draft", "TotalOutstanding": "Total estimate outstanding", "DatefIssue": "Date of Issue", "DueDate": "Due Date", "More": "More", "BilledTo": "Billed to", "Description": "Description", "Edit": "Edit", "ByLink": "Share by link", "SendMail": "Send Email"}, "GIRD": {"EstimateNumber": "Estimate Number", "Clients": "Clients", "IssuedDate": "Issued Estimate Date", "DueDate": "Due Date", "Status": "Status", "Amount": "Amount"}, "Buttons": {"NewEstimate": "New Estimate", "EditEstimate": "Edit Estimate", "SendEstimate": "Send Estimate"}, "ESTIMATE_FORM": {"EditBusinessInfo": "Edit Business Information", "ClientProject": "Sent to Client/Project", "ClientProjectPlaceholder": "Select client/project", "EstimateNumber": "Estimate Number", "IssueDate": "Issue Estimate Date", "IssueDatePlaceholder": "Select issue date", "DueDate": "Due Date", "DueDatePlaceholder": "Select due date", "Description": "Description", "DescriptionPlaceholder": "A brief description of the estimate details.", "Subtotal": "Subtotal", "Tax": "Tax", "AmountDue": "Amount Due", "EstimateTotal": "Estimate Total", "Discount": "Discount", "AddDiscount": "Add discount", "Buttons": {"AddUnbillTime": "Add Unbill Time", "AddNewItem": "+ Add New Item", "AddTaxes": "+ Add Taxes", "AddUnbillExpense": "Add Unbill Expense", "AddNewLine": "Add new line"}, "TableHeaders": {"EstimateItem": "Estimate Item", "Rate": "Rate", "Quantity": "Quantity", "Tax": "Tax", "LineTotal": "Line Total"}, "Validation": {"ClientRequired": "Client is required", "IssueDateRequired": "Invoice date is required", "DueDateRequired": "Due date is required"}}}, "INVOICES": {"Warning": "Please fill in all the invoice information completely.", "Title": "Invoices", "DeleteInvoice": "Delete Invoice !", "TitleEdit": "Update Invoice", "Tabs": {"Created": "Created", "SentToMe": "Sent to Me"}, "Summary": {"TotalOverdue": "Total overdue", "TotalInDraft": "Total in draft", "TotalOutstanding": "Total outstanding", "DatefIssue": "Date of Issue", "DueDate": "Due Date", "More": "More", "BilledTo": "Billed to", "Description": "Description", "Edit": "Edit", "ByLink": "Share by link", "SendMail": "Send Email"}, "GIRD": {"InvoiceNumber": "Invoice Number", "Clients": "Clients", "IssuedDate": "Issued Date", "DueDate": "Due Date", "Status": "Status", "Amount": "Amount"}, "Buttons": {"NewInvoice": "New Invoice", "EditInvoice": "Edit Invoice"}, "INVOICE_FORM": {"EditBusinessInfo": "Edit Business Information", "ClientProject": "Sent to Client/Project", "ClientProjectPlaceholder": "Select client/project", "InvoiceNumber": "Invoice Number", "IssueDate": "Issue Date", "IssueDatePlaceholder": "Select issue date", "DueDate": "Due Date", "DueDatePlaceholder": "Select due date", "Description": "Description", "DescriptionPlaceholder": "A brief description of the invoice details.", "Subtotal": "Subtotal", "Tax": "Tax", "AmountDue": "Amount Due", "Discount": "Discount", "AddDiscount": "Add discount", "Buttons": {"AddUnbillTime": "Add Unbill Time", "AddNewItem": "+ Add New Item", "AddTaxes": "+ Add Taxes", "AddUnbillExpense": "Add Unbill Expense", "AddNewLine": "Add new line"}, "TableHeaders": {"InvoiceItem": "Invoice Item", "Rate": "Rate", "Quantity": "Quantity", "Tax": "Tax", "LineTotal": "Line Total"}, "Validation": {"ClientRequired": "Client is required", "IssueDateRequired": "Invoice date is required", "DueDateRequired": "Due date is required"}}}, "ITEMS_SERVICES": {"Title": "Item & Services", "EditItem": "Edit item", "EditService": "Edit service", "DeleteItem": "Delete Item !", "DeleteService": "Delete Service !", "CreateNewService": "Create new service", "Tabs": {"Items": "Items", "DescribeItem": "Items can be included in invoices to charge your clients.", "DescribeServices": " Services allow you to track time spent on tasks and bill your clients accordingly.", "Services": "Services"}, "SearchPlaceholder": "Search", "Buttons": {"CreateNew": "Create New"}, "GIRD": {"ServiceName": "Service Name", "ItemName": "Item Name", "Description": "Description", "Taxes": "Taxes", "Rate": "Rate", "User": "User", "Date": "Date"}, "NEW_ITEM_FORM": {"Title": "Create new item", "Name": "Name", "NameRequired": "Name is required", "NamePlaceholder": "Enter name", "Description": "Description", "DescriptionPlaceholder": "Add a description", "Rate": "Rate ($)", "RatePlaceholder": "0.00", "Taxes": "Taxes", "TaxName": "Tax name", "TaxRate": "Rate (%)", "TaxNumber": "Tax number", "AddTax": "Add another tax"}}, "TAX": {"AddTax": "Add Taxes", "TaxName": "Tax name", "ApplyTax": "Apply taxes to all line items", "AddAnother": "Add another tax", "PlaceholderTaxName": "Enter the tax name", "PlaceholderTaxNumber": "Enter the tax number", "Rate": "Rate (%)", "TaxNumber": "Tax number"}, "GENERATEINVOICE": {"GenerateNewInvoice": "Generate a New Invoice", "Client": "Client", "SelectClient": "Select client", "ClientRequired": "'Client is required", "DateRange": "Date Range", "AllTime": "All time", "ChooseExpenses": "<PERSON><PERSON> Expenses", "AllUnbilledExpensesClient": "All unbilled expenses for this client", "OnlyUnbilledExpensesProjects": "Only unbilled expenses assigned to selected projects", "NoExpenses": "No expenses", "ChooseProject": "Choose Project", "AmountDue": "Amount Due", "EXPENSES": {"ExpensesName": "Expenses Name", "CategoryName": "Category Name", "ItemName": "Item Name", "PaidAmount": "<PERSON><PERSON>"}, "RangDateOptions": {"AllTime": "All time", "ThisMonth": "This month", "LastMonth": "Last month", "Custom": "Custom"}, "TableHeaders": {"Description": "Description", "User": "User", "Project": "Project", "Hours": "Hours", "LineTotal": "Line Total"}}, "SERVICEFORPROJECT": {"ServiceName": "Service Name", "Rate": "Rate", "Description": "Description", "Taxes": "Taxes", "Date": "Date", "Buttons": {"CreateNewService": "Create New Service"}, "Title": "Add Service"}, "SELECTTIMETRACKING": {"Title": "Select Item&Services", "Tabs": {"Items": "Items", "Services": "Services"}, "Table": {"ItemName": "Item Name", "ServiceName": "Service Name", "Rate": "Rate", "Qty": "Qty", "Description": "Description", "Taxes": "Taxes", "Date": "Date"}}, "FILEUPLOAD": {"Title": "File Upload", "FileName": "File Name", "Size": "Size", "Type": "Type", "Action": "Action"}, "DASHBOARD": {"Title": "Dashboard", "TitleChartRevenue": "Revenue and Expenses", "TitleChartGraphics": "Graphics Chart", "TotalAmount": "Total Amount", "TotalAmountPaid": "Total AmountPaid", "Explore": "Explore more", "Value": "Value", "Users": "Users", "Orders": "Orders", "Tickets": "Tickets"}, "REPORT": {"Title": "Reports"}, "FORGOTPASSWORD": {"Title": "Forgot your password?", "Instruction": "Enter your email and we'll send you instructions to reset your password", "EmailLabel": "Email address", "EmailInvalid": "Invalid email", "EmailPlaceholder": "Enter your email", "EmailRequired": "Email is required", "ResetButton": "Reset Password", "BackToSignIn": "Back to Sign In"}, "PROFILE": {"Title": "Profile Information", "FirstName": "First name", "LastName": "Last name", "Email": "Email", "UserName": "UserName", "FirstNameRequired": "Firstname is required", "LastNameRequired": "Lastname is required", "TimeZone": "Time Zone"}, "ACCOUNT": {"Title": "Account Management", "DeleteUser": "Delete User !", "GIRD": {"Name": "Name", "Role": "Role", "Action": "Action"}}, "MENU": {"Pos": "Pos", "Tracking": "Tracking", "Estimates": "Estimates", "TimeTracking": "Time Tracking", "Analyze": "Analyze", "Dashboard": "Dashboard", "Reports": "Reports", "Manager": "Manager", "Clients": "Clients", "Projects": "Projects", "Invoices": "Invoices", "Expenses": "Expenses", "Upload": "Upload", "ItemServices": "Items & Services", "TeamMembers": "Team Members", "Settings": "Settings"}, "ROLE": {"RolePermission": "Role Permission", "UserGroup": "User Group", "User": "User", "Role": "Role", "UserGroupAuthorization": "User group authorization", "New": "New", "GIRD": {"RoleName": "Role Name", "Description": "Description", "Code": "Code"}}, "FILTER": {"ClientProjects": "Client/Projects", "SelectClientProjects": "Select Client/Projects", "LoggedBy": "Logged by", "SelectUser": "Select user", "StartDate": "Start Date", "SelectStartDate": "Select start date", "EndDate": "End Date", "SelectEndDate": "Select end date", "ResetDefault": "<PERSON><PERSON>"}, "POS": {"Title": "POS", "Tax": "Tax", "ChooseClient": "Choose Client", "SearchClients": "Search clients", "CreateNewClient": "+ Create a new client", "CreateInvoice": "Create Invoice", "Print": "Print", "Rate": "Rate", "Items": "Items", "ItemName": "Item name", "Quantity": "Quantity", "LineTotal": "Line Total", "EnterItemName": "Enter item name", "AddNewItem": "Add New Item"}, "STATUS": {"Draft": "Draft", "Paid": "Paid", "NonBillable": "Non-billable", "Unbilled": "Unbilled", "Billed": "Billed", "Sent": "<PERSON><PERSON><PERSON>"}, "TAG": {"CreateTag": " Create a new tag “Web”", "Search": "Search or create new tag"}, "VERIFICATION": {"Title": "Enter verification code", "Message": "We've sent a code to", "Instruction": "Enter the code to log in to your account", "LoginButton": "<PERSON><PERSON>", "NotReceived": "Didn't receive a code?", "Resend": "Resend Code"}, "Toast.TitleSuccess.Text": "Success", "User.Recover.Password.Title": "RESET YOUR PASSWORD", "User.Recover.Password.ButtonResetPassword": "SEND RESET LINK", "User.Recover.Password.LinkToLoginPage": "Back to login page", "User.Reset.Password.Sucessfully": "Password was successfully reset", "User.Reset.Password.SendMail": "<PERSON>t recover password url to the email successfully", "Resend Email": "Resend email successfully", "Error.Code": "Code does not exist or has expired", "Toast.DescriptionSuccess.Text": "Successfully updated", "User.Reset.Password.Token.Invalid": "The link is either invalid or has expired", "Grid.ConfirmDelete.Text": "Do you want to delete this item?", "Logs.Tooltip.Error": "The system flagged several transactions, but upon review, we found that the rule is in error, mistakenly identifying legitimate activities as fraudulent."}