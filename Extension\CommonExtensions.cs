﻿namespace InnoBook.Extension
{
    public static class CommonExtensions
    {
        public static bool IsBase64(string value)
        {
            if (string.IsNullOrEmpty(value) || value.Length % 4 != 0)
                return false;

            return true;
        }
    }

    public static class ConfigurationExtensions
    {
        public static string GetRequired(this IConfiguration config, string key)
        {
            var value = config[key];
            if (string.IsNullOrWhiteSpace(value))
                throw new InvalidOperationException($"Configuration value for '{key}' is missing.");
            return value;
        }
    }
}
