import { Company } from "./company.interface";
import { Client } from "./client.interface";
import { ItemInvoice } from "./ItemInvoice.interface";

export interface Invoice {
  companyId?: string;
  contractorId?: string;
  clientId?: string;
  invoiceNumber?: string;
  invoiceDate?: Date;
  dueDate?: Date;
  reference?: any;
  notes?: any;
  img?: any
  totalAmount?: number;
  rate?: number;
  type?: string;
  paidAmount: number;
  taxAmount?: number;
  timeAmount?: number;
  status?: number;
  base64?: any;
  filename?: any;
  taxes?: any[];
  payments?: any;
  itemInvoices: ItemInvoice[];
  client?: Client;
  company?: Company;
  id?: string;
  isEstimate?: boolean
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
  updatedBy?: any;
  projectId?: string;
  position?: number


}
