<app-inno-modal-wrapper [title]="title"
  (onClose)="handleClose()">
  <form [formGroup]="newpExpensesForm">
    <div class="w-full p-[16px] flex flex-col gap-[16px]">
      <app-inno-form-select-search
        [label]="'EXPENSES.NEW_ADD_FORM.MerchantName' | translate"
        [options]="merchantOptions"
        [formControl]="f['merchantId']"
        [value]="f['merchantId'].value"
        [errorMessages]="{ required: 'EXPENSES.NEW_ADD_FORM.ValidationMerchantRequired' | translate }"
        [placeholder]="'EXPENSES.NEW_ADD_FORM.MerchantPlaceholder' | translate"
        (onCreateNew)="handleCreateNewMerchant($event)" />
      <app-inno-form-input
        [label]="'EXPENSES.NEW_ADD_FORM.ExpenseName' | translate"
        [placeholder]="'EXPENSES.NEW_ADD_FORM.ExpensePlaceholder' | translate"
        [formControl]="f['expensesName']"
        [value]="f['expensesName'].value"
        [errorMessages]="{
          required: 'EXPENSES.NEW_ADD_FORM.ValidationExpenseRequired' | translate
        }" />
      <app-inno-form-textarea
        [label]="'EXPENSES.NEW_ADD_FORM.Description' | translate"
        [formControl]="f['note']"
        [value]="f['note'].value"
        [placeholder]="'EXPENSES.NEW_ADD_FORM.DescriptionPlaceholder' | translate" />
      <div class="grid grid-cols-2 gap-3">

        <app-inno-form-select-search
          #selectSearchClientElement
          [label]="'EXPENSES.NEW_ADD_FORM.ClientProject' | translate"
          [options]="projectAndClientOptions"
          [isProjectClient]="true"
          [formControl]="f['projectId']"
          [projectId]="f['projectId'].value"
          [value]="f['clientId'].value"
          [placeholder]="'EXPENSES.NEW_ADD_FORM.ClientProjectPlaceholder' | translate"
          [customOptionTemplate]="projectOptionTemplate">
          <ng-template #projectOptionTemplate let-item>
            <div
              (click)="handleSelectProject(item)"
              [ngClass]="{'pl-8': item?.metadata?.type == 'project'}"
              class="w-full flex p-[8px] items-center gap-[10px] rounded-md cursor-pointer hover:bg-bg-brand-primary"
              [class.selected]="item.value === f['projectId'].value">
              @if(item?.metadata?.type == 'client') {
              <ngx-avatars
                [size]="32"
                [name]="item.label" />
              } @else {
              <div
                class="w-[32px] h-[32px] rounded-full overflow-hidden flex justify-center items-center bg-bg-brand-primary shrink-0">
                <img class="w-[16px]"
                  src="../../../assets/img/icon/ic_file_green.svg" alt="Icon">
              </div>
              }
              <div class="w-full">
                <p
                  class="line-clamp-1 text-text-primary text-text-sm-regular txtTitle">
                  {{ item.label }}
                </p>
                @if(item.metadata?.description) {
                <p
                  class="line-clamp-1 text-text-tertiary text-text-xs-regular txtDescription">
                  {{ item.metadata.description }}
                </p>
                }
              </div>
            </div>
          </ng-template>
        </app-inno-form-select-search>

        <app-inno-form-select-search
          #selectSearchCategoryElement
          [label]="'EXPENSES.NEW_ADD_FORM.Category' | translate"
          [placeholder]="'EXPENSES.NEW_ADD_FORM.CategoryPlaceholder' | translate"
          [options]="categoryOptions"
          [formControl]="f['categoryItemId']"
          [value]="f['categoryItemId'].value"
          [customOptionTemplate]="categoryOptionTemplate"
          (onCreateNew)="handleCreateCategory($event)"
          [errorMessages]="{ required: 'EXPENSES.NEW_ADD_FORM.ValidationCategoryRequired' | translate }">
          <ng-template #categoryOptionTemplate let-item>
            <div
              (click)="handleSelectCategory(item)"
              [ngClass]="{'pl-10': item?.metadata?.type == 'childCategory'}"
              class="w-full flex p-[8px] items-center gap-[10px] rounded-md cursor-pointer hover:bg-bg-brand-primary"
              [class.selected]="item.value === f['categoryItemId'].value">
              <div class="w-full flex items-center">
                @if( item?.metadata?.type=='childCategory')
                {
                <div
                  class="w-[32px] h-[32px] rounded-full overflow-hidden flex justify-center items-center bg-bg-brand-primary shrink-0">
                  <img class="w-[16px]"
                    src="../../../assets/img/icon/ic_file_green.svg" alt="Icon">
                </div>
                }@else{
                <ngx-avatars
                  [size]="32"
                  [name]="item.label" />
                }
                <p
                  class="line-clamp-1 text-text-primary text-text-sm-regular txtTitle pl-2">
                  {{ item.label }}
                </p>
              </div>
            </div>

          </ng-template>
        </app-inno-form-select-search>
      </div>

      <app-inno-form-datepicker
        [label]="'EXPENSES.NEW_ADD_FORM.Date' | translate"
        [formControl]="f['date']"
        [value]="f['date'].value"
        [placeholder]="'EXPENSES.NEW_ADD_FORM.DatePlaceholder' | translate"
        [errorMessages]="{ required: 'EXPENSES.NEW_ADD_FORM.ValidationDateRequired' | translate }" />

      <div
        class="w-full mt-[16px] border-t border-dashed border-border-primary">
        <div class="overflow-auto w-full">
          <div class="expensesTableLayout">
            <p class="text-text-tertiary text-text-sm-semibold">
              {{ 'EXPENSES.NEW_ADD_FORM.ExpensesItem' | translate }}
            </p>
            <p class="text-text-tertiary text-text-sm-semibold">
              {{ 'EXPENSES.NEW_ADD_FORM.Rate' | translate }}
            </p>
            <p class="text-text-tertiary text-text-sm-semibold">
              {{ 'EXPENSES.NEW_ADD_FORM.Quantity' | translate }}
            </p>
            <p class="text-text-tertiary text-text-sm-semibold">
              {{ 'EXPENSES.NEW_ADD_FORM.Tax' | translate }}
            </p>
            <p class="text-text-tertiary text-text-sm-semibold">
              {{ 'EXPENSES.NEW_ADD_FORM.LineTotal' | translate }}
            </p>
          </div>
          @for(itemExpenses of (f['itemExpenses'].value); track itemExpenses;
          let i = $index) {
          <div class="expensesTableLayout">
            <p class="text-text-primary text-text-md-semibold">
              {{ itemExpenses?.description ?? '' }}
            </p>
            <p class="text-text-primary text-text-md-regular">
              ${{ itemExpenses?.rate ?? 0 | formatNumber }}
            </p>
            <p class="text-text-primary text-text-md-regular">
              {{ itemExpenses?.qty ?? 0 | formatNumber }}
            </p>
            @if(itemExpenses?.taxes.length > 0 &&
            getNameSelectedTaxes(itemExpenses?.taxes)!='')
            {

            <p (click)="handleModifyTaxes(itemExpenses?.taxes,i)"
              class="text-text-primary text-text-md-regular cursor-pointer">
              {{ getNameSelectedTaxes(itemExpenses?.taxes) }}
            </p>
            }@else{
            <p (click)="handleModifyTaxes(itemExpenses?.taxes,i)"
              class="text-blue-500 text-sm cursor-pointer">
              {{ 'EXPENSES.NEW_ADD_FORM.AddTaxes' | translate }}
            </p>
            }

            <p class="text-text-primary text-text-md-bold">
              ${{
              calculateTotalInvoiceItem(itemExpenses?.rate, itemExpenses?.qty
              ) | decimal:2 | formatNumber
              }}

            </p>
            <app-inno-table-action
              (onEdit)="handleModifyExpenseItem(i, itemExpenses)"
              (onDelete)="handleDeleteInvoiceItem(i)" />
          </div>
          }
        </div>
        <button (click)="handleModifyExpenseItem()"
          class="mt-[8px] button-size-md button-outline-primary w-full border-dashed justify-center">
          <img src="../../../../../assets/img/icon/ic_add_green.svg" alt="Icon">
          {{ 'EXPENSES.NEW_ADD_FORM.AddNewLine' | translate }}
        </button>
      </div>
      <div class="w-full flex flex-col items-end mt-[16px]">
        <div class="flex justify-end items-start gap-[8px]">
          <p class="text-right text-text-primary text-text-md-regular">
            {{ 'EXPENSES.NEW_ADD_FORM.Subtotal' | translate }}
          </p>
          <p
            class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
            ${{ subtotal | decimal:2 | formatNumber}}
          </p>
        </div>
        <div class="flex justify-end items-start gap-[8px]">
          <p class="text-right text-text-primary text-text-md-regular">
            {{ 'EXPENSES.NEW_ADD_FORM.Tax' | translate }}
          </p>
          <p
            class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
            ${{ CheckIsNaN(sumtax) | decimal:2 | formatNumber}}
          </p>
        </div>
        <div class="flex justify-end items-start gap-[8px]">
          <div class="block">
            <p class="text-right text-text-primary text-text-md-regular">
              {{ 'EXPENSES.NEW_ADD_FORM.Discount' | translate }}
            </p>
            <button class="button-link-primary">
              {{ 'EXPENSES.NEW_ADD_FORM.AddDiscount' | translate }}
            </button>
          </div>
          <p
            class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
            $0
          </p>
        </div>
        <div class="flex justify-end items-start gap-[8px]">
          <p class="text-right text-text-primary text-text-md-regular">
            {{ 'EXPENSES.NEW_ADD_FORM.AmountDue' | translate }}
            ({{_storeService.curencyCompany | async}})
          </p>
          <p
            class="text-text-primary text-headline-md-bold text-right w-[160px] shrink-0">
            ${{ totalAmount | decimal:2 | formatNumber}}
          </p>
        </div>
      </div>
      <hr>
      <!--  load file -->
      @for(file of Attachment; track file;
      let i = $index) {
      <div class=" flex items-center">
        <div class="flex cursor-pointer" (click)="handleDowload(file)">
          <img class="w-4" [src]="_storeService.getIconFile(file.type)">
          <div class="ml-2">
            <span
              class=" text-text-primary text-text-md-regular">{{file.filename}}</span>
            <span class="pl-2 text-gray-500">({{file.size | size}})</span>
          </div>

        </div>
        <div class="pl-2">
          <button class="button-icon" (click)="RemoveChoseFile(i)">
            <img src="../../../../../../../../assets/img/icon/ic_remove.svg"
              alt="Icon">
          </button>
        </div>

      </div>
      }
      <button class="button-link-primary" (click)="hiddenfileinput.click()">
        <img src="../../../../assets/img/icon/ic_add_green.svg" alt="Icon">
        {{ 'EXPENSES.NEW_ADD_FORM.Attachment' | translate }}
      </button>
      <input
        accept="application/vnd.ms-excel, application/pdf,application/msword,.pdf, .xls, .xlsx, application/vnd.ms-powerpoint"
        type='file' id="PDFInpdd" #hiddenfileinput
        (change)="onSelectFile($event)" multiple
        style="display: none;">
    </div>
  </form>
  <div footer>
    <app-inno-modal-footer
      (onSubmit)="handleSubmit()"
      (onCancel)="handleCancel()" />
  </div>
</app-inno-modal-wrapper>
