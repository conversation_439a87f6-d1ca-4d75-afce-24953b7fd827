﻿using InnoBook.DTO.Category;
using InnoBook.DTO.CoreModel;
using InnoBook.Request.Category;

namespace InnoBook.Services.Interface
{
    public interface ICategoryService
    {

        Task<PaginatedResponse<GetAllCategoryDTO>> GetAllCategoryAsync(PaginatedRequest query);
        Task<bool> CreateCategoryAsync(List<RequestCategory> category);
        Task<bool> CreateCategoryItemAsync(List<RequestCategoryItem> category);
        Task<bool> DeleteCategory(List<Guid?> listCategory);
        public Task<bool> DeleteCategoryItem(List<Guid?> listCategoryIteam);
        Task<bool> UpdateCategory(RequestCategory category);
        Task<GetCategoryDTO> GetCategoryById(string Id);
        Task<List<GetCategoryDTO>> GetCategory();
    }
}
