<span
  class="text-text-sm-semibold px-[8px] py-[2px] rounded-md text-center"
  [ngClass]="{
    'text-text-warning bg-bg-warning-primary': status === STATUS.UN_BILLED,
    'text-text-primary bg-bg-tertiary': status === STATUS.NON_BILLABLE,
    'text-text-primary bg-bg-disabled': status === STATUS.DRAFT,
    'text-text-success bg-bg-success-secondary': status === STATUS.PAID,
    'text-text-success bg-bg-secondary-subtle': status === STATUS.BILLED,
    'text-text-danger bg-bg-secondary': status === STATUS.SENT,
  }">
  {{ getStatusText()|translate }}
</span>
