﻿using InnoBook.DTO.Business;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.TimeTracking;
using InnoBook.DTO.UserBusiness;
using InnoBook.Enum;

namespace InnoBook.Services.Interface
{
    public interface IBusinessService
    {
        Task<List<GetBusinessDTO>> GetUserBusiness(Guid userId);
        Task<GetUserBusiness> GetDetailUserInBusiness(Guid userId, Guid CompanyId);
        Task<ResultAddMember?> AddMemberBusiness(MemberBusiness member, Guid authId);
        Task<bool> DeleteMemberBusiness(Guid memberId, Guid CompanyId);
        Task<PaginatedResponse<GetBusiness>> GetAllUserBusiness(GetUserBusinessQueryParam query, string userId, string companyId, string role);
        Task<GetBusinessByIdDTO> GetBusinessById(Guid BusinessId, Guid UserId);
        Task<UserBusinessDTO> UpdateRoleMember(Guid userId, string role, Guid companyId);
        Task CreateUserBusiness(Guid userId, CreateUserBusiness data);
        Task CreateUserBusinessCompany(Guid userId, CreateUserBusinessDTO data);
        Task UpdateMemberByEmailAsync(string email,string role);
        string GetCompanyId(string businessId);
        Task<bool> UpdateStatus(string businessId, UserBusinessStatus status);

    }
}
