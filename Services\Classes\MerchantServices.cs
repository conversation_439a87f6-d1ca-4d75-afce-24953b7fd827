﻿using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Merchant;
using InnoBook.Entities;
using InnoBook.Request.Merchant;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.Design;

namespace InnoBook.Services.Classes
{
    public class MerchantServices(InnoLogicielContext context) : IMerchantServices
    {
        public async Task<RequestMerchant?> CreateMerchantAsync(RequestMerchant merchant, string UserId, string CompanyId)
        {
            try
            {
                var newMerchant = new Merchant
                {
                    CompanyId = Guid.Parse(CompanyId),
                    MerchantName = merchant.MerchantName,
                    CreatedBy = UserId
                };
                context.Merchant.Add(newMerchant);
            await context.SaveChangesAsync();
                merchant.Id = newMerchant.Id;
            return merchant;
            }catch
            {
                return null;
            }

        }
     
        public async Task<PaginatedResponse<GetMerchantDTO>> GetAllMerchant(PaginatedRequest query,string CompanyId)
        {
            var data = await context.Merchant.Select(m=> new GetMerchantDTO
            {
                Id=m.Id,
                MerchantName=m.MerchantName,
                CompanyId=m.CompanyId
            }).Where(c=>c.CompanyId.ToString()== CompanyId).OrderBy(x=>x.MerchantName)
                                .Skip((query.Page - 1) * query.PageSize)
                                .Take(query.PageSize)
                                .ToListAsync();
            return new PaginatedResponse<GetMerchantDTO>
            {
                Data = data,
                Page = query.Page,
                TotalPage = (int)Math.Ceiling(data.Count() / (decimal)query.PageSize),
                PageSize = query.PageSize,
                TotalRecords = data.Count()

            };
        }
    }
}
