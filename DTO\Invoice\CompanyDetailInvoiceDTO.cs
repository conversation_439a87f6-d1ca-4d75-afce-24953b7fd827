﻿namespace InnoBook.DTO.Invoice
{
    public class CompanyDetailInvoiceDTO
    {
        public string? BusinessName { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? Country { get; set; }
        public string? Adress { get; set; }
        public string? Adress2 { get; set; }
        public string? City { get; set; }
        public string? Province { get; set; }
        public string? PostalCode { get; set; }
        public string? CompanyImage { get; set; }
        
    }
}
