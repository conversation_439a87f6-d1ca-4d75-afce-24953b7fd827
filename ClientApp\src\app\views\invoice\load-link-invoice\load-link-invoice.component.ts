import { DecimalPipe } from './../../../pipes/decimal.pipe';
import { LayoutUtilsService } from './../../../core/services/layout-utils.service';
import { Location } from '@angular/common';
import { UserBusiness } from 'app/dto/interface/userBusiness.interface';
import { SharedModule } from 'app/module/shared.module';
import { InvoiceService } from './../../../service/invoice.service';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, inject, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { ToastService } from 'app/service/toast.service';
import { Invoice } from 'app/dto/interface/invoice.interface';
import { TranslateService } from '@ngx-translate/core';
import { AvatarModule } from 'ngx-avatars';
import { InnoStatusComponent } from 'app/component/inno-status/inno-status.component';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { getFullAddress, getFullAddressClient } from 'app/helpers/common.helper';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { SpinnerService } from 'app/service/spinner.service';
import { DuplicateInvoiceDialog } from 'app/service/dialog/dupicate-invoice.dialog'
import { PhoneMaskPipe } from 'app/pipes/phoneMask.pipe';
import { calculateGroupedTaxes, getNameTaxes } from 'app/utils/invoice.helper';
@Component({
  selector: 'app-load-link-invoice',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [
    SharedModule,
    MatFormFieldModule,
    AvatarModule,
    RouterModule,
    MatMenuModule,
    InnoStatusComponent,
    InnoPopoverComponent,
    FormatNumberPipe,
    PhoneMaskPipe,
    DecimalPipe,
  ],
  templateUrl: './load-link-invoice.component.html',
  styleUrl: './load-link-invoice.component.scss'

})
export class LoadLinkInvoiceComponent implements OnInit {
  titleStatus: string = "Draft"
  today = new Date();
  imageUrl!: string;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  pageSizesDefault: number = 10
  public selectionOptions: Object = { type: 'Multiple', checkboxOnly: true };
  InforInvoice: Invoice | undefined = undefined
  listChoosePayment: string[] = [];
  InforBussiness!: UserBusiness;
  isErrorTotal: boolean = false
  isErrorClient: boolean = false
  clientId!: string;
  reference!: string;
  note!: string;
  nameTax: string = ''
  sumtax: number = 0;
  subtotal: number = 0;
  qty!: number;
  total!: number;
  rate!: number;
  listTax: any[] = []
  taxArray: { name: string, total: number, numberTax: string, amount: number }[] = [];
  selectedDate!: string;
  selectedDueDate!: string;
  public calculateGroupedTaxes = calculateGroupedTaxes
  public getNameTaxes = getNameTaxes
  router = inject(Router);
  @ViewChild('actiontMenuTrigger') actiontMenuTrigger!: MatMenuTrigger;
  clientName!: string;
  destroyRef = inject(DestroyRef);
  _id!: string;
  protected activatedRoute = inject(ActivatedRoute);
  public listClient: any[] = [];
  private translate = inject(TranslateService);
  private _toastService = inject(ToastService)
  public _storeService = inject(StoreService)
  private _invoiceService = inject(InvoiceService)
  private duplicateInvoiceDialog = inject(DuplicateInvoiceDialog)
  constructor(
    private location: Location,
    private spinnerService: SpinnerService
  ) {
    this.InforBussiness = this._storeService.get_UserBusiness();
  }
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  convertToHours(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours + minutes / 60;
  }

  get businessInfo() {
    return {
      businessName: this.InforInvoice?.company?.businessName ?? '',
      businessPhoneNumber: this.InforInvoice?.company?.phone ?? '',
      businessAddress: getFullAddress({
        addressLine1: this.InforInvoice?.company?.adress ?? '',
        addressLine2: this.InforInvoice?.company?.adress2 ?? '',
        city: this.InforInvoice?.company?.city ?? '',
        stateProvince: this.InforInvoice?.company?.province ?? '',
        postalCode: this.InforInvoice?.company?.postalCode ?? '',
      }),
    }
  }
  get businessInfoClient() {
    return {
      businessAddress: getFullAddressClient({
        addressLine1: this.InforInvoice?.client?.addressLine1 ?? '',
        addressLine2: this.InforInvoice?.client?.addressLine2 ?? '',
        townCity: this.InforInvoice?.client?.townCity ?? '',
        stateProvince: this.InforInvoice?.client?.stateProvince ?? '',
        country: this.InforInvoice?.client?.country ?? '',
      }),
    }
  }

  handleDuplicate() {
    const dialogRef = this.duplicateInvoiceDialog.open({ id: this._id, isInvoice: true });

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) return
      })
    });

  }
  handleMarkAsPaid() {
    this._invoiceService.MarkAsPaid(this._id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this._toastService.showSuccess("Mark As Paid", "Success");
        this.GetInvoiceById(this._id);
      }
    }
    )
  }
  handleMarkAsSent() {
    this._invoiceService.MarkAsSent(this._id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this._toastService.showSuccess("Mark As Sent", "Success");
        this.GetInvoiceById(this._id);
      }
    }
    )
  }
  handleBack() {
    this.location.back();
  }

  ngOnInit(): void {
    this.activatedRoute.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params: any) => {
      if (params?.id) {
        this._id = params?.id
        this.GetInvoiceById(params?.id);
      }
    });

  }
  _handleData(_data: any) {
    let index = this.listClient.findIndex(x => x.clientId.toString() == this.InforInvoice?.clientId)
    if (index >= 0) {
      this.clientName = this.listClient[index].clientName
    }
    this.selectedDueDate = this.formatDate(new Date(this.InforInvoice?.dueDate ?? 0));
    this.selectedDate = this.formatDate(new Date(this.InforInvoice?.invoiceDate ?? 0));
    this.note = this.InforInvoice?.notes
    this.rate = this.InforInvoice?.rate ?? 0
    this.qty = this.InforInvoice?.timeAmount ?? 0
    this.total = this.InforInvoice?.paidAmount ?? 0
    this.reference = this.InforInvoice?.reference
    this.imageUrl = this.InforInvoice?.company?.companyImage
  }



  GetInvoiceById(_id: string) {
    this._invoiceService.GetInvoiceByIdLink(_id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.InforInvoice = res;
        this._handleData(res)
        this.calculateAllTax();
      }

    })
  }

  handleDownloadPDF() {
    this.spinnerService.show();
    this._invoiceService.PrintInvoiceById(this._id, this.InforInvoice.invoiceNumber);
  }


  _formatTotal(total: number) {
    return Math.floor(total * 1000) / 1000
  }
  calculateAllTax() {
    this.taxArray = []
    let totalTax = 0
    this.sumtax = 0;

    const resultTax = calculateGroupedTaxes(this.InforInvoice?.itemInvoices)
    this.taxArray = Object.values(resultTax.totalTaxes);
    this.sumtax = resultTax.grandTotalTax
    this.subtotal = this._formatTotal((this.InforInvoice?.totalAmount ?? 0) - this.CheckIsNaN(this.sumtax))

  }
  CheckIsNaN(value: any) {
    if (isNaN(value)) {
      return 0
    }
    return value;

  }

  calculateTotal(item: any) {
    let totalTax = 0
    let rate = item.rate;
    let qty = item.qty;

    // Truncate both rate and qty to 2 decimal places
    let truncatedRate = Math.floor(rate * 100) / 100;
    let truncatedQty = Math.floor(qty * 100) / 100;
    // Calculate total tax
    totalTax = truncatedRate * truncatedQty;

    return this._formatTotal(this.CheckIsNaN(totalTax));

  }



  onRowSelecting(event: any): void {
    if (event?.data?.length > 0) {
      event?.data.forEach((element: any) => {
        let index = this.listChoosePayment.findIndex(x => x == element?.id)
        if (index < 0) {
          this.listChoosePayment.push(element?.id)
        }
      });

    } else {
      let index = this.listChoosePayment.findIndex(x => x == event?.data?.id)
      if (index < 0) {
        this.listChoosePayment.push(event?.data?.id)
      }
    }

  }
  onRowDeselecting(event: any): void {
    if (event?.data?.length > 0) {
      this.listChoosePayment = [];
    }
    else {

      let index = this.listChoosePayment.findIndex(x => x == event.data?.id)
      if (index >= 0) {
        this.listChoosePayment.splice(index, 1)
      }
    }

  }



  getFullName(item: any) {
    if (item?.firstName && item?.lastName) {
      return item?.firstName + " " + item?.lastName
    }
    else {
      return item?.email ?? ""
    }

  }
  handleFunctionInDevelopment() {
    this._toastService.showInfo("The feature is in development.")
  }

}
