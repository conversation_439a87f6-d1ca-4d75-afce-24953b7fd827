import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  forwardRef,
  OnChanges,
  SimpleChanges,
  TemplateRef,
} from '@angular/core';
import { SharedModule } from '../../module/shared.module';
import { InnoErrorMMessageComponent } from '../inno-error-message/inno-error-message.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { InnoInputSearchResultComponent } from '../inno-input-search-result/inno-input-search-result.component';
import { InnoPopoverComponent } from '../inno-popover/inno-popover.component';
import { FormControl, NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';

@Component({
  selector: 'app-inno-form-select-search',
  templateUrl: './inno-form-select-search.component.html',
  styleUrls: ['./inno-form-select-search.component.scss'],
  standalone: true,
  imports: [
    SharedModule,
    InnoPopoverComponent,
    InnoInputSearchResultComponent,
    InnoErrorMMessageComponent
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InnoFormSelectSearchComponent),
      multi: true
    }
  ]
})
export class InnoFormSelectSearchComponent implements ControlValueAccessor, OnChanges {
  @Input() isRequired?: boolean;
  @Input() label?: string = '';
  @Input() options: IFilterDropdownOption[] = [];
  @Input() placeholder?: string = '';
  @Input() value?: any = '';
  @Input() projectId?: string = '';
  @Input() isProjectClient?: boolean = false;
  @Input() errorMessages?: { [key: string]: string };
  @Input() formControl?: FormControl;
  @Input() customOptionTemplate: TemplateRef<any> | null = null
  @Input() public isDisableSearch: boolean = false
  @Input() public isForYear: boolean = false

  @Output() onChange = new EventEmitter<any>();
  @Output() onSelect = new EventEmitter<IFilterDropdownOption>();
  @Output() onCreateNew = new EventEmitter<any>();

  public textSearch: string = ''
  public clientName: string = ''
  public labelOfValueSelected?: string = ''
  public listOptionPreview: IFilterDropdownOption[] = []
  public listOptionOriginal: IFilterDropdownOption[] = []

  @ViewChild(InnoPopoverComponent) searchResultComponent!: InnoPopoverComponent;

  constructor() { }

  registerOnChange(fn: (value: string) => void): void { }

  registerOnTouched(fn: () => void): void { }

  setDisabledState(isDisabled: boolean): void { }

  writeValue(value: string): void { }

  ngOnChanges(changes: SimpleChanges) {
    const newValue = changes?.['value']?.currentValue ?? null
    const newProjectId = changes?.['projectId']?.currentValue ?? null
    if (newValue) {
      this.labelOfValueSelected = this.listOptionOriginal.find(x => x.value === newValue)?.label
      this.clientName = this.listOptionOriginal.find(x => x.value === newValue)?.label!
    }
    const newListOptions = changes?.['options']?.currentValue
    if (newListOptions?.length) {
      this.options = newListOptions
      this.listOptionOriginal = this.options
      this.listOptionPreview = this.listOptionOriginal

      if (this.formControl?.value) {
        this.value = this.formControl.value
        this.labelOfValueSelected = this.listOptionOriginal.find(x => x.value === this.value)?.label
      }
    }
    if (newProjectId) {
      this.clientName = this.listOptionOriginal.find(x => x.value === newProjectId)?.metadata?.objectClient?.clientName
      this.labelOfValueSelected = this.clientName + "-" + this.listOptionOriginal.find(x => x.value === newProjectId)?.label

    } else {
      this.labelOfValueSelected = this.listOptionOriginal.find(x => x.value === newValue)?.label
    }
    if (this.value && !this.labelOfValueSelected) {
      this.labelOfValueSelected = this.listOptionOriginal.find(x => x.value === this.value)?.label
    }
  }

  get isShowCreateButton() {
    return (
      this.onCreateNew?.observed
      && this.textSearch.length
      && (!this.listOptionOriginal?.length || !this.listOptionPreview?.length)
    )
  }

  handleChange(event: any): void {
    if (this.onChange?.emit) {
      this.onChange.emit(event);
    }
  }

  hasError() {
    return (this.formControl?.invalid && (this.formControl.dirty || this.formControl.touched));
  }

  getErrorMessage(): string {
    if (!this.hasError()) return '';

    if (this.formControl?.errors && this.errorMessages) {
      for (const errorType in this.formControl.errors) {
        if (this.errorMessages[errorType]) {
          return this.errorMessages[errorType];
        }
      }
    }
    return '';
  }

  handleSearch(textSearch: string) {
    if (this.isProjectClient) {

      textSearch = textSearch?.trim()?.toLowerCase()
      if (!textSearch?.length) {
        this.listOptionPreview = this.listOptionOriginal
        return
      }

      this.listOptionPreview = this.listOptionOriginal
        .filter(e => e.label.toLowerCase().indexOf(textSearch) > -1)

      const result: any[] = [];
      this.listOptionPreview.forEach(element => {
        let projectAdd = false;
        let clientAdd = false;
        this.listOptionOriginal.filter(x => x.metadata.type == 'project').forEach((item: any) => {
          if (element.value == item.metadata.objectClient.id) {
            if (!projectAdd) {
              result.push(element)
              projectAdd = true;
            }
            result.push(item)
          }
          else {
            let checkClient = this.listOptionPreview.find(x => x.metadata?.type == 'client')
            if (!projectAdd && !checkClient) {
              let client = this.listOptionOriginal.find(x => x.metadata?.client?.id == element.metadata?.objectClient?.id)
              let check = result.find(x => x.value == element.metadata?.objectClient?.id)
              if (!check) {
                clientAdd = true;
                result.push(client)
              }
              if (clientAdd || check) {
                result.push(element)
                projectAdd = true;

              }
            }
          }

        });
      });

      this.listOptionPreview = result
    }
    else {
      textSearch = textSearch?.trim()?.toLowerCase()
      this.textSearch = textSearch
      if (!textSearch?.length) {
        this.listOptionPreview = this.listOptionOriginal
        return
      }

      this.listOptionPreview = this.listOptionOriginal
        .filter(e => e.label.toLowerCase().indexOf(textSearch) > -1)

      const result: any[] = [];
      this.listOptionPreview.forEach(element => {
        result.push(element);
        this.listOptionOriginal.filter(x => x.metadata?.type == 'project').forEach((item: any) => {
          if (element.value == item.metadata.objectClient.id) {
            result.push(item)
          }

        });
      });
    }
  }
  public handleCloseSearchResult() {
    if (!this.searchResultComponent) return
    this.searchResultComponent.handleHideContent()
  }

  public touchControl() {
    if (!this.formControl) return

    this.formControl.markAsDirty()
    this.formControl.markAsTouched()
  }

  handleChooseOption(item: IFilterDropdownOption) {
    if (item.value == this.value) return
    if (this.formControl) this.formControl.setValue(item.value)
    this.labelOfValueSelected = item.label
    this.value = item.value
    this.onSelect.emit(item)
    this.handleCloseSearchResult()
  }

  callbackAfterHideSearchResult() {
    this.listOptionPreview = this.listOptionOriginal
  }

  handleCreateNew() {
    this.onCreateNew.emit(this.textSearch)
    this.handleCloseSearchResult()
  }
}
