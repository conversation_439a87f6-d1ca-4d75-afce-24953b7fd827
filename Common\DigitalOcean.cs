﻿
using Amazon.S3;
using Amazon.S3.Model;
using Amazon.S3.Transfer;
using InnoBook.Extension;
using InnoBook.Filter;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using Minio;
using Minio.DataModel;
using Minio.DataModel.Args;
using SixLabors.ImageSharp;
using SkiaSharp;
using System.IO;
namespace InnoBook.Common
{
    public class DigitalOcean
    {
        //public static string filePath = "d:\\test upload.txt";
        public static IFormFile Base64ToIFormFile(string base64String, string fileName, string contentType)
        {
            var base64Data = base64String.Split(',')[1];
            var bytes = Convert.FromBase64String(base64Data);
            var stream = new MemoryStream(bytes);

            return new FormFile(stream, 0, stream.Length, "file", fileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = contentType
            };
        }
        public static async Task<bool> UploadFileAsync(IFormFile formFile, IConfiguration configuration,string companyId)
        {
            try
            {

                string bucketName = configuration.GetRequired("DigitalOceanSpace:BucketName");
                string accessKey = configuration.GetRequired("DigitalOceanSpace:Key");
                string secretKey = configuration.GetRequired("DigitalOceanSpace:Secret");

                // Initialize the S3 client
                var s3ClientConfig = new AmazonS3Config
                {
                    Timeout = TimeSpan.FromMinutes(10),
                    ForcePathStyle = true,
                    RegionEndpoint = Amazon.RegionEndpoint.USEast1
                };
                s3ClientConfig.ServiceURL = configuration.GetRequired("DigitalOceanSpace:Endpoint");
                using var s3Client = new AmazonS3Client(accessKey, secretKey, s3ClientConfig);

                // Set up the transfer utility and upload request
                using var memoryStream = new MemoryStream();
                await formFile.CopyToAsync(memoryStream);
                memoryStream.Position = 0;

                var uploadRequest = new TransferUtilityUploadRequest
                {
                    BucketName = bucketName,
                    Key = companyId+"/"+ formFile.FileName,
                    InputStream = memoryStream,
                    ContentType = formFile.ContentType,
                    CannedACL = S3CannedACL.PublicRead,
                    PartSize = 5 * 1024 * 1024, // 5 MB chunks for large files
                };

                var transferUtility = new TransferUtility(s3Client);
                await transferUtility.UploadAsync(uploadRequest);

                return true;

            }
            catch (Exception ex)
            {
                return false;
            }
        }
        public async Task<byte[]?> DownloadImageToTempAsync(IConfiguration configuration, string idCompany, string imageUrl)
        {
            var (stream, contentType, fileName) = await GetFileMinio(configuration, idCompany, imageUrl);
            return stream.ToArray();
        }
        public string GetFileURL(IConfiguration configuration, string IdCompany, string fileName)
        {

            try
            {
                var s3ClientConfig = new AmazonS3Config
                {

                    ServiceURL = configuration["DigitalOceanSpace:Endpoint"],
                    ForcePathStyle = true,
                    //   SignatureVersion = "v4",
                };
                var s3Client = new AmazonS3Client(configuration.GetRequired("DigitalOceanSpace:Key"), configuration.GetRequired("DigitalOceanSpace:Secret"), s3ClientConfig);


                string bucketName = configuration.GetRequired("DigitalOceanSpace:BucketName");


                var request = new GetPreSignedUrlRequest
                {
                    BucketName = bucketName,
                    Key = IdCompany + "/" + fileName,
                    Expires = DateTime.UtcNow.AddMinutes(15) // URL expires in 15 minutes
                };
                string url = s3Client.GetPreSignedURL(request);
                return url;
            }
            catch (Exception exception)
            {
                throw new ApiException("Erreur téléchargement image");
            }
        }
        public async Task<(MemoryStream, string, string)> GetFile(IConfiguration configuration, string IdCompany, string fileName)
        {

            //System.ArgumentOutOfRangeException error with hex, it means that the AWS SDK is
            //still trying to process the ETag even though you are not accessing it manually.
            //This usually happens when you are using GetObjectAsync,
            //and the SDK is trying to validate the data internally with the ETag.
            string folderName = configuration.GetRequired("DigitalOceanSpace:BucketName");
            string test = configuration.GetRequired("DigitalOceanSpace:Endpoint");
            var s3ClientConfig = new AmazonS3Config
            {

                ServiceURL = configuration["DigitalOceanSpace:Endpoint"],
                ForcePathStyle = true,
      //          SignatureVersion = "v4",
            };
            var s3Client = new AmazonS3Client(configuration.GetRequired("DigitalOceanSpace:Key"), configuration.GetRequired("DigitalOceanSpace:Secret"), s3ClientConfig);

            try
            {
                string bucketName = configuration.GetRequired("DigitalOceanSpace:BucketName");

                var objectRequest = new GetObjectRequest
                {
                    BucketName = bucketName,
                    Key = IdCompany + "/" + fileName // change it by the file you want to download
                };


                using var response = await s3Client.GetObjectAsync(objectRequest);
                using var responseStream = response.ResponseStream;
                var memoryStream = new MemoryStream();
                await responseStream.CopyToAsync(memoryStream);
                memoryStream.Position = 0; // Réinitialiser le stream avant de le retourner

                // Renvoyer le fichier sous forme de MemoryStream + son type MIME et son nom
                return (memoryStream, response.Headers["Content-Type"], fileName);

            }
            catch (AmazonS3Exception exception)
            {
                throw new Exception(exception.ToString());
            }
            catch (Exception exception)
            {
                throw new Exception(exception.ToString());
            }
        }
    
        public async Task<(MemoryStream, string, string)>GetFileMinio(IConfiguration configuration,string IdCompany, string fileName)
        {

            try
            {
                string bucketName = configuration.GetRequired("DigitalOceanSpace:BucketName");
                string endpoint = configuration.GetRequired("DigitalOceanSpace:Endpoint")
                    .Replace("https://", "").Replace("http://", ""); 
                string accessKey = configuration.GetRequired("DigitalOceanSpace:Key");
                string secretKey = configuration.GetRequired("DigitalOceanSpace:Secret");

                var minio = new MinioClient()
                    .WithEndpoint(endpoint)
                    .WithCredentials(accessKey, secretKey)
                    .WithSSL() 
                    .Build();

                string objectKey = $"{IdCompany}/{fileName}";
                var memoryStream = new MemoryStream();

                var getObjectArgs = new GetObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(objectKey)
                   .WithCallbackStream(stream => stream.CopyTo(memoryStream));

                await minio.GetObjectAsync(getObjectArgs);

                string contentType = GetMimeTypeFromFileNameOrUri(fileName);
                memoryStream.Position = 0;
                return (memoryStream, contentType, fileName);
            }
            catch (Exception ex)
            {
                throw new Exception($"Error getting file from DigitalOcean Spaces: {ex.Message}", ex);
            }
        }
        private string GetMimeTypeFromFileNameOrUri(string input)
        {
            string fileName;
            if (Uri.IsWellFormedUriString(input, UriKind.Absolute))
            {
                var uri = new Uri(input);
                fileName = Path.GetFileName(uri.LocalPath);
            }
            else
                fileName = input;

            var provider = new FileExtensionContentTypeProvider();
            if (!provider.TryGetContentType(fileName, out string contentType))
            {
                contentType = "application/octet-stream";
            }

            return contentType;
        }
    }
}
