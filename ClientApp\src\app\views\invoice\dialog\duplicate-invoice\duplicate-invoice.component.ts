import { User } from 'app/dto/interface/user.interface';
import { AddNewItemDialog } from 'app/service/dialog/add-new-item-invoice.dialog';
import { DecimalPipe } from './../../../../pipes/decimal.pipe';
import { InvoiceService } from './../../../../service/invoice.service';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, Inject, inject, OnInit, ViewChild } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { SharedModule } from 'app/module/shared.module';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from 'app/service/toast.service';
import moment from 'moment-timezone';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoFormSelectSearchComponent } from 'app/component/inno-form-select-search/inno-form-select-search.component';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoFormDatepickerComponent } from 'app/component/inno-form-datepicker/inno-form-datepicker.component';
import { InnoFormTextareaComponent } from 'app/component/inno-form-textarea/inno-form-textarea.component';
import { InnoTableActionComponent } from 'app/component/inno-table-action/inno-table-action.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';

import { DropdownOptionsService } from 'app/service/dropdown-options.service';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { AvatarModule } from 'ngx-avatars';
import { InnoUploadComponent } from 'app/component/inno-upload/inno-upload.component';
import { getBase64AndFileName, getFullAddress } from 'app/helpers/common.helper';
import { Router, RouterModule } from '@angular/router';
import { DataService } from 'app/service/data.service';
import { Invoice } from 'app/dto/interface/invoice.interface';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { LayoutUtilsService } from 'app/core/services/layout-utils.service';
import { ModifyInvoiceItemDialog } from '../../../../service/dialog/modify-invoice-item.dialog';
import { ModifyTaxesDialog } from '../../../../service/dialog/modify-taxes.dialog';
import { SelectTimeTrackingDialog } from '../../../../service/dialog/select-time-tracking.dialog';
import { SendInvoiceDialog } from '../../../../service/dialog/send-invoice.dialog';
import {
  CdkDragDrop,
  CdkDrag,
  CdkDropList,
  CdkDropListGroup,
  moveItemInArray,
  transferArrayItem,
} from '@angular/cdk/drag-drop';
import { calculateTotalInvoiceItem, getNameTaxes } from 'app/utils/invoice.helper';
interface IModifyDuplicate {
  id?: string;
  isInvoice: boolean
}
@Component({
  selector: 'app-edit-invoice',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [
    SharedModule,
    AvatarModule,
    RouterModule,
    InnoModalWrapperComponent,
    InnoFormSelectSearchComponent,
    InnoFormInputComponent,
    InnoFormDatepickerComponent,
    InnoFormTextareaComponent,
    InnoTableActionComponent,
    InnoModalFooterComponent,
    InnoUploadComponent,
    FormatNumberPipe,
    DecimalPipe,
    CdkDropListGroup,
    CdkDropList,
    CdkDrag
  ],
  templateUrl: './duplicate-invoice.component.html',
  styleUrl: './duplicate-invoice.component.scss'
})
export class DuplicateInvoiceComponent implements OnInit {
  private invoiceNumber: string = "0000001"
  inforUser: User
  title: string = ""
  public invoiceForm!: UntypedFormGroup;
  public itemInvoice2 = [];
  public calculateTotalInvoiceItem = calculateTotalInvoiceItem
  public getNameSelectedTaxes = getNameTaxes
  public projectAndClientOptions: IFilterDropdownOption[] = []
  public subtotal: number = 0
  public totalAmount: number = 0
  public selectedDateStart = this.formatDate(new Date())
  public selectedDateEnd = this.formatDate(new Date());
  taxArray: { name: string, total: number, numberTax: string, amount: number }[] = [];
  InforInvoice: Invoice | undefined = undefined

  private formBuilder = inject(UntypedFormBuilder)
  private base64!: string;
  private filename!: string;
  private payment: any[] = []
  public sumtax: number = 0;
  private listTax: any[] = []
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  private _toastService = inject(ToastService)
  public _storeService = inject(StoreService)
  private _invoiceService = inject(InvoiceService)
  private dataService = inject(DataService)
  private dropdownOptionService = inject(DropdownOptionsService)
  private layoutUtilsService = inject(LayoutUtilsService)
  @ViewChild('selectSearchClientElement') selectSearchClientElement!: InnoFormSelectSearchComponent;
  static getComponent(): typeof DuplicateInvoiceComponent {
    return DuplicateInvoiceComponent;
  }


  constructor(public dialogRef: MatDialogRef<DuplicateInvoiceComponent>,
    private modifyInvoiceItemDialog: ModifyInvoiceItemDialog,
    private modifyTaxesDialog: ModifyTaxesDialog,
    private addNewItemDialog: AddNewItemDialog,
    private selectTimeTrackingDialog: SelectTimeTrackingDialog,
    private sendInvoiceDialog: SendInvoiceDialog,
    @Inject(MAT_DIALOG_DATA) public data?: IModifyDuplicate) {
    this.data.isInvoice ? this.title = "Duplicate Invoice" : this.title = "Duplicate Estimate"

    this.invoiceForm = this.formBuilder.group({
      clientId: ["", Validators.compose([Validators.required])],
      invoiceDate: [null, Validators.compose([Validators.required])],
      dueDate: [null, Validators.compose([Validators.required])],
      invoiceNumber: [],
      projectId: [],
      notes: [""],
      itemInvoice: [],
    });

    this.invoiceForm.get('itemInvoice')?.valueChanges.subscribe(listInvoice => {
      this.subtotal = 0
      listInvoice?.forEach((invoiceItem: any) => {
        const totalInvoiceItem = calculateTotalInvoiceItem(invoiceItem?.rate, invoiceItem?.qty);
        this.subtotal = Math.floor((this.subtotal + totalInvoiceItem) * 100) / 100;
      })

      // Calculate with discount if exist

      this.calculateAllTax();
    });

    this.invoiceForm.get('invoiceNumber')?.disable();
    this.inforUser = this._storeService.get_InforUser();
  }

  async handleSelectProject(item: IFilterDropdownOption) {

    let newClientId = ""
    let newProjectID = ""

    if (item.metadata?.type == 'client') {
      newClientId = item.value
      newProjectID = ""
    } else {
      newClientId = item.metadata?.objectClient?.id
      newProjectID = item.value
    }

    const currentClientId = this.f['clientId'].value
    const isSameClient = currentClientId === newClientId
    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    if (!isSameClient && currentPaymentInvoice.length) {
      const isConfirm = await this.layoutUtilsService.alertConfirm({
        title: 'Warning',
        description: 'You are changing the client, and the invoices will be reset. Are you sure you want to continue?',
      })
      if (!isConfirm) return;
      this.f['itemInvoice'].setValue([])
    }

    this.f['clientId'].setValue(newClientId)
    this.f['projectId'].setValue(newProjectID)
    this.selectSearchClientElement.handleCloseSearchResult()
  }
  _handleData(_data: Invoice) {
    this.f['notes'].setValue(_data?.notes)
    this.f['itemInvoice'].setValue(_data?.itemInvoices)
    this.f['invoiceDate'].setValue(_data?.invoiceDate)
    this.f['dueDate'].setValue(this.data.isInvoice ? _data?.dueDate : new Date())
    this.f['clientId'].setValue(_data?.clientId)
    this.f['projectId'].setValue(_data?.projectId)
  }
  GetInvoiceById(_id: string) {
    this._invoiceService.GetInvoiceById(_id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.InforInvoice = res;
        this._handleData(res)
        this.calculateAllTax();

      }

    })
  }
  CountInvoiceByCompany() {
    this._invoiceService.CountInvoiceByCompany().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {

        if (res == 0) {
          const number = 1;
          this.invoiceNumber = number.toString().padStart(7, '0');
          this.f['invoiceNumber'].setValue(this.invoiceNumber)
          return;
        }
        const number = res + 1;
        this.invoiceNumber = number.toString().padStart(7, '0');
        this.f['invoiceNumber'].setValue(this.invoiceNumber)
      }
      else {
        const number = 1;
        this.invoiceNumber = number.toString().padStart(7, '0');
        this.f['invoiceNumber'].setValue(this.invoiceNumber)
        return;

      }
    }
    )
  }
  ngOnInit(): void {
    this.dropdownOptionService
      .getDropdownOptionsProjectAndClient()
      .then((projectAndClientOptions) => this.projectAndClientOptions = projectAndClientOptions)
    this.GetInvoiceById(this.data?.id)
    if (this.data.isInvoice) {
      this.CountInvoiceByCompany();
      return;
    }
    this.CountEstimate();
  }
  getFullName(item: any) {
    if (item) {
      if (item?.firstName && item?.lastName) {
        return item?.firstName + " " + item?.lastName
      }
      else {
        return item?.email ?? ""
      }
    }
    else {
      if (this.inforUser?.firstName && this.inforUser?.lastName) {
        return this.inforUser?.firstName + " " + this.inforUser?.lastName
      }
      else {
        return this.inforUser?.email ?? ""
      }
    }



  }
  get businessInfo() {
    const business = this._storeService.get_UserBusiness();

    return {
      businessName: business?.company?.businessName ?? '',
      businessPhoneNumber: business?.company?.phone ?? '',
      businessAddress: getFullAddress({
        addressLine1: business?.company?.adress ?? '',
        addressLine2: business?.company?.adress2 ?? '',
        stateProvince: business?.company?.province ?? '',
        postalCode: business?.company?.postalCode ?? '',
        country: business?.company?.country ?? '',
      }),
    }
  }

  get f() {
    return this.invoiceForm.controls as Record<string, FormControl>;;
  }

  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  convertToHours(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours + minutes / 60;
  }


  calculateAllTax() {
    this.taxArray = [];
    let totalTax = 0
    this.sumtax = 0;
    this.f['itemInvoice'].value.forEach((element: any) => {
      element.taxes.forEach((tax: any) => {
        if (tax?.companyTax) {
          totalTax += Math.floor(isNaN(element.rate * element.qty * (tax?.companyTax?.amount / 100)) ? 0 : element.rate * element.qty * (tax?.companyTax?.amount / 100) * 100) / 100
          // handle tax
          const taxAmount = Number(Math.floor((isNaN(element.rate * element.qty * (tax?.companyTax?.amount / 100)) ? 0 : element.rate * element.qty * (tax?.companyTax?.amount / 100)) * 100) / 100) || 0
          const existingTax = this.taxArray.find(item => item.name === tax?.companyTax?.name);
          if (existingTax) {
            existingTax.total += taxAmount;
            existingTax.amount = tax.companyTax?.amount ?? 0
          } else {
            this.taxArray.push({ name: tax.companyTax?.name, total: taxAmount, numberTax: tax.companyTax?.taxeNumber, amount: tax.companyTax?.amount });
          }
        }
        else {
          if (tax.selected) {
            totalTax += Math.floor(isNaN(element.rate * element.qty * (tax?.amount / 100)) ? 0 : element.rate * element.qty * (tax?.amount / 100) * 100) / 100
            // handle tax
            const taxAmount = Math.floor((isNaN(element.rate * element.qty * (tax?.amount / 100)) ? 0 : element.rate * element.qty * (tax?.amount / 100)) * 100) / 100
            const existingTax = this.taxArray.find(item => item.name === tax?.name);
            if (existingTax) {
              existingTax.total += taxAmount;
              existingTax.amount = tax?.amount;
            } else {
              this.taxArray.push({ name: tax?.name, total: taxAmount, numberTax: tax?.taxeNumber, amount: tax?.amount });
            }
          }
        }
      });
    });
    this.sumtax += totalTax

    this.totalAmount = this.subtotal + (isNaN(this.sumtax) ? 0 : this.sumtax)
  }
  _formatTotal(total: number) {
    return Math.floor(total * 1000) / 1000
  }


  handleDeleteInvoiceItem(index: number) {
    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    if (!currentPaymentInvoice?.length) return

    currentPaymentInvoice.splice(index, 1)
    this.f['itemInvoice'].setValue(currentPaymentInvoice)
  }
  CountEstimate() {
    this._invoiceService.CountEstimate().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {

        if (res == 0) {
          const number = 1;
          this.invoiceNumber = number.toString().padStart(7, '0');
          this.f['invoiceNumber'].setValue(this.invoiceNumber)
          return;
        }
        const number = res + 1;
        this.invoiceNumber = number.toString().padStart(7, '0');
        this.f['invoiceNumber'].setValue(this.invoiceNumber)
      }
      else {
        const number = 1;
        this.invoiceNumber = number.toString().padStart(7, '0');
        this.f['invoiceNumber'].setValue(this.invoiceNumber)
        return;

      }
    }
    )
  }
  handleCancel() {
    this.dialogRef.close();
  }

  get getInvoicePayload() {
    let invoice: Invoice;
    if (this.invoiceForm.invalid) return null;
    if (this.data.isInvoice) {
      invoice = {
        clientId: this.f['clientId'].value,
        invoiceNumber: this.f['invoiceNumber'].value,
        invoiceDate: moment.utc(this.f['invoiceDate'].value).toDate(),
        dueDate: moment.utc(this.f['dueDate'].value).toDate(),
        reference: '',
        isEstimate: this.data.isInvoice ? false : true,
        notes: this.f['notes'].value,
        projectId: this.f['projectId'].value,
        payments: this.payment,
        taxes: this.listTax,
        base64: this.base64,
        filename: this.filename,
        itemInvoices: this.f['itemInvoice'].value.map(({ user, project, id, service, ...item }) => ({
          ...item,
          taxes: item['taxes'].some(tax => tax.companyTax)
            ? item['taxes'].map(({ companyTax, ...rest }) => rest)
            : item['taxes'].filter(tax => tax.selected)
        })),
        paidAmount: this.subtotal,
        taxAmount: this.sumtax,
        totalAmount: this.totalAmount,
        rate: 0,
        status: 0,
        timeAmount: 0,
      };
    }
    else {
      invoice = {
        clientId: this.f['clientId'].value,
        invoiceNumber: this.f['invoiceNumber'].value,
        invoiceDate: moment.utc(this.f['invoiceDate'].value).toDate(),
        reference: '',
        isEstimate: this.data.isInvoice ? false : true,
        notes: this.f['notes'].value,
        projectId: this.f['projectId'].value,
        payments: this.payment,
        taxes: this.listTax,
        base64: this.base64,
        filename: this.filename,
        itemInvoices: this.f['itemInvoice'].value.map(({ user, project, id, service, ...item }) => ({
          ...item,
          taxes: item['taxes'].some(tax => tax.companyTax)
            ? item['taxes'].map(({ companyTax, ...rest }) => rest)
            : item['taxes'].filter(tax => tax.selected)
        })),
        paidAmount: this.subtotal,
        taxAmount: this.sumtax,
        totalAmount: this.totalAmount,
        rate: 0,
        status: 0,
        timeAmount: 0,
      };
    }

    return invoice;
  }

  handleAddUnBillTime() {
    const clientId = this.f['clientId'].value
    if (!clientId) {
      this._toastService.showWarning("No selected client", "Please select a client to add the time.")
      return
    }

    const client = this.projectAndClientOptions.find(item => item.value === clientId)?.metadata?.client
    if (!client) return this._toastService.showWarning("Not fount client")


    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    const listIdTimeTrackingSelected = currentPaymentInvoice.map(item => item.trackingId).filter(x => x)
    const payload = {
      client,
      listIdTimeTrackingSelected
    }



    const dialogRef = this.selectTimeTrackingDialog.open(payload);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((_listTimeTrackingSelected) => {
        if (!_listTimeTrackingSelected?.length) return

        currentPaymentInvoice.push(..._listTimeTrackingSelected)
        this.f['itemInvoice'].setValue(currentPaymentInvoice)
      })
    });
  }
  handleClose() {
    this.dialogRef.close();
  }
  handleSave() {
    if (this.invoiceForm.invalid) {
      this.markAllControlsAsTouched();
      return
    }
    this._invoiceService.CreatedInvoice(this.getInvoicePayload)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(res => {
        if (res) {
          this.dataService.triggerRefreshInvoice()
          this.dialogRef.close(res)
          this._toastService.showSuccess("Save", "Success");
        }
      })
  }
  handleAddNewItem() {
    const dialogRef = this.addNewItemDialog.open(this.getInvoicePayload);
    const currentPaymentInvoice = [...(this.f['itemInvoice']?.value ?? [])]
    dialogRef.then((c) => {
      c.afterClosed().subscribe((itemnew) => {
        if (!itemnew?.length) return
        // const updatedData = itemnew.map(({ id, taxes, ...rest }) => ({
        //   ...rest,
        //   taxes: taxes?.map(({ id, itemId, serviceId, ...taxRest }) => taxRest)
        // }));
        const updatedData = itemnew.map(({ id, taxes, itemName, serviceName, projectName, ...rest }) => ({
          ...rest,
          description: itemName,
          projectName: projectName == "" ? null : projectName ?? null,
          serviceName: serviceName == "" ? null : serviceName ?? null,
          user: this.inforUser,
          taxes: taxes?.map(({ id, itemId, serviceId, ...taxRest }) => taxRest)
        }));
        currentPaymentInvoice.push(...updatedData)
        this.f['itemInvoice'].setValue(currentPaymentInvoice)
      })
    });
  }
  handleSendInvoice() {
    if (this.invoiceForm.invalid) {
      this.markAllControlsAsTouched();
      this._toastService.showWarning("Please fill in all the invoice information completely.", " ")
      return
    }


    const dialogRef = this.sendInvoiceDialog.open(this.getInvoicePayload);
    dialogRef.then((c) => {
      c.afterClosed().subscribe(res => {
        if (!res) return
        this.dataService.triggerRefreshInvoice()
        this.dialogRef.close()
      })
    });
  }

  handleModifyInvoiceItem(index?: number, item?: any) {
    const dialogRef = this.modifyInvoiceItemDialog.open(item);

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) return
        const currentPaymentInvoice = this.f['itemInvoice'].value ?? []
        if (index === undefined) {
          currentPaymentInvoice.push(res)
          this.f['itemInvoice'].setValue(currentPaymentInvoice)

        }
        else {
          currentPaymentInvoice[index] = res
          this.f['itemInvoice'].setValue(currentPaymentInvoice)
        }

      })
    });


  }

  handleSelectClient(item: IFilterDropdownOption) {
    this.f["clientId"].setValue(item.value)
    this.selectSearchClientElement.handleCloseSearchResult()
  }
  RouterSetting() {
    this.dialogRef.close();
    this.router.navigate(["/settings/business"])
  }
  async handleChangePicture(files: any) {
    // this._invoiceService.uploadFile(files[0]).subscribe(res => {
    // }
    // )
    const pictureFile = files?.[0]
    const { base64, fileName } = await getBase64AndFileName(pictureFile)

    this.base64 = base64
    this.filename = fileName;
  }
  handleModifyTaxes(item: any, index: number) {
    let taxes = [];
    item.forEach(itemtax => {
      if (itemtax.companyTax) {
        itemtax.companyTax.selected = true;
        taxes.push(itemtax.companyTax)
      }
      else {
        taxes.push(itemtax)
      }
    });

    const itemInvoiceOld = structuredClone(this.f['itemInvoice'].value);

    const dialogRef = this.modifyTaxesDialog.open(taxes.filter(x => x.selected));

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) {
          this.f['itemInvoice'].setValue(itemInvoiceOld)
          return;
        }
        const currentPaymentInvoice = this.f['itemInvoice'].value ?? []
        itemInvoiceOld.forEach((item: any, indexOld: number) => {
          if (index != indexOld) {
            currentPaymentInvoice[indexOld].taxes = item.taxes
          }
        });


        currentPaymentInvoice[index].taxes = res.taxes.filter(x => x.selected == true)
        this.f['itemInvoice'].setValue(currentPaymentInvoice)
        if (this._storeService.get_ApplyTaxAll()) {
          this._storeService.set_ApplyTaxAll(false)
          let temp = this.f['itemInvoice'].value
          temp.forEach((element: any) => {
            if (element.taxes.length > 0) {
              res.taxes.filter(x => x.selected == true).forEach((item: any) => {
                const exists = element.taxes.some((existingTax: any) => {
                  if (existingTax.companyTax) {
                    return existingTax?.companyTax.name === item?.name;
                  }
                  return existingTax.name === item.name;
                });
                if (!exists) {
                  element.taxes.push(item);
                }
              });
            } else {
              res.taxes.forEach((item: any) => {
                element.taxes.push(item)
              });
            }


          });
          this.calculateAllTax()
        }
      })
    });

  }
  drop(event: CdkDragDrop<string[]>) {
    if (event.previousContainer === event.container) {
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    } else {
      transferArrayItem(
        event.previousContainer.data,
        event.container.data,
        event.previousIndex,
        event.currentIndex,
      );

    }
  }
  CheckIsNaN(value: any) {
    if (isNaN(value)) {
      return 0
    }
    return value;

  }
}
