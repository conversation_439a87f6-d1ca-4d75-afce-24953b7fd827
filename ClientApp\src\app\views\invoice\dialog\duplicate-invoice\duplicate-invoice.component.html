<app-inno-modal-wrapper [title]="title" (onClose)="handleClose()">
    <div class="w-full p-[16px]">
        <div class="flex w-full gap-[18px] xl:flex-row flex-col">
            <!-- Left column - Thumbnail -->
            <div class="w-[160px] shrink-0 mx-auto md:mx-[unset]">
                <app-inno-upload
                    onerror="this.src='../../../../assets/img/image_default.svg'"
                    (onChange)="handleChangePicture($event)" />
            </div>

            <!-- Right column -->
            <div class="w-full flex flex-col gap-[16px]">
                <!-- Company info -->
                <div class="w-full">
                    @if(!businessInfo.businessName)
                    {
                    <p class="text-text-md-semibold text-text-primary mb-[1px]">
                        Loading...
                    </p>
                    }

                    @if(businessInfo.businessPhoneNumber) {
                    <div class="flex items-center gap-[8px]">
                        <!-- <p class="text-text-xs-regular text-text-secondary">
                  business InnoBooks.com
                </p>
                <div class="shrink-0 rounded-full bg-object-disabled-slight w-[4px] h-[4px]"></div> -->
                        <p class="text-text-xs-regular text-text-secondary">
                            {{ businessInfo.businessPhoneNumber }}
                        </p>
                    </div>
                    }

                    @if(businessInfo.businessAddress){
                    <p class="w-full text-text-xs-regular text-text-secondary">
                        {{ businessInfo.businessAddress }}
                    </p>
                    }

                    <button (click)="RouterSetting()"
                        class="button-link-primary mt-[3px]">
                        Edit Business Information
                    </button>
                </div>

                <!-- Client -->
                <div
                    class="w-full grid md:grid-cols-2 lg:grid-cols-4 gap-[16px]">
                    <app-inno-form-select-search
                        #selectSearchClientElement
                        label="Sent to Client/Project"
                        [options]="projectAndClientOptions"
                        [formControl]="f['clientId']"
                        [projectId]="f['projectId'].value"
                        [value]="f['clientId'].value"
                        placeholder="Select client/project"
                        [errorMessages]="{ required: 'Client is required' }"
                        [customOptionTemplate]="projectOptionTemplate">
                        <!-- <ng-template #projectOptionTemplate let-item>
                <div (click)="handleSelectClient(item)"
                  class="w-full flex p-[8px] items-center gap-[10px] hover:bg-bg-secondary rounded-md cursor-pointer">
                  <ngx-avatars
                    [size]="32"
                    [name]="item.label" />
                  <div class="w-full">
                    <p
                      class="line-clamp-1 text-text-primary text-text-sm-regular">
                      {{ item.label }}
                    </p>
                  </div>
                </div>
              </ng-template> -->
                        <ng-template #projectOptionTemplate let-item>
                            <div
                                (click)="handleSelectProject(item)"
                                [ngClass]="{'pl-8': item?.metadata?.type == 'project'}"
                                class="w-full flex p-[8px] items-center gap-[10px] rounded-md cursor-pointer hover:bg-bg-brand-primary"
                                [class.selected]="item.value === f['projectId'].value">
                                @if(item?.metadata?.type == 'client') {
                                <ngx-avatars
                                    [size]="32"
                                    [name]="item.label" />
                                } @else {
                                <div
                                    class="w-[32px] h-[32px] rounded-full overflow-hidden flex justify-center items-center bg-bg-brand-primary shrink-0">
                                    <img class="w-[16px]"
                                        src="../../../assets/img/icon/ic_file_green.svg"
                                        alt="Icon">
                                </div>
                                }
                                <div class="w-full">
                                    <p
                                        class="line-clamp-1 text-text-primary text-text-sm-regular txtTitle">
                                        {{ item.label }}
                                    </p>
                                    @if(item.metadata?.description) {
                                    <p
                                        class="line-clamp-1 text-text-tertiary text-text-xs-regular txtDescription">
                                        {{ item.metadata.description }}
                                    </p>
                                    }
                                </div>
                            </div>
                        </ng-template>
                    </app-inno-form-select-search>

                    <app-inno-form-input
                        type="number"
                        label="Invoice Number"
                        placeholder="Enter invoice number"
                        [formControl]="f['invoiceNumber']"
                        [value]="f['invoiceNumber'].value" />
                    <app-inno-form-datepicker
                        label="Issue Date"
                        placeholder="Select issue date"
                        [formControl]="f['invoiceDate']"
                        [value]="f['invoiceDate'].value"
                        [errorMessages]="{ required: 'Invoice date is required' }" />
                    @if(data.isInvoice)
                    {
                    <app-inno-form-datepicker
                        label="Due Date"
                        placeholder="Select due date"
                        [formControl]="f['dueDate']"
                        [value]="f['dueDate'].value"
                        [errorMessages]="{ required: 'Due date is required' }" />
                    }

                </div>

                <!-- Description -->
                <div class="w-full">
                    <app-inno-form-textarea
                        label="Description"
                        placeholder="A brief description of the invoice details."
                        [formControl]="f['notes']" />
                </div>
            </div>
        </div>

        <div
            class="w-full mt-[16px] border-t border-dashed border-border-primary">
            <div class="flex gap-1.5">
                <button (click)="handleAddUnBillTime()"
                    class="button-outline-primary button-size-md mt-[16px]">
                    Add Unbill Time
                </button>
                <button class="button-outline button-size-md mt-[16px]"
                    type="button"
                    (click)="handleAddNewItem()">
                    + Add New Item
                </button>
            </div>
            <div class="overflow-auto w-full" cdkDropList
                [cdkDropListData]="f['itemInvoice'].value"
                (cdkDropListDropped)="drop($event)">
                <div class="invoiceTableLayout">
                    <p class="text-text-tertiary text-text-sm-semibold">
                        Invoice Item
                    </p>
                    <p class="text-text-tertiary text-text-sm-semibold">
                        Rate
                    </p>
                    <p class="text-text-tertiary text-text-sm-semibold">
                        Quantity
                    </p>
                    <p class="text-text-tertiary text-text-sm-semibold">
                        Tax
                    </p>
                    <p class="text-text-tertiary text-text-sm-semibold">
                        Line Total
                    </p>
                </div>
                @for(invoiceItem of (f['itemInvoice'].value); track invoiceItem;
                let i = $index) {
                <div class="invoiceTableLayout" cdkDrag>
                    <div class=" flex flex-col">
                        @if(invoiceItem?.project)
                        {
                        <p class="text-text-primary text-text-md-semibold">
                            {{ invoiceItem?.project?.projectName ?? '' }}
                        </p>
                        }
                        @else{
                        <p class="text-text-primary text-text-md-semibold">
                            {{ invoiceItem?.projectName ?? '' }}
                        </p>
                        }

                        @if(invoiceItem?.item?.itemName)
                        {
                        <p class="text-text-primary text-text-md-semibold">
                            {{ invoiceItem?.item?.itemName?? '' }}
                        </p>
                        }
                        <p class="text-text-primary text-text-md-regular">
                            @if(invoiceItem?.service
                            ?.serviceName)
                            {
                            {{ invoiceItem?.service
                            ?.serviceName ?? '' }} -
                            }
                            {{getFullName(invoiceItem?.user)}} -
                            {{invoiceItem.dateSelectItem | date:'MMM, d y'}}
                        </p>
                        <p class="text-text-primary text-text-md-regular">
                            {{ invoiceItem?.description ?? '' }}
                        </p>
                    </div>
                    <p class="text-text-primary text-text-md-regular">
                        ${{ invoiceItem?.rate ?? 0 | formatNumber }}
                    </p>
                    <p class="text-text-primary text-text-md-regular">
                        {{ invoiceItem?.qty ?? 0 | decimal:2 | formatNumber }}
                    </p>
                    @if(invoiceItem?.taxes.length>0&&getNameSelectedTaxes(invoiceItem?.taxes)!='')
                    {

                    <p (click)="handleModifyTaxes(invoiceItem?.taxes,i)"
                        class="text-text-primary text-text-md-regular cursor-pointer">
                        {{ getNameSelectedTaxes(invoiceItem?.taxes) }}
                    </p>
                    }@else{
                    <p (click)="handleModifyTaxes(invoiceItem?.taxes,i)"
                        class="text-blue-500 text-sm cursor-pointer">
                        + Add Taxes
                    </p>
                    }

                    <p class="text-text-primary text-text-md-bold">
                        ${{
                        calculateTotalInvoiceItem(invoiceItem?.rate,
                        invoiceItem?.qty) | decimal:2 | formatNumber
                        }}

                    </p>
                    <app-inno-table-action
                        (onEdit)="handleModifyInvoiceItem(i, invoiceItem)"
                        (onDelete)="handleDeleteInvoiceItem(i)" />
                </div>
                }
            </div>
            <button (click)="handleModifyInvoiceItem()"
                class="mt-[8px] button-size-md button-outline-primary w-full border-dashed justify-center">
                <img src="../../../../../assets/img/icon/ic_add_green.svg"
                    alt="Icon">
                Add new line
            </button>
        </div>

        <div class="w-full flex flex-col items-end mt-[16px]">
            <div class="flex justify-end items-start gap-[8px]">
                <p class="text-right text-text-primary text-text-md-regular">
                    Subtotal
                </p>
                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    ${{ subtotal | decimal:2 | formatNumber }}
                </p>
            </div>
            @for(tax of taxArray; track tax ;let i=$index)
            {
            <div class="flex justify-end items-start gap-[8px] mb-2">
                <div class=" flex  flex-col pl-2">
                    <p
                        class="text-right text-text-primary text-text-sm-regular">
                        {{tax.name}} ({{tax.amount}}%)
                    </p>
                    <p
                        class="text-right text-text-primary text-text-sm-regular">
                        #{{tax.numberTax}}
                    </p>
                </div>

                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    ${{ tax.total | decimal:2 | formatNumber }}
                </p>
            </div>
            }
            <div class="flex justify-end items-start gap-[8px]">
                <p class="text-right text-text-primary text-text-md-regular">
                    Tax
                </p>
                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    ${{ CheckIsNaN(sumtax) | decimal:2 | formatNumber}}
                </p>
            </div>
            <div class="flex justify-end items-start gap-[8px]">
                <div class="block">
                    <p
                        class="text-right text-text-primary text-text-md-regular">
                        Discount
                    </p>
                    <button class="button-link-primary">
                        Add discount
                    </button>
                </div>
                <p
                    class="text-text-primary text-text-md-bold text-right w-[160px] shrink-0">
                    $0
                </p>
            </div>
            <div class="flex justify-end items-start gap-[8px]">
                <p class="text-right text-text-primary text-text-md-regular">
                    Amount Due ({{_storeService.curencyCompany | async}})
                </p>
                <p
                    class="text-text-primary text-headline-md-bold text-right w-[160px] shrink-0">
                    ${{ totalAmount | decimal:2 | formatNumber }}
                </p>
            </div>
        </div>
    </div>
    <div footer>
        <app-inno-modal-footer
            (onCancel)="handleCancel()"
            [customSubmitButton]="customSubmitNewInvoice" />
        <ng-template #customSubmitNewInvoice>
            <div class="flex items-center gap-[12px]">
                <button (click)="handleSave()"
                    class="button-outline-primary button-size-md">
                    Save
                </button>
                <!-- <button class="button-primary button-size-md"
                (click)="handleSendInvoice()">
                Send Invoice
            </button> -->
            </div>
        </ng-template>
    </div>
</app-inno-modal-wrapper>
