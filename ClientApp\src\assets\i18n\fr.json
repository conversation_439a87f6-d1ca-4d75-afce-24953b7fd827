{"TOAST": {"Sent": "<PERSON><PERSON><PERSON>", "SentCode": "Votre code a été envoyé", "DeleteImg": "Voulez-vous supprimer l'image ?", "MissingRequiredFields": "Champs obligatoires manquants", "Tax": "Il existe deux ou plusieurs taxes portant le même nom.", "TaxName": "Veuillez vous assurer que tous les noms de taxe sont uniques.", "DuplicateTax": "Des noms de taxe en double ont été trouvés :", "Success": "Su<PERSON>ès", "Save": "Enregistrer", "Create": "<PERSON><PERSON><PERSON>", "MarkAsPaid": "Marquer comme payé", "MarkAsSent": "Marquer comme envoyé", "SizeFile": "<PERSON><PERSON>", "Warning": "Avertissement", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "NoSelected": "Aucun client sélectionné", "Instruction": "Veuillez sélectionner un client pour continuer la création de la facture.", "Fail": "<PERSON><PERSON><PERSON>", "Paid": "<PERSON><PERSON>", "Active": "Active", "Update": "Mettre à jour", "UpdateBusiness": "Vos informations ont été mises à jour avec succès !", "UpdateService": "Service mis à jour avec succès", "UpdateItem": "Mise à jour de l'élément réussie", "CreateService": "Nouveau service créé avec succès"}, "ITEM_INVOICE": {"NewInvoiceItem": "Nouvel élément de facture", "EditInvoiceItem": "Modifier un élément de facture", "NewEstimateItem": "Nouvel élément de devis", "EditEstimateItem": "Modifier un élément de devis", "Description": "Description", "EnterInvoiceDescription": "Saisissez la description de la facture", "Rate": "<PERSON><PERSON>", "RateRequired": "Le taux est requis", "Qty": "Quantité", "EnterQty": "Saisissez la quantité", "QuantityRequired": "La quantité est requise", "Total": "Total", "AddUpdateTaxes": "Ajouter/mettre à jour les taxes"}, "DASHBOARD": {"Title": "Tableau de bord", "TitleChartRevenue": "Revenus et dépenses", "TitleChartGraphics": "Graphique", "TotalAmount": "Montant total", "TotalAmountPaid": "Montant total payé", "Explore": "Explorez <PERSON>", "Value": "<PERSON><PERSON>", "Users": "Utilisateurs", "Orders": "Commandes", "Tickets": "Tickets"}, "EMPTY": {"EmptyProject": "Projet vide", "NoResult": "Aucun résultat"}, "COMMON": {"NoResult": "Aucun résultat", "DifferentKeywords": "Veuillez essayer différents mots-clés", "EmptyData": "<PERSON><PERSON><PERSON> vides", "CreateNew": "Créer un nouveau", "NewEntry": "Nouvelle entrée", "MarkBill": "Marquer comme facture", "SearchProjectsClients": "Rechercher des projets ou des clients", "ChooseBusiness": "Choisissez une entreprise", "Search": "<PERSON><PERSON><PERSON>", "DatePlaceholder": "Sélectionnez la date", "Owner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "UserExist": "L'utilisateur existe déjà", "Loading": " Chargement...", "ConfirmDelete": "Voulez-vous supprimer ?", "Action": "Action", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Archive": "Archiver", "Activate": "Activer", "UploadPicture": "Télécharger une photo", "DescriptionEmptyProject": "Veuillez sélectionner un projet pour obtenir la liste des projets"}, "CLIENT": {"ADD_CLIENT_FORM": {"Title": "Ajouter un nouveau client", "TitleEdit": "Modifier un client", "FirstName": "Prénom", "FirstNamePlaceholder": "Entrez le prénom", "LastName": "Nom de famille", "LastNamePlaceholder": "Entrez le nom de famille", "ClientName": "Nom du client", "ClientNamePlaceholder": "Entrez le nom du client", "ClientNameRequired": "Le nom du client est requis", "Email": "Email", "EmailPlaceholder": "Entrez l'email", "EmailRequired": "L'email est requis", "EmailInvalid": "<PERSON><PERSON> invalide", "PhoneNumber": "Numéro de téléphone", "PhoneNumberPlaceholder": "Entrez le numéro de téléphone", "PhoneLength": "Le numéro de téléphone doit comporter 10 chiffres", "PhonePattern": "Chiffres uniquement", "Post": "Poste", "PostPlaceholder": "Entrez le poste", "BusinessPhone": "Téléphone professionnel", "BusinessPhonePlaceholder": "Entrez le numéro de téléphone professionnel", "MobilePhone": "Numéro de téléphone mobile", "MobilePhonePlaceholder": "Entrez le numéro de téléphone mobile", "Country": "Pays", "CountryPlaceholder": "Sélectionnez un pays", "CountryRequired": "Le pays est requis", "Address1": "Adresse ligne 1", "Address1Placeholder": "Entrez l'adresse", "Address2": "Adresse ligne 2", "Address2Placeholder": "Entrez l'adresse", "TownCity": "Ville", "TownCityPlaceholder": "Entrez la ville", "StateProvince": "État/Province", "StateProvincePlaceholder": "Entrez l'état/province", "PostalCode": "Code postal", "PostalCodePlaceholder": "Entrez le code postal"}, "GIRD": {"Company": "Entreprise", "Credit": "Crédit"}, "NewClient": "Nouveau client", "DeleteClient": "Supprimer le client !", "ActiveClient": "Clients actifs", "TotalOverdue": "Total en retard", "TotalDraft": "Total en brouillon", "TotalOutstanding": "Total impayé", "AllClient": "Tous les clients", "MailsHistory": "Historique des courriels"}, "PROJECT": {"ADD_PROJECT_FORM": {"Title": "Ajouter un nouveau projet", "TitleEdit": "Modifier un projet", "ProjectType": "Type de projet", "ProjectTypePlaceholder": "Sélectionnez le type de projet", "ProjectNameRequired": "Le nom du projet est requis", "ClientRequired": "Le client est requis", "HourlyProject": "Projet horaire", "ProjectName": "Nom du projet", "ProjectNamePlaceholder": "Entrez le nom du projet", "Description": "Description", "DescriptionPlaceholder": "Ajoutez une description", "AssignClient": "Assigner un client", "AssignClientPlaceholder": "Sélectionnez un projet/client", "EndDate": "Date de fin", "EndDatePlaceholder": "Sélectionnez la date de fin", "HourlyRate": "<PERSON><PERSON> horaire", "HourlyRatePlaceholder": "Entrez le taux horaire", "FlatRate": "<PERSON><PERSON><PERSON> for<PERSON>ita<PERSON>", "FlatRatePlaceholder": "Entrez le tarif forfaitaire", "TotalHours": "Total d'heures", "TotalHoursPlaceholder": "Entrez le total d'heures", "Member": "Membre", "Billable": "Facturable", "AddService": "Ajouter un service"}, "GIRD": {"ProjectName": "Nom du projet", "Client": "Clients", "EndDate": "Date de fin", "Members": "Me<PERSON><PERSON>", "Logged": "Enregistré", "Unbilled": "Non facturé", "Amount": "<PERSON><PERSON>"}, "NewAProject": "Créer un nouveau projet", "NewProject": "Créer un nouveau projet", "DeleteProject": "Supprimer le projet !", "ActiveProject": "Projet actif", "DescriptionActive": "Voulez-vous être actif ?", "UnActiveProject": "Projet inactif", "ArchiveProjects": "<PERSON>r le projet", "TotalLogged": "Total enregistré", "TotalUnbilled": "Total non facturé", "TotalAmount": "Montant total"}, "BUTTON": {"Cancel": "Annuler", "Agree": "Accepter", "Delete": "<PERSON><PERSON><PERSON><PERSON>", "Update": "Mettre à jour", "Save": "Enregistrer", "Apply": "Appliquer", "SendInvoice": "Envoyer la facture"}, "LOGIN": {"TitleSign": "Se connecter à InnoBooks", "Or": "Ou", "TitleNewAccount": "créer un nouveau compte", "EmailAddress": "Adresse e-mail", "EmailRequired": "L'e-mail est requis", "InvalidRequired": "E-mail invalide", "Password": "Mot de passe", "PasswordRequired": "Mot de passe requis", "PlaceholderPassword": "Entrez votre mot de passe", "PlaceholderEmail": "Entrez votre e-mail", "ForgotPassword": "Mot de passe oublié ?", "OrContinue": "Ou continuer avec", "Signin": "Se connecter", "TimeTrackingMade": "Suivi du temps simplifié", "Describe": "<PERSON><PERSON><PERSON><PERSON>, g<PERSON>rez et optimisez votre flux de travail avec notre puissante plateforme de suivi du temps."}, "REGISTER": {"TitleCreateAccount": "<PERSON><PERSON><PERSON> votre compte", "Describe": "Rejoignez notre plateforme et commencez à gérer votre temps efficacement", "REGISTER_FORM": {"FirstNameRequired": "Le prénom est obligatoire", "LastNameRequired": "Le nom de famille est obligatoire", "FirstName": "Prénom", "LastName": "Nom de famille", "EnterFirstName": "Entrez votre prénom", "EnterLastName": "Entrez votre nom de famille", "Email": "Adresse e-mail", "EnterEmail": "Entrez votre e-mail", "EmailRequired": "L'e-mail est requis", "Password": "Mot de passe", "InvalidEmail": "E-mail invalide", "PasswordRequired": "Mot de passe requis", "EnterPassword": "Entrez votre mot de passe", "PasswordMinLength": "Le mot de passe doit contenir au moins 6 caractères", "ConfirmPassword": "Confirmez le mot de passe", "ConfirmPasswordRequired": "La confirmation du mot de passe est requise", "ConfirmPasswordValidator": "Le mot de passe et sa confirmation ne correspondent pas.", "EnterPasswordAgain": "Entrez à nouveau votre mot de passe", "AgreeTo": "Je confirme avoir lu et accepté les Conditions d'utilisation et la Politique de confidentialité d'InnoBooks.", "CreateButton": "<PERSON><PERSON><PERSON> un compte", "AlreadyAccount": "Vous avez déjà un compte ?", "SignIn": " Se connecter", "Company": "Entreprise", "OrContinue": "Ou continuer avec"}}, "TIMETRACKING": {"Timerdiscarded": "Minuterie abandonnée !", "Title": "<PERSON><PERSON><PERSON> du temps", "Write": "Écrivez vos pensées ici...", "TimeRequired": "Il faut du temps", "EditEntry": "Modifier l'entrée de temps", "ProjectRequired": "Le projet est requis", "TotalTime": "Durée totale", "DeleteTimeTracking": "Supprimer le suivi du temps !", "TitleGenerate": "Générer une facture", "Day": "Jour", "Week": "<PERSON><PERSON><PERSON>", "Month": "<PERSON><PERSON>", "All": "<PERSON>ut", "Search": "<PERSON><PERSON><PERSON>", "GenerateInvoice": "Générer une facture", "DatePickerPlaceholder": "Sélectionner une date", "EntryPlaceholder": "Sur quoi travaillez-vous ?", "StartTimer": "<PERSON><PERSON><PERSON><PERSON> le minuteur", "Billable": "Facturable", "Entry": "Entrée", "LogTime": "Enregistrer le temps", "Discard": "Annuler", "NoClient": "Pas de <PERSON>", "AddEntry": "Ajouter une entrée", "Timer": "<PERSON><PERSON><PERSON>", "NoTime": "Aucune saisie de temps pour le moment", "DescribeNoTime": "Commencez à ajouter des projets/clients et à enregistrer votre temps dès maintenant !", "ChooseClient": "<PERSON><PERSON> un client", "ChooseService": "Choisir un service", "NoEntries": "Aucune saisie de temps pour le moment", "NoEntriesSubtext": "Commencez à ajouter des projets/clients et à enregistrer votre temps dès maintenant !", "DIALOG": {"TitleConfirm": "Confirmer", "Discard": "Êtes-vous sûr de vouloir annuler ce minuteur ?"}}, "SETTINGS": {"Title": "Paramètres", "BasicInformation": {"Title": "Informations de base", "Description": "Ajouter ou modifier les informations de base sur votre entreprise", "BasicInformationForm": {"BusinessName": "Nom de l'entreprise", "BusinessNamePlaceholder": "Nom de l'entreprise", "BusinessNameRequired": "Le nom de l'entreprise est requis", "EmailBusiness": "Email professionnel", "EmailPlaceholder": "Email", "EmailRequired": "L'email est requis", "PhoneNumber": "Numéro de téléphone", "PhonePlaceholder": "Téléphone", "PhoneRequired": "Le téléphone est requis", "Country": "Pays", "AddressLine1": "Adresse ligne 1", "AddressLine1Placeholder": "Adresse ligne 1", "AddressLine2": "Adresse ligne 2", "AddressLine2Placeholder": "Adresse ligne 2", "TownCity": "Ville", "TownCityPlaceholder": "Ville", "StateProvince": "État/Province", "StateProvincePlaceholder": "État/Province", "PostalCode": "Code postal", "PostalCodePlaceholder": "Code postal", "StartWeekOn": "Commencer la semaine le", "StartWeekOnPlaceholder": "Sélectionner un jour", "Timezone": "<PERSON><PERSON> ho<PERSON>", "PlaceholderTimezone": "Sélectionner un fuseau horaire", "DateFormat": "Format de date", "PlaceholderDateFormat": "Sélectionner un format", "Note": "Note", "NotePlaceholder": "Note ici...", "PhoneLength": "Le numéro de téléphone doit contenir 10 chiffres", "PhonePattern": "Chiffres uniquement"}}, "PermissionInformation": {"Title": "Informations sur les autorisations", "Description": "Ajouter ou modifier les rôles et autorisations de votre projet"}, "AccountInformation": {"Title": "Informations du compte", "Description": "Gestion du compte innoBook"}, "CategoryInformation": {"Title": "Informations sur les catégories", "Description": "Modifier ou ajouter des types pour la gestion innoBook"}, "TaxAndFinancialInformation": {"Title": "Informations fiscales et financières", "Description": "Ajouter ou modifier les préférences financières concernant votre entreprise", "TaxAndFinancialInformationForm": {"BaseCurrency": "Devise de <PERSON>", "BaseCurrencyPlaceholder": "Sélectionner une devise", "FiscalYearStartMonth": "Mois de début de l'exercice fiscal", "FiscalYearStartMonthPlaceholder": "Sélectionner...", "FiscalYearEndMonth": "Mois de fin de l'exercice fiscal", "FiscalYearEndMonthPlaceholder": "Sélectionner...", "StandardRate": "Taux standard", "StandardRatePlaceholder": "0,00 $", "StandardRateUnit": "$ / Heure", "SaveChanges": "Enregistrer les modifications"}}, "Billing": {"Title": "Facturation & Plans", "Description": "Gérer vos informations de facturation", "BillingForm": {"CardNumber": "Numéro de carte", "CardNumberPlaceholder": "Entrez votre numéro de carte", "CardNumberRequired": "Le numéro de carte est requis", "ExpirationDate": "Date d'expiration", "ExpirationDatePlaceholder": "Sélectionnez une date", "ExpirationDateRequired": "La date d'expiration est requise", "CVC": "CVC", "CVCPlaceholder": "Entrez votre CVC", "CVCRequired": "Le CVC est requis", "SaveChanges": "Enregistrer les modifications"}}}, "CATEGORY": {"Title": "<PERSON><PERSON><PERSON><PERSON>", "TitleAdd": "Ajouter une catégorie", "ListCategory": "Lister les éléments de catégorie", "CategoryItem": "Élément de catégorie", "AddCateGory": "Ajouter une nouvelle catégorie", "CreateCategoryItem": "Élément de catégorie c<PERSON>é", "CreateCategory": "<PERSON><PERSON><PERSON><PERSON>", "AddNew": "Ajouter une nouvelle catégorie d'élément", "RemoveAll": "<PERSON>ut supprimer", "DeleteCategory": "Supprimer la catégorie", "GIRD": {"CategoryName": "Nom de la catégorie", "CreateDate": "Date de création"}}, "TEAMMEMBERS": {"Title": "Membres de l'équipe", "AddButton": "Ajouter des membres", "DeleteMember": "Supprimer les membres !", "GIRD": {"Name": "Nom", "Role": "R<PERSON><PERSON>"}, "AddMemberForm": {"Title": "Ajouter des membres", "ProfileMember": "<PERSON><PERSON>", "FirstName": "Prénom", "LastName": "Nom de famille", "Email": "Email", "FirstNameRequired": "Le prénom est requis", "LastNameRequired": "Le nom de famille est requis", "EmailRequired": "L'email est requis", "InvalidEmail": "<PERSON><PERSON> invalide"}, "Detail": {"BasicInfo": "Informations de base", "RolePermissions": "Rôle et autorisations", "EmailLabel": "E-mail", "RoleLabel": "R<PERSON><PERSON>", "SettingsTitle": "Paramètres du membre de l'équipe", "SetBillableRate": "Définir le taux facturable", "BillableRateDescription": "Définir les taux horaires pour le temps suivi", "SetCostRate": "Définir le taux de coût", "CostRateDescription": "Combien vous payez vos membres d'équipe pour le travail qu'ils effectuent", "SetCapacity": "Définir la capacité", "CapacityUnit": "heures/semaine"}}, "EXPENSES": {"Title": "<PERSON>é<PERSON>ses", "UpdateExpense": "Mettre à jour les dépenses", "DeleteExpeneses": "Supprimer les dépenses !", "DatePlaceholder": "Sélectionner une date", "DescriptionEditExpensesItem": "Modifier un poste de dépenses", "NewExpensesItem": "Nouveau poste de dépenses", "ExportButton": "Exporter", "NewExpenseButton": "Nouvelle dépense", "GIRD": {"ExpenseName": "Nom de la dépense", "ProjectClient": "Projet/Client", "Date": "Date", "User": "Utilisa<PERSON>ur", "Amount": "<PERSON><PERSON>"}, "NEW_ADD_FORM": {"MerchantName": "Nom du commerçant", "MerchantPlaceholder": "<PERSON>cher<PERSON> ou créer une nouvelle dépense", "ExpenseName": "Nom de la dépense", "ExpensePlaceholder": "Entrer le nom de la dépense", "Description": "Description", "DescriptionPlaceholder": "Une brève description du projet.", "ClientProject": "Client/Projet", "ClientProjectPlaceholder": "Sélectionner un client/projet", "Category": "<PERSON><PERSON><PERSON><PERSON>", "CategoryPlaceholder": "Sélectionner une catégorie", "Date": "Date", "DatePlaceholder": "Sélectionner une date", "ValidationMerchantRequired": "Le nom du commerçant est requis", "ValidationExpenseRequired": "Le nom de la dépense est requis", "ValidationCategoryRequired": "La catégorie est requise", "ValidationDateRequired": "La date est requise", "ValidationRateRequired": "Le tarif est requis", "ValidationQuantityRequired": "La quantité est requise", "AddTaxes": "+ Ajouter des taxes", "AddNewLine": "Ajouter une nouvelle ligne", "ExpensesItem": "Élément de dépense", "Rate": "<PERSON><PERSON>", "Quantity": "Quantité", "Tax": "Taxe", "AddUpdateTaxes": "Ajouter/mettre à jour les taxes", "LineTotal": "Total de la ligne", "Subtotal": "Sous-total", "AmountDue": "<PERSON><PERSON> dû", "Discount": "Remise", "AddDiscount": "Ajouter une remise", "Attachment": "Ajouter une pièce jointe"}}, "INVOICES": {"Warning": "<PERSON><PERSON><PERSON><PERSON> remplir complètement toutes les informations de la facture.", "Title": "Factures", "DeleteInvoice": "Supprimer la facture !", "TitleEdit": "Mettre à jour la facture", "Tabs": {"Created": "Créées", "SentToMe": "Envoyées à moi"}, "Summary": {"TotalOverdue": "Total en retard", "TotalInDraft": "Total en brouillon", "TotalOutstanding": "Total impayé", "DatefIssue": "Date d’émission", "DueDate": "Date d’échéance", "More": "Plus", "BilledTo": "Fact<PERSON><PERSON> à", "Description": "Description", "Edit": "Modifier", "ByLink": "Partager par lien", "SendMail": "Envoyer un e-mail"}, "GIRD": {"InvoiceNumber": "Numéro de facture", "Clients": "Clients", "IssuedDate": "Date d’émission", "DueDate": "Date d’échéance", "Status": "Statut", "Amount": "<PERSON><PERSON>"}, "Buttons": {"NewInvoice": "Nouvelle facture", "EditInvoice": "Modifier la facture"}, "INVOICE_FORM": {"EditBusinessInfo": "Modifier les informations de l'entreprise", "ClientProject": "Envoyée à Client/Projet", "ClientProjectPlaceholder": "Sélectionner client/projet", "InvoiceNumber": "Numéro de facture", "IssueDate": "Date d’émission", "IssueDatePlaceholder": "Sélectionner la date d’émission", "DueDate": "Date d’échéance", "DueDatePlaceholder": "Sélectionner la date d’échéance", "Description": "Description", "DescriptionPlaceholder": "Une brève description des détails de la facture.", "Subtotal": "Sous-total", "Tax": "Taxe", "AmountDue": "<PERSON><PERSON> dû", "Discount": "Remise", "AddDiscount": "Ajouter une remise", "Buttons": {"AddUnbillTime": "Ajouter du temps non facturé", "AddNewItem": "+ Ajouter un nouvel article", "AddTaxes": "+ Ajouter des taxes", "AddUnbillExpense": "Ajouter des dépenses non facturées", "AddNewLine": "Ajouter une nouvelle ligne"}, "TableHeaders": {"InvoiceItem": "Article de facture", "Rate": "<PERSON><PERSON><PERSON>", "Quantity": "Quantité", "Tax": "Taxe", "LineTotal": "Total de la ligne"}, "Validation": {"ClientRequired": "Le client est requis", "IssueDateRequired": "La date d’émission est requise", "DueDateRequired": "La date d’échéance est requise"}}}, "ESTIMATE": {"Title": "<PERSON><PERSON>", "TitleEdit": "Mettre à jour le devis", "DeleteEstimate": "Supprimer l'estimation !", "Tabs": {"Created": "<PERSON><PERSON><PERSON>", "SentToMe": "<PERSON><PERSON><PERSON> à moi"}, "Summary": {"TotalOverdue": "Total des devis en retard", "TotalInDraft": "Total des devis en brouillon", "TotalOutstanding": "Total des devis en attente", "DatefIssue": "Date d'émission", "DueDate": "Date d'échéance", "More": "Plus", "BilledTo": "Fact<PERSON><PERSON> à", "Description": "Description", "Edit": "Modifier", "ByLink": "Partager par lien", "SendMail": "Envoyer par e-mail"}, "GIRD": {"EstimateNumber": "Numéro de devis", "Clients": "Clients", "IssuedDate": "Date d'émission du devis", "DueDate": "Date d'échéance", "Status": "Statut", "Amount": "<PERSON><PERSON>"}, "Buttons": {"NewEstimate": "Nouveau devis", "EditEstimate": "Modifier le devis", "SendEstimate": "Envoyer le devis"}, "ESTIMATE_FORM": {"EditBusinessInfo": "Modifier les informations de l'entreprise", "ClientProject": "Envoyé au client/projet", "ClientProjectPlaceholder": "Sélectionner un client/projet", "EstimateNumber": "Numéro de devis", "IssueDate": "Date d'émission du devis", "IssueDatePlaceholder": "Sélectionner la date d'émission", "DueDate": "Date d'échéance", "DueDatePlaceholder": "Sélectionner la date d'échéance", "Description": "Description", "DescriptionPlaceholder": "Une brève description des détails de l'estimation.", "Subtotal": "Sous-total", "Tax": "Taxe", "EstimateTotal": "Estimation totale", "AmountDue": "<PERSON><PERSON> dû", "Discount": "Remise", "AddDiscount": "Ajouter une remise", "Buttons": {"AddUnbillTime": "Ajouter un temps non facturé", "AddNewItem": "+ Ajouter un nouvel article", "AddTaxes": "+ Ajouter des taxes", "AddUnbillExpense": "Ajouter une dépense non facturée", "AddNewLine": "Ajouter une nouvelle ligne"}, "TableHeaders": {"EstimateItem": "Article du devis", "Rate": "<PERSON><PERSON>", "Quantity": "Quantité", "Tax": "Taxe", "LineTotal": "Total de la ligne"}, "Validation": {"ClientRequired": "Le client est requis", "IssueDateRequired": "La date d'émission est requise", "DueDateRequired": "La date d'échéance est requise"}}}, "ITEMS_SERVICES": {"Title": "Articles et Services", "EditItem": "Modifier l'élément", "EditService": "Service d'édition", "DeleteItem": "Supprimer l'article !", "DeleteService": "Supprimer le service !", "CreateNewService": "Créer un nouveau service", "Tabs": {"Items": "Articles", "DescribeItem": "Les articles peuvent être inclus dans les factures pour facturer vos clients.", "DescribeServices": "Les services vous permettent de suivre le temps passé sur les tâches et de facturer vos clients en conséquence.", "Services": "Services"}, "SearchPlaceholder": "<PERSON><PERSON><PERSON>", "Buttons": {"CreateNew": "Créer Nouveau"}, "GIRD": {"ServiceName": "Nom du service", "ItemName": "Nom de l'article", "Description": "Description", "Taxes": "Taxes", "Rate": "<PERSON><PERSON><PERSON>", "User": "Utilisa<PERSON>ur", "Date": "Date"}, "NEW_ITEM_FORM": {"Title": "Créer un nouvel article", "Name": "Nom", "NameRequired": "Le nom est requis", "NamePlaceholder": "Entrer un nom", "Description": "Description", "DescriptionPlaceholder": "Ajouter une description", "Rate": "Taux (€)", "RatePlaceholder": "0.00", "Taxes": "Taxes", "TaxName": "Nom de la taxe", "TaxRate": "<PERSON><PERSON> (%)", "TaxNumber": "Numéro de taxe", "AddTax": "Ajouter une autre taxe"}}, "TAX": {"AddTax": "Ajouter des taxes", "TaxName": "Nom de la taxe", "ApplyTax": "Appliquer les taxes à tous les postes", "AddAnother": "Ajouter une autre taxe", "PlaceholderTaxName": "Entrez le nom de la taxe", "PlaceholderTaxNumber": "Entrez le numéro de taxe", "Rate": "<PERSON><PERSON> (%)", "TaxNumber": "Numéro de taxe"}, "MENUACTION": {"MarkAsPaid": "Marquer comme payé", "MarkAsSent": "Marquer comme envoyé", "Duplicate": "<PERSON><PERSON><PERSON><PERSON>", "DownloadPDF": "Télécharger le PDF", "Print": "<PERSON><PERSON><PERSON><PERSON>", "Archive": "Archiver", "Delete": "<PERSON><PERSON><PERSON><PERSON>"}, "GENERATEINVOICE": {"GenerateNewInvoice": "Générer une nouvelle facture", "Client": "Client", "SelectClient": "Sélectionner un client", "ClientRequired": "Le client est requis", "DateRange": "Plage de dates", "AllTime": "Tout le temps", "ChooseExpenses": "Choisir les dépenses", "AllUnbilledExpensesClient": "Toutes les dépenses non facturées pour ce client", "OnlyUnbilledExpensesProjects": "Seulement les dépenses non facturées affectées aux projets sélectionnés", "NoExpenses": "<PERSON><PERSON><PERSON>", "ChooseProject": "Choisir un projet", "AmountDue": "<PERSON><PERSON> dû", "RangDateOptions": {"AllTime": "Tout le temps", "ThisMonth": "Ce mois-ci", "LastMonth": "Le mois dernier", "Custom": "<PERSON><PERSON><PERSON><PERSON>"}, "EXPENSES": {"ExpensesName": "Nom de la dépense", "CategoryName": "Nom de la catégorie", "ItemName": "Nom de l'article", "PaidAmount": "<PERSON><PERSON> payé"}, "TableHeaders": {"Description": "Description", "User": "Utilisa<PERSON>ur", "Project": "Projet", "Hours": "<PERSON><PERSON>", "LineTotal": "Total par ligne"}}, "SERVICEFORPROJECT": {"ServiceName": "Nom du service", "Rate": "<PERSON><PERSON>", "Description": "Description", "Taxes": "Taxes", "Date": "Date", "Buttons": {"CreateNewService": "Créer un nouveau service"}, "Title": "Ajouter un service"}, "SELECTTIMETRACKING": {"Title": "Sélectionner des articles et services", "Tabs": {"Items": "Articles", "Services": "Services"}, "Table": {"ItemName": "Nom de l'article", "ServiceName": "Nom du service", "Rate": "<PERSON><PERSON>", "Qty": "Quantité", "Description": "Description", "Taxes": "Taxes", "Date": "Date"}}, "FILEUPLOAD": {"Title": "<PERSON><PERSON>léverser un fichier", "FileName": "Nom du fichier", "Size": "<PERSON><PERSON>", "Type": "Type", "Action": "Action"}, "REPORT": {"Title": "Rapport"}, "FORGOTPASSWORD": {"Title": "Mot de passe oublié ?", "Instruction": "Entrez votre email et nous vous enverrons les instructions pour réinitialiser votre mot de passe", "EmailLabel": "Adresse e-mail", "EmailInvalid": "E-mail invalide", "EmailPlaceholder": "Entrez votre e-mail", "EmailRequired": "L'e-mail est requis", "ResetButton": "Réinitialiser le mot de passe", "BackToSignIn": "Retour à la connexion"}, "PROFILE": {"Title": "Informations du profil", "FirstName": "Prénom", "LastName": "Nom de famille", "Email": "E-mail", "UserName": "Nom d'utilisateur", "FirstNameRequired": "Le prénom est requis", "LastNameRequired": "Le nom de famille est requis", "TimeZone": "<PERSON><PERSON> ho<PERSON>"}, "ACCOUNT": {"Title": "Gestion du compte", "DeleteUser": "Supprimer l'utilisateur !", "GIRD": {"Name": "Nom", "Role": "R<PERSON><PERSON>", "Action": "Action"}}, "MENU": {"Pos": "Pos", "Tracking": "<PERSON><PERSON><PERSON>", "Estimates": "<PERSON><PERSON>", "TimeTracking": "<PERSON><PERSON><PERSON> du temps", "Analyze": "Analyser", "Dashboard": "Tableau de bord", "Reports": "Rapports", "Manager": "Gestion", "Clients": "Clients", "Projects": "Projets", "Invoices": "Factures", "Expenses": "<PERSON>é<PERSON>ses", "Upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ItemServices": "Articles et services", "TeamMembers": "Membres de l'équipe", "Settings": "Paramètres"}, "ROLE": {"RolePermission": "Autorisation de rôle", "UserGroup": "Groupe d'utilisateurs", "User": "Utilisa<PERSON>ur", "Role": "R<PERSON><PERSON>", "UserGroupAuthorization": "Autorisation du groupe d'utilisateurs", "New": "Nouveau", "GIRD": {"RoleName": "Nom du rôle", "Description": "Description", "Code": "Code"}}, "FILTER": {"ClientProjects": "Client/Projets", "SelectClientProjects": "Sélectionner Client/Projets", "LoggedBy": "Enregistré par", "SelectUser": "Sélectionner un utilisateur", "StartDate": "Date de début", "SelectStartDate": "Sélectionner la date de début", "EndDate": "Date de fin", "SelectEndDate": "Sélectionner la date de fin", "ResetDefault": "Réinitialiser par défaut"}, "POS": {"Title": "POS", "Tax": "Tax", "ChooseClient": "<PERSON><PERSON> un client", "SearchClients": "Rechercher des clients", "CreateNewClient": "+ Créer un nouveau client", "CreateInvoice": "<PERSON><PERSON>er une facture", "Print": "<PERSON><PERSON><PERSON><PERSON>", "Items": "Articles", "Rate": "<PERSON><PERSON>", "ItemName": "Nom de l'article", "Quantity": "Quantité", "LineTotal": "Total de la ligne", "EnterItemName": "Saisir le nom de l'article", "AddNewItem": "Ajouter un nouvel article"}, "TAG": {"CreateTag": "C<PERSON>er un nouveau tag « Web »", "Search": "<PERSON><PERSON><PERSON> ou créer un nouveau tag"}, "STATUS": {"Draft": "Brouillon", "Paid": "<PERSON><PERSON>", "NonBillable": "Non facturable", "Unbilled": "Non facturé", "Billed": "<PERSON><PERSON><PERSON><PERSON>"}, "VERIFICATION": {"Title": "Entrez le code de vérification", "Message": "Nous avons envoyé un code à", "Instruction": "Entrez le code pour vous connecter à votre compte", "LoginButton": "Connexion", "NotReceived": "Vous n'avez pas reçu de code ?", "Resend": "Renvoyer le code"}}