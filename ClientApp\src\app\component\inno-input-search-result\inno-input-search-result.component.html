<div
  class="min-w-[300px] w-full shadow-md rounded-md border border-border-primary-slight bg-bg-primary">
  @if(!isDisableSearch) {
  <div class="w-full p-[16px] border-b border-border-primary-slight">
    <div
      class="w-full h-[40px] flex items-center rounded-[8px] border-[2px] px-[12px]">
      <img src="../../../assets/img/icon/ic_search_gray.svg"
        class="w-[16px] shrink-0" alt="Icon search">
      <input
        appAutofocus
        type="text"
        (keyup)="handleOnChange($event)"
        placeholder="{{ placeholder }}"
        class="h-full w-full pl-[8px] text-text-md-regular"
        [value]="inputSearchValue">
    </div>
  </div>
  }

  @if(isLoading) {
  <div class="flex justify-center py-3">
    <app-inno-spin></app-inno-spin>
  </div>
  } @else {
  <div class="w-full p-[8px] max-h-[300px] max-w-[500px] overflow-auto">
    @if(isNotFound) {
    @if(isEmptyData) {
    <app-inno-empty-data
      [title]="'COMMON.EmptyData'" />
    } @else {
    <app-inno-empty-data
      [title]="'COMMON.NoResult'"
      [description]="'COMMON.DifferentKeywords'" />
    }
    } @else {
    @for(option of data; track option) {
    <ng-container
      *ngTemplateOutlet="optionTemplate; context: { $implicit: option }"></ng-container>
    }
    }
  </div>
  @if(footerTemplate) {
  <div class="border-t border-border-primary p-[8px] w-full">
    <ng-container *ngTemplateOutlet="footerTemplate;"></ng-container>
  </div>
  }
  }
</div>
