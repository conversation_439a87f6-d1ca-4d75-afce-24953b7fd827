import { InnoTabsComponent } from 'app/component/inno-tabs/inno-tabs.component';
import { InvoiceItem } from './../../../dto/interface/invoiceItem.interface';
import { Component, inject, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { InnoEmptyDataComponent } from 'app/component/inno-empty-data/inno-empty-data.component';
import { InnoFormCheckboxComponent } from 'app/component/inno-form-checkbox/inno-form-checkbox.component';
import { InnoModalFooterComponent } from 'app/component/inno-modal-footer/inno-modal-footer.component';
import { InnoModalWrapperComponent } from 'app/component/inno-modal-wrapper/inno-modal-wrapper.component';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { ItemAndServiceViewEnum } from 'app/enum/item-service.enum';
import { SharedModule } from 'app/module/shared.module';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { LoadItemComponent } from './load-item/load-item.component';
import { LoadServiceComponent } from './load-service/load-service.component';
import { TranslateService } from '@ngx-translate/core';
import { calculateGroupedTaxes } from '../../../utils/invoice.helper';

interface IAddTrackingByClient {
  client: Record<string, any>, // id, name
  listIdTimeTrackingSelected: InvoiceItem[]
}

@Component({
  selector: 'app-add-new-item',
  templateUrl: './add-new-item.component.html',
  styleUrl: './add-new-item.component.scss',
  standalone: true,
  imports: [
    SharedModule,
    InnoTabsComponent,
    FormatNumberPipe,
    InnoModalWrapperComponent,
    InnoModalFooterComponent,
    InnoFormCheckboxComponent,
    InnoSpinomponent,
    InnoEmptyDataComponent,
    LoadItemComponent,
    LoadServiceComponent
  ]
})
export class AddNewItemComponent implements OnInit {
  public currentTypeView?: ItemAndServiceViewEnum = ItemAndServiceViewEnum.Item;
  public itemServiceView = ItemAndServiceViewEnum;
  public listTypeView = [
  ]

  public clientName: string = ''
  public listInvoiceItem: InvoiceItem[] = []

  public listIndexInvoiceSelected: number[] = []
  public isFetchingProject: boolean = false

  static getComponent(): typeof AddNewItemComponent {
    return AddNewItemComponent;
  }

  public translate = inject(TranslateService)
  constructor(
    public dialogRef: MatDialogRef<AddNewItemComponent>,
    @Inject(MAT_DIALOG_DATA) public data?: IAddTrackingByClient
  ) {
    this.clientName = data?.client?.['clientName'] ?? ''
    this.listTypeView = [
      { label: this.translate.instant('SELECTTIMETRACKING.Tabs.Items'), value: ItemAndServiceViewEnum.Item },
      { label: this.translate.instant('SELECTTIMETRACKING.Tabs.Services'), value: ItemAndServiceViewEnum.Service },
    ]
  }
  handleClose() {
    this.dialogRef.close();
  }
  handleChangeTypeView(_typeView: ItemAndServiceViewEnum) {
    if (this.currentTypeView === _typeView) return
    this.currentTypeView = _typeView
  }

  ngOnInit(): void {

  }

  handleCheckedAll(isChecked: any) {
    if (isChecked) {
      this.listIndexInvoiceSelected = this.listInvoiceItem.map((_item, index) => index)
    } else {
      this.listIndexInvoiceSelected = []
    }
  }

  isCheckedIndex(index: number): boolean {
    return this.listIndexInvoiceSelected.includes(index)
  }

  handleToggleCheckedIndex(index: number) {
    const isChecked = this.isCheckedIndex(index)
    let newListSelected = [...this.listIndexInvoiceSelected]

    if (isChecked) {
      newListSelected = newListSelected.filter(_i => _i !== index)
    } else {
      newListSelected.push(index)
    }
    this.listIndexInvoiceSelected = newListSelected
  }

  totalAmount() {
    const resultTax = calculateGroupedTaxes(this.listInvoiceItem)
    return resultTax.subtotal + resultTax.grandTotalTax ?? 0
  }

  handleCancel() {
    this.dialogRef.close();
  }
  handleSubmitItem($event) {
    this.dialogRef.close($event)

  }


  handleSubmit() {
    const listInvoiceItemSelected = this.listInvoiceItem.filter((_item, index) => this.listIndexInvoiceSelected.includes(index))
    this.dialogRef.close(listInvoiceItemSelected)
  }
}
