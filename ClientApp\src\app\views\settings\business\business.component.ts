import { CdnService } from './../../../service/cdn.service';
import { InnoUploadComponent } from 'app/component/inno-upload/inno-upload.component';
import { StoreService } from './../../../service/store.service';
import { TypeDateTimeFormat } from './../../../utils/datetime-format';
import { CompanyService } from './../../../service/company.service';
import { SharedModule } from './../../../module/shared.module';
import { Component, DestroyRef, inject, OnInit } from '@angular/core';
import { FormControl, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { SpinnerService } from 'app/service/spinner.service';
import { COUNTRIES } from 'app/utils/country-items';
import moment from 'moment-timezone';
import { provideNgxMask } from 'ngx-mask';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ToastService } from 'app/service/toast.service';
import { Company } from '../../../dto/interface/company.interface';
import { Location } from '@angular/common';
import { InnoFormInputComponent } from 'app/component/inno-form-input/inno-form-input.component';
import { InnoFormTextareaComponent } from 'app/component/inno-form-textarea/inno-form-textarea.component';
import { InnoFormSelectSearchComponent } from 'app/component/inno-form-select-search/inno-form-select-search.component';
import { IFilterDropdownOption } from 'app/dto/interface/common.interface';
import { TranslateService } from '@ngx-translate/core';
import { getBase64AndFileName } from 'app/helpers/common.helper';
@Component({
  selector: 'app-business',
  standalone: true,
  imports: [
    SharedModule,
    InnoFormInputComponent,
    InnoFormTextareaComponent,
    InnoFormSelectSearchComponent,
    InnoUploadComponent
  ],
  providers: [provideNgxMask()],
  templateUrl: './business.component.html',
  styleUrl: './business.component.scss'
})
export class BusinessComponent implements OnInit {
  private base64!: string;
  private type!: string;
  private filename!: string;
  imageUrl!: string;
  createdBy: string;
  public businessForm!: UntypedFormGroup;
  public dateFormatOption: IFilterDropdownOption[] = []
  public countriesOption: IFilterDropdownOption[] = COUNTRIES.map(e => ({ label: e.name, value: e.code }));
  public startWeekOption: IFilterDropdownOption[] = [
    { label: 'Monday', value: 'Monday' },
    { label: 'Sunday', value: 'Sunday' }
  ]
  public timezoneOption: IFilterDropdownOption[] = moment.tz.names().map((tz) => {
    const offset = moment.tz(tz).utcOffset() / 60;
    const formattedOffset = `UTC${offset >= 0 ? '+' : ''}${offset}:00`;
    return {
      label: `(${formattedOffset}) ${tz}`,
      value: tz
    };
  });

  private companyId!: string;
  private spiner_services = inject(SpinnerService)
  private destroyRef = inject(DestroyRef);
  private router = inject(Router);
  private _toastService = inject(ToastService)
  private _companyServices = inject(CompanyService)
  private formBuilder = inject(UntypedFormBuilder)
  private _storeService = inject(StoreService)
  private translate = inject(TranslateService)
  private cdnService = inject(CdnService)
  constructor(private location: Location) {
    this.businessForm = this.formBuilder.group({
      business: ["", Validators.compose([Validators.required])],
      email: ["", Validators.compose([Validators.required, Validators.email])],
      phone: ["", Validators.compose([Validators.required, Validators.pattern('[0-9]*'),
      Validators.minLength(10),
      Validators.maxLength(10)])],
      country: ['', Validators.compose([Validators.required])],
      address1: [''],
      address2: [''],
      tow_city: [''],
      state_povince: [''],
      postal_code: [''],
      timezone: [''],
      start_week_on: [''],
      dateFormat: [''],
      note: ['']
    },

    );
    this.businessForm.controls['timezone'].setValue(moment.tz.guess());
    this.businessForm.controls['start_week_on'].setValue("Monday");

    this.dateFormatOption = TypeDateTimeFormat.map((x) => (
      {
        label: `${x.value} ${moment().format(x.moment)}`,
        value: x.value
      }))
    this.businessForm.controls['dateFormat'].setValue(this.dateFormatOption[0].value);
  }

  ngOnInit() {
    this.GetBusiness();
  }
  GetImg(fileName: string) {
    this.cdnService.GetFile(fileName).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        const reader = new FileReader();
        reader.onload = () => {
          this.imageUrl = reader.result as string;
        };
        reader.readAsDataURL(res);
      }
    }
    )

  }
  get f() {
    return this.businessForm.controls as Record<string, FormControl>;
  }

  _handleData(data: any) {
    this.businessForm.controls["business"].setValue(data.businessName),
      this.businessForm.controls["phone"].setValue(data.phone),
      this.businessForm.controls["country"].setValue(data.country),
      this.businessForm.controls["address1"].setValue(data?.adress),
      this.businessForm.controls["address2"].setValue(data?.adress2),
      this.businessForm.controls["tow_city"].setValue(data.city),
      this.businessForm.controls["email"].setValue(data.email),
      this.businessForm.controls["state_povince"].setValue(data.province),
      this.businessForm.controls["postal_code"].setValue(data.postalCode),
      this.businessForm.controls["timezone"].setValue(data.timeZone ? data.timeZone : moment.tz.guess()),
      this.businessForm.controls["start_week_on"].setValue(data.startWeekOn),
      this.businessForm.controls["dateFormat"].setValue(data.dateFormat),
      this.businessForm.controls["note"].setValue(data.note)
    if (data.companyImage) {
      this.GetImg(data.companyImage)
    }
  }
  async handleChangePicture(files: any) {
    const pictureFile = files?.[0]
    const { base64, fileName, type } = await getBase64AndFileName(pictureFile)

    this.base64 = base64
    this.type = type
    this.filename = fileName;
  }
  GetBusiness() {
    this._companyServices.GetInforCompany().pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.createdBy = res.createdBy
        this._handleData(res)
        this.companyId = res.id
      }
    })
  }

  markAllControlsAsTouched() {
    Object.values(this.f).forEach(control => {
      control.markAsTouched();
    });
  }

  onSubmit() {
    if (this.businessForm.invalid) {
      this.markAllControlsAsTouched();
      return
    }

    this.spiner_services.show();
    let payload: Company = {
      businessName: this.businessForm.controls["business"].value,
      phone: this.businessForm.controls["phone"].value,
      email: this.businessForm.controls["email"].value,
      country: this.businessForm.controls["country"].value,
      adress: this.businessForm.controls["address1"].value,
      adress2: this.businessForm.controls["address2"].value,
      city: this.businessForm.controls["tow_city"].value,
      province: this.businessForm.controls["state_povince"].value,
      postalCode: this.businessForm.controls["postal_code"].value,
      timeZone: this.businessForm.controls["timezone"].value,
      startWeekOn: this.businessForm.controls["start_week_on"].value,
      dateFormat: this.businessForm.controls["dateFormat"].value,
      note: this.businessForm.controls["note"].value,
      createdBy: this.createdBy,
      base64: this.base64,
      type: this.type,
      filename: this.filename,

    }
    //  update business
    if (this._storeService.getChooseBusiness()) {
      payload["id"] = this.companyId
      this._companyServices.UpdateCompany(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this.spiner_services.hide();
          this._toastService.showSuccess(this.translate.instant('TOAST.Success'), this.translate.instant('TOAST.UpdateBusiness'))
          this.GetBusiness()
          if (res.dateFormat && res.dateFormat != "") {
            this._storeService.setdateFormat(res.dateFormat)
          }
        }
      })
    }
    else {
      // create business if have not
      this._companyServices.CreateCompany(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe({
        next: (res) => {
          if (res) {
            this.spiner_services.hide();
            this._toastService.showSuccess(this.translate.instant('TOAST.Save'), this.translate.instant('TOAST.Success'))
            this.router.navigate(['/']);
          }
        }
      }
      )
    }
  }

  handleBack() {
    this.location.back();
  }
}
