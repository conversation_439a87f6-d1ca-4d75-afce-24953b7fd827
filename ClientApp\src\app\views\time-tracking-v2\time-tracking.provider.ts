import { AuthenticationService } from 'app/auth/service/authentication.service';
import moment from 'moment-timezone';
import { inject, Injectable } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Parameter, TimeTrackingQueryParam } from 'app/dto/interface/queryParameter.interface';
import { TimeTrackingViewEnum } from 'app/enum/time-tracking.enum';
import { currentTimeZone, formatDateFilter, getStartAndEndOfMonth, getStartAndEndOfWeek } from 'app/helpers/common.helper';
import { DataService } from 'app/service/data.service';
import { StoreService } from 'app/service/store.service';
import { TimetrackingService } from 'app/service/timetracking.service';
import { ToastService } from 'app/service/toast.service';
import { Observable, of } from 'rxjs';
import { TimeTrackingFilter } from '../../dto/interface/timeTrackingFilter.interface';
import { TimeTracking } from '../../dto/interface/timeTracking.interface';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root'
})
export class TimeTrackingProvider {

  private storeService = inject(StoreService)
  private timeTrackingService = inject(TimetrackingService)
  private toastService = inject(ToastService)
  private authenticationService = inject(AuthenticationService)
  private dataService = inject(DataService)
  private translate = inject(TranslateService)
  private activatedRoute = inject(ActivatedRoute);

  constructor() { }

  private __updateFilter(data: Partial<TimeTrackingFilter>) {
    const currentFilter = this.dataService.GetTimeTrackingFilterValue()
    const newValueFilter = { ...currentFilter, ...data }
    this.dataService.SetNewTimeTrackingFilter(newValueFilter)
  }


  reloadTimeTrackingData(): Observable<any> {
    const currentFilter = this.dataService.GetTimeTrackingFilterValue()

    const typeView = currentFilter.typeView ?? TimeTrackingViewEnum.Day

    const page = this.activatedRoute.snapshot.queryParams['page'] || 1;
    const textSearch = currentFilter.textSearch ?? ''
    const dateSelected = currentFilter.dateSelected ?? new Date()
    const userSelected = currentFilter.userSelected ?? undefined
    const clientSelected = currentFilter.clientSelected ?? undefined
    const projectSelected = currentFilter.projectSelected ?? undefined
    const startDate = currentFilter.startDate ?? undefined
    const endDate = currentFilter.endDate ?? undefined

    let queryParams: TimeTrackingQueryParam = {
      Page: page,
      PageSize: 20,
      Search: textSearch,
      loggedBy: userSelected,
      projectId: projectSelected,
      clientId: clientSelected,
    }
    switch (typeView) {
      case TimeTrackingViewEnum.Day: {
        queryParams = {
          ...queryParams,
          filterDate: formatDateFilter(dateSelected),
        }
        break;
      }
      case TimeTrackingViewEnum.Week: {
        const week = getStartAndEndOfWeek(dateSelected ?? new Date())
        queryParams = {
          ...queryParams,
          Page: 0,
          startDate: week.startOfWeek,
          endDate: week.endOfWeek,
        }
        break;
      }
      case TimeTrackingViewEnum.Month: {
        const month = getStartAndEndOfMonth(dateSelected ?? new Date())
        queryParams = {
          ...queryParams,
          Page: 0,
          startDate: month.startOfMonth,
          endDate: month.endOfMonth,
        }
        break;
      }
      case TimeTrackingViewEnum.All: {
        queryParams = {
          ...queryParams,
          startDate: formatDateFilter(startDate),
          endDate: formatDateFilter(endDate),
        }
        break;
      }

      default: {
        return of(null)
      }
    }
    return this.timeTrackingService.GetAllTimeTracking(queryParams)
  }

  handleCreateTimeTracking({
    payload,
    optional
  }: {
    payload: TimeTracking
    optional?: {
      callbackSuccess?: () => void,
    }
  }) {
    const defaultPayload: TimeTracking = {
      memberId: null,
      service: null,
      startTime: null,
      serviceId: null,
      clientId: null,
      projectId: null,
    }


    //   const userId = this.authenticationService.getIdUser();

    const isInvalid =
      !payload.date ||
      !payload.endTime// ||
    //    !companyId //||
    // !userId

    if (isInvalid) {
      return this.toastService.showError("Fail", "Missing required fields");
    }

    //  payload.userId = payload.userId ? payload.userId : userId
    payload = { ...defaultPayload, ...payload }
    this.timeTrackingService.CreateTimeTracking(payload)
      .subscribe({
        next: () => {
          localStorage.removeItem("isRunning")
          this.toastService.showSuccess(this.translate.instant("TOAST.Create"), this.translate.instant("TOAST.Success"));
          this.dataService.triggerRefreshListTimeTracking()
          if (optional?.callbackSuccess) optional.callbackSuccess()
        },
        error: () => {
          this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
        }
      })
  }

  handleUpdateTimeTracking({
    payload,
    optional
  }: {
    payload: TimeTracking,
    optional?: {
      callbackSuccess?: () => void,
    }
  }) {
    const defaultPayload: TimeTracking = {
      memberId: null,
      service: null,
      startTime: null,
      serviceId: null,
      clientId: null,
      projectId: null,
    }

    //  const userId = this.authenticationService.getIdUser();

    const isInvalid =
      !payload.date ||
      !payload.endTime //||
    //!companyId// ||
    //!userId

    if (isInvalid) {
      return this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.MissingRequiredFields"));
    }

    // payload.userId = payload.userId ? payload.userId : userId
    payload.id = this.dataService.getResume().id
    payload = { ...defaultPayload, ...payload }

    this.timeTrackingService.UpdateTimeTracking(payload)
      .subscribe({
        next: () => {
          localStorage.removeItem("ResumeData")
          localStorage.removeItem("isRunning")
          this.toastService.showSuccess(this.translate.instant("TOAST.Update"), this.translate.instant("TOAST.Success"));
          this.dataService.triggerRefreshListTimeTracking()
          if (optional?.callbackSuccess) optional.callbackSuccess()
        },
        error: () => {
          this.toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
        }
      })
  }

  handleChangeDateFilter(dateSelected?: Date) {
    this.__updateFilter({ dateSelected })
  }

  handleNextDate() {
    const currentFilter = this.dataService.GetTimeTrackingFilterValue()
    const dateSelected = currentFilter.dateSelected ?? new Date()
    const timeZone = moment.tz.guess();
    let newDate = moment.tz(dateSelected, timeZone);

    switch (currentFilter.typeView) {
      case TimeTrackingViewEnum.Day:
        newDate = newDate.add(1, 'day');
        break;
      case TimeTrackingViewEnum.Week:
        newDate = newDate.add(1, 'week');
        break;
      case TimeTrackingViewEnum.Month:
        newDate = newDate.add(1, 'month');
        break;
      default:
        break;
    }

    this.__updateFilter({ dateSelected: newDate.toDate() })
  }

  handlePreDate() {
    const currentFilter = this.dataService.GetTimeTrackingFilterValue()
    const dateSelected = currentFilter.dateSelected ?? new Date()
    const timeZone = moment.tz.guess();
    let newDate = moment.tz(dateSelected, timeZone);

    switch (currentFilter.typeView) {
      case TimeTrackingViewEnum.Day:
        newDate = newDate.subtract(1, 'day');
        break;
      case TimeTrackingViewEnum.Week:
        newDate = newDate.subtract(1, 'week');
        break;
      case TimeTrackingViewEnum.Month:
        newDate = newDate.subtract(1, 'month');
        break;
      default:
        break;
    }

    this.__updateFilter({ dateSelected: newDate.toDate() })
  }

  handleChangeTypeView(typeView: TimeTrackingViewEnum) {
    this.__updateFilter({ typeView })
  }
}
