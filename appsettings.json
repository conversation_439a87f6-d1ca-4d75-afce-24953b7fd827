{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ConnectionStrings": {
    //"InnoLogicielContext": "Host=localhost;Port=5432;Database=TimeKeeping;Username=postgres;Password=*********;"
    "InnoLogicielContext": "Host=localhost;Port=5433;Database=TimeKeeping;Username=inno;Password=******;"
  },
  "ActivateAccountUrl": "http://localhost:4200/login",
  "NavigationInvite": "http://localhost:4200/navigation-invite",
  "ResetPasswordUrl": "http://localhost:4200/reset-password",
  "Invoice": "https://app.innobooks.net",
  "InviteUrlMember": "http://localhost:4200/activate-invite",
  "Host": "https://localhost:7295",
  "ENCRYPTION_KEY": "t79G3J4MjzqeW7dxywtpGH3q3YLsTJpKz2V8TR8IfIgpgqLBzpC6vBwdzu6XQC34",
  "Jwt": {
    "SecretKey": "t79G3J4MjzqeW7dxywtpGH3q3YLsTJpKz2V8TR8IfIgpgqLBzpC6vBwdzu6XQC34",
    "Issuer": "http://localhost:4200",
    "Audience": "http://localhost:4200"
  },
  "Stripe": {
    "ApiKey": "pk_test_51N5uenLemcGBQjMPOuFDyJbLBB0KNaOX3O7Ey9THHkIjvxWKNaY6vZMAOqxEAy7YUbUfB8jLedfGh0k666jYgEad00iyeNr2IB",
    "ApiSecret": "sk_test_51N5uenLemcGBQjMPaVSukz31Pkc0YZFmJAYEsqsBabLiIKToHxxJuQL1uKoE8aXANzjhf1bR5FVR8gYrPgq6GvNd00Bo4JD134",
    "WebhookSecret": "whsec_DEINGJPcF9RjCHJ1qDDgHJ7JZAfmqu59",
    "RedirectUrl": "https://app.innobooks.net/billing"
  },
  "DigitalOceanSpace": {
    "BucketName": "innobooks",
    "Key": "Wo2AbuDVtLrwLrlG1l2s",
    "Secret": "9V6Ihp8dRirHKJciOyW3xhs7gP2Mcwn8FSTrpqI1",
    "Endpoint": "https://minio.s3inno.com"
  },
  "AllowedHosts": "*",
  "Smtp": {
    "Host": "smtp-relay.gmail.com",
    "Port": "587",
    "From": "<EMAIL>",
    "DKIM_Selector": "default"
  }
}
