﻿using InnoBook.DTO.Company;
using InnoBook.Request.Company;
namespace InnoBook.Interface
{
    public interface ICompanyService
    {
        Task Insert(RequestCompany model,string IdUser);
        Task<bool> Update(RequestCompany model, string companyId);
        Task<bool> RemoveImgCompany(string companyId);
        Task<string>  GetCurrencyCompany(Guid id);
        Task UpdateFinancial(RequestCompany model);
        Task<GetCompanyDTO> GetCompanyById(Guid id);
    }
}
