import { Component } from '@angular/core';
import { SharedModule } from 'app/module/shared.module';
import { ReportCardComponent } from './report-card/report-card.component';

@Component({
  selector: 'app-report',
  standalone: true,
  imports: [SharedModule, ReportCardComponent],
  templateUrl: './report.component.html',
  styleUrl: './report.component.scss'
})
export class ReportComponent {
  topNavItems = [
    { label: 'Expense Report', icon: 'ic_Report.svg' },
    { label: 'Item Sales', icon: 'ic_Sales.svg' },
    { label: 'Accounts Aging', icon: 'ic_Sales.svg' },
    { label: 'Trial Balance', icon: 'ic_Balance.svg' },
    { label: 'Cash Flow', icon: 'ic_Flow.svg' },
    { label: 'Retainer Summary', icon: 'ic_Summary.svg' },
  ];

  MenuReport = [
    {
      title: "Invoice and Expense Reports",
      submenu: [
        { title: 'Invoice Details', description: 'Summary of all invoices sent...', icon: 'fas fa-file-alt' },
        { title: 'Expense Report', description: 'Overview of your spending...', icon: 'fas fa-receipt', starred: true },
        { title: 'Item Sales', description: 'Breakdown of income...', icon: 'fas fa-tag', starred: true },
        { title: 'Revenue by Client', description: 'Track revenue per client...', icon: 'fas fa-user' },
      ]
    },
    {
      title: "Payment Reports",
      submenu: [
        { title: 'Accounts Aging', description: 'Identify clients with overdue...', icon: 'fas fa-clock', starred: true },
        { title: 'Payments Collected', description: 'List of payments received...', icon: 'fas fa-money-check' },
        { title: 'Credit Balance', description: 'See available client balances...', icon: 'fas fa-wallet' },
      ]
    }
  ]



}
