﻿using InnoBook.Attributes;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.TimeTracking;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace InnoBook.Controllers
{
    [Route("api/[controller]")]
    [Authorize]
    [ApiController]
    public class TimeTrackingController(ITimeTrackingService timeTrackingService, IHttpContextAccessor httpContextAccessor) : BaseController(httpContextAccessor)
    {
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Employee, UserBusinessRole.Contractor)]
        [HttpDelete("DeleteTimeTracking")]
        //[Ownership(typeof(TimeTracking))]
        public async Task<IActionResult> DeleteTimeTracking([FromBody]List<Guid?> listTimeTracking)
        {
            var result = await timeTrackingService.DeleteTimeTracking(listTimeTracking);
            return Ok(result);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Employee, UserBusinessRole.Contractor)]
        [HttpPost("UpdateTimeTracking")]
        //[Ownership(typeof(TimeTracking))]
        public async Task<IActionResult> UpdateTimeTracking(TimeTrackingRequest timeTracking)
        {
            timeTracking.CompanyId = Guid.Parse(IdCompany);
            var result = await timeTrackingService.UpdateTimeTracking(timeTracking, Guid.Parse(IdUser));
            return Ok(result);
        }

        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Employee, UserBusinessRole.Contractor)]
        [HttpPost("CreateTimeTracking")]
        public async Task<IActionResult> CreateTimeTracking(TimeTrackingRequest timeTracking)
        {
            timeTracking.CompanyId = Guid.Parse(IdCompany);
            var data = await timeTrackingService.CreateTimeTracking(timeTracking, Guid.Parse(IdUser), IdCompany);

            return Ok(data);
        }


        [RequiredRoles(UserBusinessRole.All)]
        [HttpGet("GetProjectAndClientService")]
        public async Task<IActionResult> GetProjectAndClientService([FromQuery] ProjectAndClientQueryParam query)
        {
            var role = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "businessRole")?.Value;
            var data = await timeTrackingService.GetProjectAndClientService(query, IdUser, IdCompany, role);
            return Ok(data);
        }

        
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Employee, UserBusinessRole.Contractor)]
        [HttpGet("CalculationTotalTimeByUser")]
        public async Task<IActionResult> CalculationTotalTimeByUser([FromQuery] GetTimeTrackingQueryParam query)
        {
            try
            {
                var data =  timeTrackingService.CalculationTotalTimeByUser(query, IdCompany);
                return Ok(data);
            }
            catch (ArgumentNullException ex)
            {
                return StatusCode(500, new { Message = "An error occurred while updating the role", Error = ex.Message });
            }
        }
        [RequiredRoles(UserBusinessRole.Admin, UserBusinessRole.Manager, UserBusinessRole.Employee, UserBusinessRole.Contractor)]
        [HttpGet("GetAllTimeTracking")]
        public async Task<IActionResult> GetAllTimeTracking([FromQuery] GetTimeTrackingQueryParam query)
        {
            try
            {
                var role = HttpContext.User.Claims.FirstOrDefault(c => c.Type == "businessRole")?.Value;
                var cliens = await timeTrackingService.GetAllTimTracking(query, IdUser, IdCompany, role);
                return Ok(cliens);
            }
            catch (ArgumentNullException ex)
            {
                return StatusCode(500, new { Message = "An error occurred while updating the role", Error = ex.Message });
            }
        }
    }
}
