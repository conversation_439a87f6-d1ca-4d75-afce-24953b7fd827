﻿using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Mail;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Services.Interface;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using System.Net;
using System.Security.Claims;

namespace InnoBook.Services.Classes
{
    public class MemberService(InnoLogicielContext context, IConfiguration config, IMailService _mail, IUserIdentity _userIdentity) : IMemberService
    {
        public async Task<int> CountMemberBusiness(string CompanyId)
        {
            var total = await context.Members.Where(c => c.CompanyId.ToString() == CompanyId&& c.Status== MemberStatus.Active).CountAsync();
            return total;
        }

        public bool SendMailAddMember(RequestAddMember addMember, string companyId)
        {
            try
            {
                var url = config.GetSection("InviteUrlMember").Value;

                string templateDirectory = "EmailTemplates";
                var templateService = new TemplateService(templateDirectory);

                // Generate token for the invite link, valid in 30 minutes
                var claims = new List<Claim>() 
                { 
                    new Claim(ClaimTypes.Email, addMember.Email),
                    new Claim("companyId", companyId),
                };
                var token = _userIdentity.GenerateAccessToken(claims);

                // Load and process template
                string templateContent = templateService.LoadTemplate("InviteMember.html");
                var placeholders = new Dictionary<string, string>
                {
                    { "Email",  addMember.Email },
                    { "url", $"{url}?email={WebUtility.UrlEncode(addMember.Email)}" +
                             $"&firstName={WebUtility.UrlEncode(addMember.FirstName)}" +
                             $"&lastName={WebUtility.UrlEncode(addMember.LastName)}" +
                             $"&token={WebUtility.UrlEncode(token)}" }
                };
                string emailBody = templateService.ProcessTemplate(templateContent, placeholders);
                var mail = new MailInfoAndContent
                {
                    To = addMember.Email,
                    Subject = "Invite Member",
                    Body = emailBody
                };

                _mail.SendMail(mail);
                return true;
            }
            catch (Exception ex)
            {
                throw  new Exception("Error Send Email");
            }
        }
    }
}
