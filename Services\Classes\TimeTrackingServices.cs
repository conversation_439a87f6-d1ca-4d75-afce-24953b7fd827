using Amazon.S3.Model;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Project;
using InnoBook.DTO.TimeTracking;
using InnoBook.Entities;
using InnoBook.Enum;
using InnoBook.Extension;
using InnoBook.Services.Interface;
using InnoLogiciel.Common;
using InnoLogiciel.Server.Contexts;
using Microsoft.EntityFrameworkCore;
using System.Data;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace InnoBook.Services.Classes
{
    public class TimeTrackingServices(InnoLogicielContext context) : ITimeTrackingService
    {
        public async Task<TimeTrackingRequest> CreateTimeTracking(TimeTrackingRequest timeTracking, Guid userId, string companyId)
        {
            try
            {
                string serviceId = await GetOrCreateServiceId(timeTracking, userId);
                Guid? memberId = await GetOrCreateMemberId(timeTracking, userId, companyId);

                var tracking = new TimeTracking
                {
                    MemberId = memberId,
                    ServiceId = string.IsNullOrEmpty(serviceId) ? null : Guid.Parse(serviceId),
                    Date = timeTracking.Date.Value.ToUniversalTime(),
                    ClientId = timeTracking.ClientId ?? null,
                    Billable = timeTracking.Billable,
                    ProjectId = timeTracking.ProjectId,
                    CompanyId = timeTracking.CompanyId,
                    CreatedBy = userId.ToString(),
                    Description = timeTracking.Description,
                    EndTime = timeTracking.EndTime ?? null,
                    StartTime = timeTracking.StartTime ?? null
                };

                context.TimeTrackings.Add(tracking);
                await context.SaveChangesAsync();

                return timeTracking;
            }
            catch (Exception ex)
            {
                // Log the exception here if needed
                throw new ApplicationException("An error occurred while creating the time tracking.", ex);
            }
        }

        private async Task<string> GetOrCreateServiceId(TimeTrackingRequest timeTracking, Guid userId)
        {
            if (timeTracking.Service != null && timeTracking.ServiceId == null)
            {
                var service = new Service
                {
                    ServiceName = timeTracking.Service.ServiceName,
                    //ProjectId = timeTracking.ProjectId != null ? Guid.Parse(timeTracking.ProjectId.ToString()) : (Guid?)null,
                    CreatedBy = userId.ToString(),
                    TotalHours = 0,
                    Rate = 0,
                    ActualHours = 0
                };

                context.Services.Add(service);
                await context.SaveChangesAsync();

                return service.Id.ToString();
            }

            return timeTracking.ServiceId?.ToString();
        }

        private async Task<Guid?> GetOrCreateMemberId(TimeTrackingRequest timeTracking, Guid userId, string companyId)
        {
            if (timeTracking.MemberId == null && timeTracking.ProjectId != null)
            {
                var member = await context.Members
                    .FirstOrDefaultAsync(x => x.UserId == userId && x.ProjectId == timeTracking.ProjectId);

                if (member == null)
                {
                    member = new Member
                    {
                        ProjectId = Guid.Parse(timeTracking.ProjectId.ToString()),
                        UserId = userId,
                        CompanyId = Guid.Parse(companyId)
                    };

                    context.Members.Add(member);
                    await context.SaveChangesAsync();
                }

                return member.Id;
            }

            return timeTracking.MemberId;
        }
        public async Task<PaginatedResponse<TimeTrackingDTO>> GetAllTimTracking(GetTimeTrackingQueryParam filter, string userId, string companyId, string role)
        {
            Guid companyUid = Guid.Parse(companyId);

            var query = context.TimeTrackings.Include(c => c.Service)
                                            .Include(c => c.Client)
                                            .Include(c => c.Project)
                                            .Include(c => c.Member)
                                            .ThenInclude(c => c.User)
                                            .Where(c => c.CompanyId == companyUid);

            // Filter DateTime
            if (!string.IsNullOrEmpty(filter.TimeZone))
            {
                // Filter Date
                if (!string.IsNullOrEmpty(filter.FilterDate) && DateTime.TryParse(filter.FilterDate, out var date))
                {
                    var utcDate = Utils.GetStartAndEndOfUtcDate(date, filter.TimeZone);
                    query = query.Where(x => x.Date >= utcDate.StartUtcDateTime && x.Date < utcDate.EndUtcDateTime);
                }

                // Filter Date Range
                if (!string.IsNullOrEmpty(filter.StartDate) && DateTime.TryParse(filter.StartDate, out var startDate)
                    && !string.IsNullOrEmpty(filter.EndDate) && DateTime.TryParse(filter.EndDate, out var endDate))
                {
                    var utcStartDate = Utils.GetStartAndEndOfUtcDate(startDate, filter.TimeZone);
                    var utcEndDate = Utils.GetStartAndEndOfUtcDate(endDate, filter.TimeZone);
                    query = query.Where(x => utcStartDate.StartUtcDateTime <= x.Date && x.Date < utcEndDate.EndUtcDateTime);
                }
            }

            // Filter LoggedBy
            if (!string.IsNullOrEmpty(filter.Loggedby))
            {
                // If get all skip logged in user time tracking
                if (filter.Loggedby.ToLower() != UWIPConstants.FilterAllTimeTracking)
                {
                    query = query.Where(c => c.CreatedBy == filter.Loggedby);
                }
            }
           
            // Just get logged in user time tracking except Admin, Manager
            else if (role != UserBusinessRole.Admin && role != UserBusinessRole.Manager || string.IsNullOrEmpty(filter.Loggedby))
            {
                query = query.Where(c => c.CreatedBy == userId);
            }
            
            // Filter ClientId
            if (filter.ClientId != null)
            {
                query = query.Where(c => c.ClientId == filter.ClientId);
            }

            // Filter ProjectId
            if (filter.ProjectId != null)
            {
                query = query.Where(c => c.ProjectId == filter.ProjectId);
            }

            // Search
            if (!string.IsNullOrEmpty(filter.Search))
            {
                query = query.Where(c => c.Client != null && c.Client.ClientName.ToLower().Contains(filter.Search.ToLower()));
            }

            // Filter IsBillable
            if (filter.IsUnBill != null)
            {
                query = query.Where(c => c.Billable && !c.isBilled);
            }

            int totalRecords = await query.CountAsync();

            var panigationQuery = query.AsNoTracking()
                                .OrderBy(x => x.Date)
                            .Select(tt => new TimeTrackingDTO
                            {
                                id = tt.Id,
                                billable = tt.Billable,
                                isBilled = tt.isBilled,
                                serviceId = tt.ServiceId,
                                clientId = tt.ClientId,
                                projectId = tt.ProjectId,
                                memberId = tt.MemberId,
                                date = tt.Date,
                                description = tt.Description,
                                startTime = tt.StartTime,
                                endTime = tt.EndTime,
                                client = tt.Client != null ? new ClientInTimeTrackingDTO
                                {
                                    Id = tt.Client.Id,
                                    ClientName = tt.Client.ClientName
                                } : null,
                                user = tt.Member.User != null ? new UserTimeTrackingDTO
                                {
                                    email = tt.Member.User.Email,
                                    firstName = tt.Member.User.FirstName,
                                    lastName = tt.Member.User.LastName
                                } : null,
                                project = tt.Project != null ? new ProjectInTimeTrackingDTO
                                {
                                    Id = tt.Project.Id,
                                    ProjectName = tt.Project.ProjectName,
                                    HourlyRate = tt.Project.HourlyRate,
                                } : null,

                                service = tt.Service != null ? new ServiceInTimeTrackingDTO
                                {
                                    Id = tt.Service.Id,
                                    ServiceName = tt.Service.ServiceName
                                } : null,



                            });

            // Sort
            if (filter.ColumnName != null && filter.Direction != null)
            {
                panigationQuery = SortExtensions.DynamicSort(panigationQuery, filter.ColumnName, filter.Direction == "Descending");
            }
            else
                panigationQuery = SortExtensions.DynamicSort(panigationQuery, "Date", true);


            if (filter.Page > 0 && filter.PageSize > 0)
            {
                panigationQuery = panigationQuery.Skip((filter.Page - 1) * filter.PageSize)
                                                 .Take(filter.PageSize);
            }
            var data = await panigationQuery.ToListAsync();

            return new PaginatedResponse<TimeTrackingDTO>
            {
                Data = data,
                Page = filter.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)filter.PageSize),
                PageSize = filter.PageSize,
                TotalRecords = totalRecords
            };
        }
        

        public async Task<PaginatedResponse<ProjectAndClientDTO>> GetProjectAndClientService(ProjectAndClientQueryParam filter, string userId, string companyId, string role)
        {
            // Build query
            var query = context.Clients.Include(c => c.Projects)
                                        .ThenInclude(c => c.Members)
                                        .Where(c => c.isActive && c.CompanyId == Guid.Parse(companyId));

            bool restrictFilter = false;
            // Filter assigned projects in case user get all
            if (filter.GetAll == null || !UWIPConstants.HIGH_LEVEL_ROLES.Contains(role))
            {
                // Restrict normal users only get the assigned projects
                query = query.Where(c => c.Projects.Any(p => p.isActive && !p.isArchive && p.Members.Select(m => m.UserId).Contains(Guid.Parse(userId))));
                restrictFilter = true;
            }

            // Search
            if (!string.IsNullOrEmpty(filter.Search))
            {
                string lowerSearch = filter.Search.ToLower();
                query = query.Where(c => c.ClientName.ToLower() == lowerSearch || c.Projects.Any(c => c.ProjectName.ToLower() == lowerSearch));
            }

            var data = query.AsNoTracking().Select(cl => new ProjectAndClientDTO
                                        {
                                            Id = cl.Id,
                                            ClientName = cl.ClientName,
                                            Projects = cl.Projects.Where(p => p.isActive && !p.isArchive && (!restrictFilter || p.Members.Select(m => m.UserId).Contains(Guid.Parse(userId))))
                                                                 .Select(p => new ProjectInClientDTO
                                                                 {
                                                                     ProjectName = p.ProjectName,
                                                                     Description = p.Description,
                                                                     Id = p.Id,
                                                                     isArchive = p.isArchive,
                                                                     Billable = p.Billable
                                                                 }).OrderBy(c => c.ProjectName).ToList()

                                        }).OrderBy(c => c.ClientName)
                                        .AsQueryable();

            if (filter.GetAll == false && filter.Page > 0 && filter.PageSize > 0)
            {
                data = data.Skip((filter.Page - 1) * filter.PageSize)
                           .Take(filter.PageSize);
            }

            var totalRecords = await data.CountAsync();
            var result = await data.ToListAsync();

            return new PaginatedResponse<ProjectAndClientDTO>()
            {
                PageSize = filter.PageSize,
                Page = filter.Page,
                TotalPage = (int)Math.Ceiling(totalRecords / (decimal)filter.PageSize),
                Data = result,
                TotalRecords = totalRecords,
            };
        }

        public async Task<bool> DeleteTimeTracking(List<Guid?> listTimeTracking)
        {
            try
            {

                var listItemDetail = context.TimeTrackings.Where(x => listTimeTracking.Contains(x.Id)).ToList();
                context.TimeTrackings.RemoveRange(listItemDetail);
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }

        }

        public async Task<bool> UpdateTimeTracking(TimeTrackingRequest TimeTracking,Guid UserId)
        {
            try
            {
                var data = context.TimeTrackings.FirstOrDefault(x => x.Id == TimeTracking.Id);
                data.UpdatedBy = UserId.ToString();
                data.EndTime = TimeTracking.EndTime;
                data.ProjectId = TimeTracking.ProjectId;
                data.ServiceId = TimeTracking.ServiceId;
                data.Billable = TimeTracking.Billable;
                data.ClientId = TimeTracking.ClientId;
                data.Date = TimeTracking.Date;
                data.Description = TimeTracking.Description;
                context.TimeTrackings.Update(data);
                await context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public CalculationTotalTimeDTO  CalculationTotalTimeByUser(GetTimeTrackingQueryParam filter,string companyUid)
        {
            var data = context.TimeTrackings
                                           .Where(c => c.CompanyId.ToString() == companyUid && c.CreatedBy == filter.Loggedby).AsQueryable();


            if (!string.IsNullOrEmpty(filter.TimeZone))
            {
                // Filter Date
                if (!string.IsNullOrEmpty(filter.FilterDate) && DateTime.TryParse(filter.FilterDate, out var date))
                {
                    var utcDate = Utils.GetStartAndEndOfUtcDate(date, filter.TimeZone);
                    data = data.Where(x => x.Date >= utcDate.StartUtcDateTime && x.Date < utcDate.EndUtcDateTime);
                }

                // Filter Date Range
                if (!string.IsNullOrEmpty(filter.StartDate) && DateTime.TryParse(filter.StartDate, out var startDate)
                    && !string.IsNullOrEmpty(filter.EndDate) && DateTime.TryParse(filter.EndDate, out var endDate))
                {
                    var utcStartDate = Utils.GetStartAndEndOfUtcDate(startDate, filter.TimeZone);
                    var utcEndDate = Utils.GetStartAndEndOfUtcDate(endDate, filter.TimeZone);
                    data = data.Where(x => utcStartDate.StartUtcDateTime <= x.Date && x.Date < utcEndDate.EndUtcDateTime);
                }
            }

            TimeSpan totalLoggedTime = TimeSpan.FromSeconds(0);
            data.ToList().ForEach(t =>
            {
                if (TimeSpan.TryParse(t.EndTime, out TimeSpan time))
                {
                    totalLoggedTime = totalLoggedTime.Add(time);
                   
                }
            });
            string totalLoggedFormatted = $"{(int)totalLoggedTime.TotalHours}:{totalLoggedTime.Minutes:D2}:{totalLoggedTime.Seconds:D2}";

            var result = new CalculationTotalTimeDTO
            {
                totalLoggedFormatted = totalLoggedFormatted
            };
            return result;
        }
       
    }
}
