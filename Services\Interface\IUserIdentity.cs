﻿using InnoBook.DTO;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.TimeTracking;
using InnoBook.DTO.User;
using InnoBook.Enum;
using InnoBook.Model;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace InnoBook.Services.Interface
{
    public interface IUserIdentity
    {
        Task<Jwt> Login(string email, string pass);
        Task<Jwt> SignIn(string email);
        Task<Jwt> RefreshToken(string refreshToken, string CompanyId);
        Task<string> GenerateUrlRecoverPass(string email);
        Task<string> CreateTokenRecoverPassword(string email);
        IActionResult RecoverPassword(string email);
        Task<GetUserProfileDTO> GetUserProfile(Guid Id);
        Task<bool> DeleteUser(string userId);
        ClaimsPrincipal GetPrincipalRefreshToken(string token);
        ClaimsPrincipal GetPrincipalFromExpiredToken(string token);
        public string GenerateAccessToken(IEnumerable<Claim> claims);
        Task<Jwt> UpdateAccessToken(string token, string newClaimType, string newClaimValue);
        string? GetIdCompanyFromRefreshToken(string refreshToken);
        public string GenerateRefreshToken(IEnumerable<Claim> claims);
        Task<PaginatedResponse<GetUserDTO>> GetAllUser(PaginatedRequest query);
        Task<GetUserDTO> FindUserEmailAndCode(string  email, string  code);
        Task<GetAllDataUserDTO> FindUserEmail(string email);  
        Task<CheckTimerDTO> CheckTimer(Guid Id);
        Task<GetUserDTO> FindId(Guid Id);
        Task<bool> Update(UserUpdateDTO Id);
        Task<bool> Update(string email, UserUpdateDTO user);
        Task UpdateTimer(UpdateTimerDTO user,Guid Id);
        Task<Guid> Insert(CreateUserDTO user);
        bool ChangePassWithToken(TokenPass tokenPass);
        Task<List<GetUserDTO>> ListUser();
        Task<Jwt> Register(RegisterUserDTO userDto);
        UserBusinessStatus GetUserStatus(string businessId);
        Task<ValidateInvitationResponse> ValidateInvitation(ValidateInvitationDto invitation);

        Task<bool> UpdateTwoFactorAsync(Guid userId, string code, DateTime expireTime);
    }
}
