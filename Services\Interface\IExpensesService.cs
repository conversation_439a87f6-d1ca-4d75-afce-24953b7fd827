﻿using InnoBook.DTO.Attachement;
using InnoBook.DTO.CoreModel;
using InnoBook.DTO.Expenses;
using InnoBook.Request.Expenses;
namespace InnoBook.Services.Interface
{
    public interface IExpensesService
    {
        Task<PaginatedResponse<DTOExpenses>> GetAllExpenses(GetExpenseRequestParam query, string companyId, string userId);
        Task<RequestExpenses> CreateExpenses(RequestExpenses expenses, string userId, string companyId);
        Task<DTOExpensesDetail> GetExpensesById(string Id);
        public Task<bool> MarkAsPaid(string Id);
        public Task<bool> DeleteExpenses(List<Guid?> listExpenses, string idUser);
        Task<RequestExpenses> UpdateExpenses(RequestExpenses expenses, string CompanyId,string userId);
        public Task<PaginatedResponse<AttachmentDTO>> GetAllUploadExpenses(GetUploadedAttachmentParam query);
    }
}
