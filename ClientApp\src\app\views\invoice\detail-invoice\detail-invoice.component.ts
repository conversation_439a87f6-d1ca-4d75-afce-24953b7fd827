import { CdnService } from './../../../service/cdn.service';
import { InnoSpinomponent } from 'app/component/inno-spin/inno-spin.component';
import { DataService } from 'app/service/data.service';
import { NewInvoiceDialog } from './../../../service/dialog/new-invoice.dialog';
import { AuthenticationService } from 'app/auth/service/authentication.service';
import { DecimalPipe } from './../../../pipes/decimal.pipe';
import { LayoutUtilsService } from './../../../core/services/layout-utils.service';
import { Location } from '@angular/common';
import { PaymentService } from './../../../service/payment.service';
import { UserBusiness } from 'app/dto/interface/userBusiness.interface';
import { SharedModule } from 'app/module/shared.module';
import { InvoiceService } from './../../../service/invoice.service';
import { StoreService } from 'app/service/store.service';
import { Component, DestroyRef, inject, OnInit, ViewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatMenuModule, MatMenuTrigger } from '@angular/material/menu';
import { ToastService } from 'app/service/toast.service';
import { Parameter } from 'app/dto/interface/queryParameter.interface';
import { Invoice } from 'app/dto/interface/invoice.interface';
import { GridAllModule, Pager, PagerDropDown, PagerModule } from '@syncfusion/ej2-angular-grids';
import { TranslateService } from '@ngx-translate/core';
import { AvatarModule } from 'ngx-avatars';
import { InnoStatusComponent } from 'app/component/inno-status/inno-status.component';
import { InnoPopoverComponent } from 'app/component/inno-popover/inno-popover.component';
import { getFullAddress, getFullAddressClient } from 'app/helpers/common.helper';
import { FormatNumberPipe } from 'app/pipes/format-number.pipe';
import { AddPaymentDialog } from '../../../service/dialog/add-payment.dialog';
import { ShareLinkDialog } from '../../../service/dialog/share-link.dialog';
import { SpinnerService } from 'app/service/spinner.service';

import { DuplicateInvoiceDialog } from 'app/service/dialog/dupicate-invoice.dialog';
import { PhoneMaskPipe } from 'app/pipes/phoneMask.pipe';
import { MenuActions } from 'app/utils/action-detail-invoice';
import { Role } from 'app/enum/role.enum';
import { calculateGroupedTaxes, getNameTaxes } from 'app/utils/invoice.helper';
Pager.Inject(PagerDropDown);
@Component({
  selector: 'app-detail-invoice',
  standalone: true,
  providers: [LayoutUtilsService],
  imports: [
    SharedModule,
    MatFormFieldModule,
    AvatarModule,
    RouterModule,
    MatMenuModule,
    GridAllModule,
    PagerModule,
    InnoStatusComponent,
    InnoSpinomponent,
    InnoPopoverComponent,
    FormatNumberPipe,
    PhoneMaskPipe,
    DecimalPipe,
  ],
  templateUrl: './detail-invoice.component.html',
  styleUrl: './detail-invoice.component.scss'
})
export class DetailInvoiceComponent implements OnInit {
  titleStatus: string = "Draft"
  dataSource: any;
  today = new Date();
  imageUrl!: string;
  public totalPages = 1;
  public currentPage: number = 1
  public pageSizes = [10, 20, 50, 100];
  pageSizesDefault: number = 10
  public selectionOptions: Object = { type: 'Multiple', checkboxOnly: true };
  InforInvoice: Invoice | undefined = undefined
  listChoosePayment: string[] = [];
  InforBussiness!: UserBusiness;
  isErrorTotal: boolean = false
  isErrorClient: boolean = false
  clientId!: string;
  reference!: string;
  note!: string;
  nameTax: string = ''
  sumtax: number = 0;
  subtotal: number = 0;
  qty!: number;
  invoiceNumber: string = "0000001"
  total!: number;
  rate!: number;
  listTax: any[] = []
  taxArray: { name: string, total: number, numberTax: string, amount: number }[] = [];
  selectedDate!: string;
  selectedDueDate!: string;
  public calculateGroupedTaxes = calculateGroupedTaxes
  public getNameTaxes = getNameTaxes
  public listClient: any[] = [];
  @ViewChild('actiontMenuTrigger') actiontMenuTrigger!: MatMenuTrigger;
  clientName!: string;
  _id!: string;
  isAccountant: boolean = false;

  private layoutUtilsService = inject(LayoutUtilsService)
  protected activatedRoute = inject(ActivatedRoute);
  router = inject(Router);
  destroyRef = inject(DestroyRef);
  private _paymentService = inject(PaymentService)
  private translate = inject(TranslateService);
  private _toastService = inject(ToastService)
  public _storeService = inject(StoreService)
  private _invoiceService = inject(InvoiceService)
  private authenticationService = inject(AuthenticationService)
  private duplicateInvoiceDialog = inject(DuplicateInvoiceDialog)
  private dataService = inject(DataService)
  private cdnService = inject(CdnService)
  constructor(
    private location: Location,
    private spinnerService: SpinnerService,
    private addPaymentDialog: AddPaymentDialog,
    private editInvoiceDialog: NewInvoiceDialog,
    private shareLinkDialog: ShareLinkDialog) {
    this.InforBussiness = this._storeService.get_UserBusiness();
  }
  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
  convertToHours(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours + minutes / 60;
  }
  GetImg(fileName: string) {

    this.cdnService.GetFile(fileName).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        const reader = new FileReader();
        reader.onload = () => {
          this.imageUrl = reader.result as string;
        };
        reader.readAsDataURL(res);
      }
    }
    )

  }
  get businessInfo() {
    return {
      businessName: this.InforInvoice?.company?.businessName ?? '',
      businessPhoneNumber: this.InforInvoice?.company?.phone ?? '',
      businessAddress: getFullAddress({
        addressLine1: this.InforInvoice?.company?.adress ?? '',
        addressLine2: this.InforInvoice?.company?.adress2 ?? '',
        city: this.InforInvoice?.company?.city ?? '',
        stateProvince: this.InforInvoice?.company?.province ?? '',
        postalCode: this.InforInvoice?.company?.postalCode ?? '',
      }),
    }
  }
  get businessInfoClient() {
    return {
      businessAddress: getFullAddressClient({
        addressLine1: this.InforInvoice?.client?.addressLine1 ?? '',
        addressLine2: this.InforInvoice?.client?.addressLine2 ?? '',
        townCity: this.InforInvoice?.client?.townCity ?? '',
        stateProvince: this.InforInvoice?.client?.stateProvince ?? '',
        country: this.InforInvoice?.client?.country ?? '',
      }),
    }
  }

  handleAction(action: string) {
    switch (action) {
      case 'paid':
        this.handleMarkAsPaid();
        break;
      case 'sent':
        this.handleMarkAsSent();
        break;
      case 'duplicate':
        this.handleDuplicate();
        break;
      case 'download':
        this.handleDownloadPDF();
        break;
      case 'print':
        this.handleFunctionInDevelopment();
        break;
      case 'archive':
        this.handleArchive();
        break;
      case 'delete':
        this.handleDelete();
        break;
      default:
        this.handleFunctionInDevelopment();
        break;
    }
  }
  handleDuplicate() {
    const dialogRef = this.duplicateInvoiceDialog.open({ id: this._id, isInvoice: true });

    dialogRef.then((c) => {
      c.afterClosed().subscribe((res) => {
        if (!res) return
      })
    });

  }
  handleMarkAsPaid() {
    this._invoiceService.MarkAsPaid(this._id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this._toastService.showSuccess("Mark As Paid", "Success");
        this.GetInvoiceById(this._id);
      }
    }
    )
  }
  handleMarkAsSent() {
    this._invoiceService.MarkAsSent(this._id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe(res => {
      if (res) {
        this._toastService.showSuccess("Mark As Sent", "Success");
        this.GetInvoiceById(this._id);
      }
    }
    )
  }
  handleBack() {
    this.location.back();
  }
  get filteredMenu() {
    return MenuActions.filter(action =>
      action.permissions.includes(this.authenticationService.getBusinessRole())
    );
  }
  ngOnInit(): void {

    this.activatedRoute.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((params: any) => {
      if (params?.id) {
        this._id = params?.id
        this.GetInvoiceById(params?.id);
      }

    });
    this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((queryParams: any) => {
      if (queryParams?.page) {
        this.currentPage = queryParams.page
        this.GetAllPaymentAsync(this.currentPage);
        return;
      }
      this.GetAllPaymentAsync(this.currentPage)
    });

    this._storeService.getRoleBusinessAsObservable().subscribe(role => {
      this.isAccountant = role === Role.Accountant;
    })
  }
  _handleData(_data: any) {
    let index = this.listClient.findIndex(x => x.clientId.toString() == this.InforInvoice?.clientId)
    if (index >= 0) {
      this.clientName = this.listClient[index].clientName
    }
    this.selectedDueDate = this.formatDate(new Date(this.InforInvoice?.dueDate ?? 0));
    this.selectedDate = this.formatDate(new Date(this.InforInvoice?.invoiceDate ?? 0));
    this.note = this.InforInvoice?.notes
    this.rate = this.InforInvoice?.rate ?? 0
    this.qty = this.InforInvoice?.timeAmount ?? 0
    this.total = this.InforInvoice?.paidAmount ?? 0
    this.reference = this.InforInvoice?.reference

  }

  GetInvoiceById(_id: string) {
    this._invoiceService.GetInvoiceById(_id).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.InforInvoice = res;
        if (this.InforInvoice.img) {
          this.GetImg(this.InforInvoice.img)
        }
        else {
          this.GetImg(this.InforInvoice?.company?.companyImage)
        }
        this._handleData(res)
        this.calculateAllTax();
      }

    })
  }

  handleDownloadPDF() {
    this.spinnerService.show();
    this._invoiceService.PrintInvoiceById(this._id, this.InforInvoice.invoiceNumber);
  }


  stopPropagation(event: any) {
    event.stopPropagation();
  }

  AddPayment() {
    const dialogRef = this.addPaymentDialog.open(this.InforInvoice);


    dialogRef.then((c) => {
      c.afterClosed().subscribe((result) => {
        if (result) {
          this.GetAllPaymentAsync(this.currentPage)
        }
      });
    });
  }
  _formatTotal(total: number) {
    return Math.floor(total * 1000) / 1000
  }
  calculateAllTax() {
    this.taxArray = []
    this.sumtax = 0;
    const resultTax = calculateGroupedTaxes(this.InforInvoice?.itemInvoices)
    this.taxArray = Object.values(resultTax.totalTaxes);
    this.sumtax = resultTax.grandTotalTax
    this.subtotal = this._formatTotal((this.InforInvoice?.totalAmount ?? 0) - this.CheckIsNaN(this.sumtax))

  }
  CheckIsNaN(value: any) {
    if (isNaN(value)) {
      return 0
    }
    return value;

  }

  calculateTotal(item: any) {
    let totalTax = 0
    let rate = item.rate;
    let qty = item.qty;

    // Truncate both rate and qty to 2 decimal places
    let truncatedRate = Math.floor(rate * 100) / 100;
    let truncatedQty = Math.floor(qty * 100) / 100;
    // Calculate total tax
    totalTax = truncatedRate * truncatedQty;

    return this._formatTotal(this.CheckIsNaN(totalTax));

  }
  EditInvoice() {
    this.dataService.isEstimate.set(false)
    const dialogRef = this.editInvoiceDialog.open(this.InforInvoice);

    dialogRef.then((c) => {
      c.afterClosed().subscribe(result => {
        if (result) {
          this.GetInvoiceById(this._id);
        }
      });
    });
  }
  onPageChange(event: any): void {
    if (event?.newProp?.pageSize) {
      this.pageSizesDefault = event.newProp.pageSize
    }
    if (event?.currentPage) {
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: { page: event.currentPage },
        queryParamsHandling: 'merge',
      });
    }

  }
  GetAllPaymentAsync(page: number) {
    let payload: Parameter = {
      Page: page,
      PageSize: this.pageSizesDefault,
      Search: "",
      InvoiceId: this._id

    }
    this._paymentService.GetAllPayment(payload).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.totalPages = res.totalRecords
        this.dataSource = res.data
      }
    }
    )
  }
  onRowSelecting(event: any): void {
    if (event?.data?.length > 0) {
      event?.data.forEach((element: any) => {
        let index = this.listChoosePayment.findIndex(x => x == element?.id)
        if (index < 0) {
          this.listChoosePayment.push(element?.id)
        }
      });

    } else {
      let index = this.listChoosePayment.findIndex(x => x == event?.data?.id)
      if (index < 0) {
        this.listChoosePayment.push(event?.data?.id)
      }
    }

  }
  onRowDeselecting(event: any): void {
    if (event?.data?.length > 0) {
      this.listChoosePayment = [];
    }
    else {

      let index = this.listChoosePayment.findIndex(x => x == event.data?.id)
      if (index >= 0) {
        this.listChoosePayment.splice(index, 1)
      }
    }

  }
  handleArchive() {
    this._invoiceService.UpdateArchive(this._id, true).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
      if (res) {
        this.GetAllPaymentAsync(this.currentPage)
        this._toastService.showSuccess(this.translate.instant("TOAST.Save"), this.translate.instant("TOAST.Success"));
        this.listChoosePayment = [];
      }
      else {
        this._toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
      }
    })
  }
  handleDelete() {
    const _title = this.translate.instant('INVOICES.DeleteInvoice');
    const _description = this.translate.instant('COMMON.ConfirmDelete');

    this.layoutUtilsService.alertDelete({
      title: _title,
      description: _description
    }).then(isConfirm => {
      if (!isConfirm) return

      this._invoiceService.DeleteInvoice([this._id], false).pipe(takeUntilDestroyed(this.destroyRef)).subscribe((res: any) => {
        if (res) {
          this._toastService.showSuccess(this.translate.instant("TOAST.Delete"), this.translate.instant("TOAST.Success"));
          this.router.navigate(["/invoices"]);
        }
        else {
          this._toastService.showError(this.translate.instant("TOAST.Fail"), this.translate.instant("TOAST.Fail"));
        }
      })
    })
  }

  CloseTax($event: boolean) {
    this.actiontMenuTrigger.closeMenu();
  }
  ShareLink() {
    this.shareLinkDialog.open(this._id);
  }
  getFullName(item: any) {
    if (item?.firstName && item?.lastName) {
      return item?.firstName + " " + item?.lastName
    }
    else {
      return item?.email ?? ""
    }

  }
  handleFunctionInDevelopment() {
    this._toastService.showInfo(this.translate.instant("TOAST.TheFeature"))
  }

}
