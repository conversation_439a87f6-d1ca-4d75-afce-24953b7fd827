import { SpinnerService } from 'app/service/spinner.service';
import { CalculationResponse } from 'app/dto/interface/projectResponse.interface';
import { InvoiceSend } from './../dto/interface/InvoiceSend.interface';
import { environment } from 'environments/environment';
import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { GetEstimateQueryParam, GetInvoiceQueryParam, Parameter } from 'app/dto/interface/queryParameter.interface';
import { Invoice } from '../dto/interface/invoice.interface';
import { Observable } from 'rxjs';
import { RequestInvoiceSendMail } from '../dto/interface/requestInvoiceSendMail.interface';
import { PaginatedResponse } from '../dto/interface/paginatedResponse.interface';
import { InvoiceSendToMe } from 'app/dto/interface/InvoiceSendToMe.interface';
import { formatParamsQuery } from 'app/helpers/common.helper';
import { GetRevenueChartDTO } from 'app/dto/interface/GetRevenueChart.interface';
const UrlApi = environment.HOST_API + "/api"
@Injectable({
  providedIn: 'root'
})
export class InvoiceService {


  private http = inject(HttpClient)
  private spinnerService = inject(SpinnerService)

  constructor() { }

  CreatedInvoice(payload: any): Observable<Invoice> {
    return this.http.post<Invoice>(UrlApi + '/Invoices/CreatedInvoice', payload);
  }
  CreatedInvoiceSend(payload: any): Observable<InvoiceSend> {
    return this.http.post<InvoiceSend>(UrlApi + '/Invoices/CreatedInvoiceSend', payload);
  }

  CountInvoiceByCompany(): Observable<number> {
    return this.http.get<number>(UrlApi + `/Invoices/CountInvoiceByCompany`);
  }
  CountInvoiceByContractor(): Observable<number> {
    return this.http.get<number>(UrlApi + `/Invoices/CountInvoiceByContractor`);
  }

  CountEstimate(): Observable<number> {
    return this.http.get<number>(UrlApi + `/Invoices/CountEstimate`);
  }

  SendMailInvoice(payload: any): Observable<RequestInvoiceSendMail> {
    return this.http.post<RequestInvoiceSendMail>(UrlApi + '/Invoices/SendMailInvoice', payload);
  }
  GetAllEstimate(params: GetEstimateQueryParam): Observable<PaginatedResponse<Invoice>> {
    const query = formatParamsQuery(params);
    return this.http.get<PaginatedResponse<Invoice>>(UrlApi + `/Invoices/GetAllEstimate`, { params: query });
  }
  GetAllInvoice(params: GetInvoiceQueryParam): Observable<PaginatedResponse<Invoice>> {
    const query = formatParamsQuery(params);
    return this.http.get<PaginatedResponse<Invoice>>(UrlApi + `/Invoices/GetAllInvoice`, { params: query });
  }
  GetAllInvoiceSendToMe(params: GetInvoiceQueryParam): Observable<PaginatedResponse<InvoiceSendToMe>> {
    const query = formatParamsQuery(params);
    return this.http.get<PaginatedResponse<InvoiceSendToMe>>(UrlApi + `/Invoices/GetAllInvoiceSendToMe`, { params: query });
  }
  GetAllEstimateSendToMe(payload: Parameter): Observable<PaginatedResponse<InvoiceSendToMe>> {
    return this.http.post<PaginatedResponse<InvoiceSendToMe>>(UrlApi + `/Invoices/GetAllEstimateSendToMe`, payload);
  }
  DeleteInvoice(payload: any, isActive: boolean): Observable<object> {
    return this.http.post(UrlApi + `/Invoices/DeleteInvoice?isActive=${isActive}`, payload);
  }
  ChangePosition(payload: any): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + `/Invoices/ChangePosition?`, payload);
  }

  GetRevenueChart(year: Number): Observable<GetRevenueChartDTO> {
    return this.http.get<GetRevenueChartDTO>(UrlApi + `/Invoices/GetRevenueChart?year=${year}`);
  }
  GraphicsChart(year: Number): Observable<any> {
    return this.http.get<any>(UrlApi + `/Invoices/GraphicsChart?year=${year}`);
  }



  MarkAsPaid(id: string): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + `/Invoices/MarkAsPaid?Id=${id}`, null);
  }
  MarkAsSent(id: string): Observable<boolean> {
    return this.http.post<boolean>(UrlApi + `/Invoices/MarkAsSent?Id=${id}`, null);
  }



  UpdateInvoice(payload: any): Observable<Invoice> {
    payload?.itemInvoices.forEach(element => {
      element.user = null
    });
    return this.http.post<Invoice>(UrlApi + '/Invoices/UpdateInvoice', payload);
  }
  UpdateArchive(invoiceId: string, isArchive: boolean): Observable<Object> {
    return this.http.post(UrlApi + `/Invoices/UpdateArchive?invoiceId=${invoiceId}&isArchive=${isArchive}`, {});
  }
  ConvertToInvoice(invoiceId: string): Observable<Object> {
    return this.http.post(UrlApi + `/Invoices/ConvertToInvoice?invoiceId=${invoiceId}`, {});
  }

  GetInvoiceById(InvoiceId: string): Observable<Invoice> {
    return this.http.get<Invoice>(UrlApi + `/Invoices/GetInvoiceById?InvoiceId=${InvoiceId}`);
  }

  PrintInvoiceById(InvoiceId: string, invoiceNumber: string) {
    this.http.get(`${UrlApi}/Invoices/PrintInvoice?InvoiceId=${InvoiceId}`, {
      responseType: 'blob'
    }).subscribe({
      next: (response) => {
        const blob = new Blob([response], { type: 'application/pdf' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `Invoice_${invoiceNumber}.pdf`;
        document.body.appendChild(a);
        a.click();
        this.spinnerService.hide();
        window.URL.revokeObjectURL(url);
      },
      error: (error) => {
        console.error('Error downloading the invoice:', error);
      }
    });
  }

  GetInvoiceByIdLink(InvoiceId: string): Observable<Invoice> {
    return this.http.get<Invoice>(UrlApi + `/Invoices/GetInvoiceByIdLink?InvoiceId=${InvoiceId}`);
  }


  CalculationInvoice(status: number,): Observable<Invoice> {
    return this.http.get<Invoice>(UrlApi + `/Invoices/CalculationInvoice?status=${status}`);
  }
  CalculationInvoiceSendToMe(status: number,): Observable<CalculationResponse> {
    return this.http.get<CalculationResponse>(UrlApi + `/Invoices/CalculationInvoiceSendToMe?status=${status}`);
  }
  CalculationEstimateSendToMe(status: number,): Observable<CalculationResponse> {
    return this.http.get<CalculationResponse>(UrlApi + `/Invoices/CalculationEstimateSendToMe?status=${status}`);
  }


  CalculationEstimate(status: number,): Observable<Invoice> {
    return this.http.get<Invoice>(UrlApi + `/Invoices/CalculationEstimate?status=${status}`);
  }

  uploadFile(file: File): Observable<string> {
    const formData = new FormData();
    formData.append('formFile', file); // 'formFile' matches the parameter name in the backend
    return this.http.post<string>(UrlApi + `/Invoices/UploadFile`, formData);
  }


}
